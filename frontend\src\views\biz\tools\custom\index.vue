<template>
  <div class="index-root">
    <div class="index-aside">
      <el-input
        v-model="filterText"
        style="width: 228px; padding-left: 6px; margin-top: 7px; margin-bottom: 10px"
        :placeholder="t('tools.search.placeholder')"
        clearable
      />
      <el-tree
        ref="treeRef"
        class="doc-tree"
        :data="treeData"
        :highlight-current="true"
        :props="defaultProps"
        :icon="ArrowRightBold"
        default-expand-all
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="changeInfo"
      >
        <template #default="{ data }">
          <span class="custom-tree-node">
            <span>
              {{ data.label }}
            </span>
          </span>
        </template>
      </el-tree>
    </div>
    <div class="index-main-container">
      <Suspense>
        <template #default>
          <div v-if="toolInfo.asideIndex == '1'" class="index-main">
            <XmlFormatter />
          </div>
          <div v-else-if="toolInfo.asideIndex == '2'" class="index-main">
            <JsonViewer />
          </div>
          <div v-else-if="toolInfo.asideIndex == '3'" class="index-main">
            <IntegerBaseConverter />
          </div>
          <div v-else-if="toolInfo.asideIndex == '4'" class="index-main">
            <TemperatureConverter />
          </div>
          <div v-else-if="toolInfo.asideIndex == '5'" class="index-main">
            <Encryption />
          </div>
        </template>
        <template #fallback>
          <SkeletonLoading style="width: 100%; height: 100%" />
        </template>
      </Suspense>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElTree } from "element-plus";
import { defineAsyncComponent, ref, watch } from "vue";
import SkeletonLoading from "@/components/SkeletonLoading.vue";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { useToolsStore } from "@/stores/modules/tools";
import { useI18n } from "vue-i18n";

const toolStore = useToolsStore();
const toolInfo = toolStore.toolInfo;
const i18n = useI18n();
const { t } = useI18n();
interface Tree {
  [key: string]: any;
}

const filterText = ref("");
const treeRef = ref<InstanceType<typeof ElTree>>();
const changeInfo = (menu): void => {
  toolStore.toolInfo.asideIndex = menu.index;
};
const defaultProps = {
  children: "children",
  label: "label"
};

watch(filterText, val => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree): boolean => {
  if (!value) return true;
  return data.label.includes(value);
};

const treeData: Tree[] = [
  {
    id: 0,
    label: i18n.t("tools.categories.title"),
    children: [
      {
        id: 1,
        label: i18n.t("tools.categories.formatting"),
        children: [
          {
            id: 4,
            label: i18n.t("tools.categories.xml"),
            index: "1"
          },
          {
            id: 5,
            label: i18n.t("tools.categories.json"),
            index: "2"
          }
        ]
      },
      {
        id: 2,
        label: i18n.t("tools.categories.conversion"),
        children: [
          {
            id: 6,
            label: i18n.t("tools.categories.radix"),
            index: "3"
          },
          {
            id: 7,
            label: i18n.t("tools.categories.temperature"),
            index: "4"
          }
        ]
      },
      {
        id: 3,
        label: i18n.t("tools.categories.encryption"),
        children: [
          {
            id: 8,
            label: i18n.t("tools.categories.textEncryption"),
            index: "5"
          }
        ]
      }
    ]
  }
];

const XmlFormatter = defineAsyncComponent({
  loader: () => import("@/views/biz/tools/custom/xml-formatter/Xml-formatter.vue"),
  loadingComponent: SkeletonLoading,
  delay: 200
});
const JsonViewer = defineAsyncComponent({
  loader: () => import("@/views/biz/tools/custom/json-viewer/Json-viewer.vue"),
  loadingComponent: SkeletonLoading,
  delay: 200
});
const IntegerBaseConverter = defineAsyncComponent({
  loader: () => import("@/views/biz/tools/custom/integer-base-converter/Integer-base-converter.vue"),
  loadingComponent: SkeletonLoading,
  delay: 200
});
const TemperatureConverter = defineAsyncComponent({
  loader: () => import("@/views/biz/tools/custom/temperature-converter/Temperature-converter.vue"),
  loadingComponent: SkeletonLoading,
  delay: 200
});
const Encryption = defineAsyncComponent({
  loader: () => import("@/views/biz/tools/custom/encryption/Encryption.vue"),
  loadingComponent: SkeletonLoading,
  delay: 200
});
</script>

<style scoped lang="scss">
@import "@/styles/utils";

$zindex-aside: 2001;
$zindex-header: 2001;
.index-root {
  @include box(calc(100% - 2px), calc(100% - 2px));
  @include flex(row, flex-start, center);

  position: relative;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  .index-aside {
    @include box(230px, 100%);

    z-index: $zindex-aside;
    margin-right: 5px;
    margin-left: 5px;
    .doc-tree {
      --el-fill-color-blank: "transparent";

      padding-right: 2px;
      padding-left: 6px;
      .menu-divider {
        height: 15px;
        margin-bottom: 15px;
        margin-left: -20px;
        pointer-events: none;
        border-bottom: 1px solid var(--el-border-color);
      }
      :deep(.menu-item-wrapper) {
        width: 100%;
      }
      :deep(.el-tree-node__children) {
        transition: none; // 关闭折叠展开动画
      }
      :deep(.is-drop-inner) {
        border-radius: 4px;
        box-shadow: inset 0 0 1px 2px var(--el-color-primary);
      }
      :deep(.el-tree__drop-indicator) {
        height: 2px;
      }
      :deep(.is-current) {
        & > .el-tree-node__content {
          border-radius: 5px;
          &:has(.menu-divider) {
            background-color: transparent;
          }
        }
      }
      :deep(.el-tree-node__content) {
        position: relative;
        height: auto;
        &:hover {
          border-radius: 5px;
        }
      }
      :deep(.doc-name) {
        display: flex;
        align-items: center;
        height: 26px;
        cursor: pointer;
      }
    }
  }
  .index-main-container {
    @include box(calc(100% - 260px), 100%);

    position: relative;
    margin-top: 5px;
    margin-left: 10px;
    .app-header {
      @include box(100%, 30px);
    }
    .index-main {
      @include box(100%, calc(100% - 30px));
    }
    .index-header {
      @include box(180px, 35px);
      @include flex(row, center, center);

      position: absolute;
      bottom: 0;
      left: 20px;
      z-index: $zindex-header;
      background-color: var(--bl-html-color);
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }
  .index-aside.simple {
    @include box(40px, 100%);
  }
  .index-main-container.simple {
    @include box(calc(100% - 43px), 100%);
  }
}
</style>
