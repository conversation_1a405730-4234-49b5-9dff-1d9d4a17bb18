import ExcelJS from "exceljs";

export interface ExcelStyle {
  [key: string]: ExcelJS.Style;
}

export function createStyles(): ExcelStyle {
  return {
    header: {
      font: { bold: true, color: { argb: "FFFFFFFF" } },
      fill: {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FF0000FF" },
      },
      alignment: { horizontal: "center", vertical: "middle" }, // 添加对齐方式
      border: {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      },
      numFmt: "General", // 添加数字格式
      protection: { locked: true }, // 添加保护样式
    },
    cell: {
      font: {}, // 添加字体样式
      fill: {
        type: "pattern",
        pattern: "none", // 或者其他合适的模式
        fgColor: { argb: "FFFFFFFF" }, // 默认白色背景
      },
      alignment: { horizontal: "left", vertical: "middle" }, // 添加对齐方式
      border: {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      },
      numFmt: "General", // 添加数字格式
      protection: { locked: true }, // 添加保护样式
    },
  };
}
