<template>
  <div class="p-layout">
    <component :is="currComponent" :key="key"></component>
  </div>
</template>
<script setup lang="ts">
import { inject, ref, Ref, watch } from "vue";
import { EventType, EventTypeParams, EventTypeParamsName, isGroup, isLine } from "../../graph";
import BlankP from "./Blank.vue";
import GraphP from "./Graph.vue";
import NodeP from "./Node.vue";
import GroupP from "./Group.vue";
import PathLineP from "./PathLine.vue";
import { Node } from "@antv/x6";
const nodeMap = new Map<string, any>([
  ["rect", NodeP],
  ["path-line", PathLineP]
]);

const eventTypeParams = inject(EventTypeParamsName) as Ref<EventTypeParams>;
const currComponent = shallowRef(BlankP);
const key = ref(new Date().getTime());
watch(eventTypeParams, () => {
  onPropertiesChanged();
});
const onPropertiesChanged = () => {
  if (!eventTypeParams.value.type || eventTypeParams.value.type == EventType.NONE) {
    return;
  }
  const type = eventTypeParams.value.type;
  switch (type) {
    case EventType.BLANK_CLICK:
      currComponent.value = GraphP;
      break;
    case EventType.NODE_CLICK:
      nodeProperties();
      break;
  }
  key.value = new Date().getTime();
};
const nodeProperties = () => {
  const eventParam = eventTypeParams.value.eventParam;
  if (!eventParam.node) {
    return;
  }
  const node = eventParam.node as unknown as Node;
  if (isGroup(node)) {
    currComponent.value = GroupP;
    return;
  }
  let shap = node.shape;
  if (isLine(eventParam.node)) {
    shap = "path-line";
  }
  const component = nodeMap.get(shap);
  if (component) {
    currComponent.value = component;
  } else {
    currComponent.value = NodeP;
  }
};
</script>
<style>
.p-layout {
  min-width: 200px;
  padding: 0 10px;
  overflow-x: auto;
}
</style>
