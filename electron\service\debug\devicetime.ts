"use strict";

import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";

/**
 * 装置时间Service
 * <AUTHOR>
 * @class
 */
class DeviceTimeService {
  // 获取装置时间，返回字符串类型
  async getDeviceTime(req: IECReq<any>): Promise<string> {
    logger.info(
      `[DeviceTimeService] ${t("logs.deviceTimeService.getDeviceTimeEntry")}:`,
      JSON.stringify(req)
    );
    const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = device?.deviceClient;
    try {
      const response = await client?.getDeviceTime();
      logger.info(
        `[DeviceTimeService] ${t("logs.deviceTimeService.getDeviceTimeReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return response.data.time;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceTimeService] ${t("logs.deviceTimeService.getDeviceTimeError")}:`,
        error
      );
      throw error;
    }
  }

  // 设置装置时间，返回布尔类型
  async writeDeviceTime(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceTimeService] ${t("logs.deviceTimeService.writeDeviceTimeEntry")}:`,
      JSON.stringify(req)
    );
    const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = device?.deviceClient;
    const deviceTime = req.data;
    try {
      const result = await client?.setDeviceTime(deviceTime);
      logger.info(
        `[DeviceTimeService] ${t("logs.deviceTimeService.writeDeviceTimeReturn")}:`,
        result
      );
      if (result?.isSuccess()) {
        return true;
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error(
        `[DeviceTimeService] ${t("logs.deviceTimeService.writeDeviceTimeError")}:`,
        error
      );
      throw error;
    }
  }
}

DeviceTimeService.toString = () => "[class DeviceTimeService]";
const deviceTimeService = new DeviceTimeService();

export { DeviceTimeService, deviceTimeService };
