<template>
  <el-form label-width="100px" class="datetime-container card">
    <el-form-item :label="t('device.time.currentTime')">
      <el-text class="mx-1" type="info">{{ formattedDateTime }}</el-text>
    </el-form-item>
    <el-form-item :label="t('device.time.deviceTime')">
      <div style="display: flex; align-items: center">
        <el-date-picker
          v-model="selectedDate"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="t('device.time.selectDateTime')"
          @change="handleDateChange"
        />
        <el-input
          v-model="milliseconds"
          :placeholder="t('device.time.milliseconds')"
          style="width: 60px; margin-left: 10px"
          @change="handleMillisecondsChange"
        />
      </div>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" plain :icon="Platform" @click="currentDateTime">{{ t("device.time.now") }}</el-button>
      <el-button type="primary" plain :icon="BellFilled" @click="readDeviceTime">{{ t("device.time.read") }}</el-button>
      <el-button type="primary" :icon="CircleCheck" @click="writeDeviceTime">{{ t("device.time.write") }}</el-button>
    </el-form-item>
  </el-form>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { Platform, BellFilled, CircleCheck } from "@element-plus/icons-vue";
import { useDebugStore } from "@/stores/modules/debug";
import { devicetimeApi } from "@/api/modules/biz/debug/devicetime";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { addConsole } = useDebugStore();
const props = defineProps<{ deviceId: string }>();
// 定义一个响应式变量来存储格式化后的日期时间
const formattedDateTime = ref("");
const selectedDate = ref(new Date().toISOString().slice(0, 19));
const milliseconds = ref(new Date().getMilliseconds().toString().padStart(3, "0"));
const progressDialog = ref();

// 函数：格式化日期时间
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

// 每秒更新一次时间（如果需要）
onMounted(() => {
  const updateInterval = setInterval(() => {
    const now = new Date();
    formattedDateTime.value = formatDateTime(now);
  }, 1000);

  // 初始化时立即更新一次
  updateDateTime();

  // 清除定时器，防止内存泄漏
  onUnmounted(() => {
    clearInterval(updateInterval);
  });
});

const currentDateTime = () => {
  updateDateTime();
};

// 函数：更新当前时间
const updateDateTime = () => {
  const now = new Date();
  const current = formatDateTime(now);
  selectedDate.value = current.slice(0, 19);
  milliseconds.value = now.getMilliseconds().toString().padStart(3, "0");
};

// 模拟读取装置时间
const readDeviceTime = async () => {
  progressDialog.value.show();
  try {
    const response = await devicetimeApi.getDeviceTimeByDevice(props.deviceId);
    if (Number(response.code) === 0) {
      const deviceTime = response.data;
      if (typeof deviceTime === "string") {
        const fullDate = new Date(deviceTime.replace(" ", "T") + "Z");
        if (isNaN(fullDate.getTime())) {
          ElMessage({
            message: t("device.time.readFailedInvalidFormat"),
            type: "error"
          });
          addConsole(t("device.time.readFailedInvalidFormat"));
          return;
        }
        selectedDate.value = fullDate.toISOString().slice(0, 19).replace("T", " ");
        milliseconds.value = fullDate.getMilliseconds().toString().padStart(3, "0");
        addConsole(t("device.time.readSuccess"));
        ElMessage({
          message: t("device.time.readSuccess"),
          type: "success"
        });
      } else {
        ElMessage({
          message: t("device.time.readFailedDataError"),
          type: "error"
        });
        addConsole(t("device.time.readFailedDataError"));
      }
    } else {
      ElMessage({
        message: t("device.time.readFailed", { msg: response.msg || t("device.time.unknownError") }),
        type: "error"
      });
      addConsole(t("device.time.readFailed", { msg: response.msg || t("device.time.unknownError") }));
    }
  } finally {
    progressDialog.value.hide();
  }
};

// 模拟写入装置时间
const writeDeviceTime = async () => {
  progressDialog.value.show();
  try {
    const fullDateTime = `${selectedDate.value.replace("T", " ")}.${milliseconds.value}`;
    const fullDate = new Date(fullDateTime);

    const mills = milliseconds.value;
    const ms = typeof mills === "string" ? parseInt(mills, 10) : mills;

    if (isNaN(ms) || ms < 0 || ms > 999) {
      ElMessage({
        message: t("device.time.millisecondsRangeError"),
        type: "error"
      });
      addConsole(t("device.time.millisecondsRangeError"));
      return;
    }
    if (isNaN(fullDate.getTime())) {
      ElMessage({
        message: t("device.time.writeFailedInvalidFormat"),
        type: "error"
      });
      addConsole(t("device.time.writeFailedInvalidFormat"));
      return;
    }

    try {
      const response = await devicetimeApi.writeDeviceTimeByDevice(props.deviceId, fullDateTime);
      if (Number(response.code) === 0) {
        addConsole(t("device.time.writeSuccess"));
        ElMessage({
          message: t("device.time.writeSuccess"),
          type: "success"
        });
      } else {
        ElMessage({
          message: t("device.time.writeFailed", { msg: response.msg || t("device.time.unknownError") }),
          type: "error"
        });
        addConsole(t("device.time.writeFailed", { msg: response.msg || t("device.time.unknownError") }));
      }
    } catch (error) {
      ElMessage({
        message: t("device.time.writeFailed", { msg: error || t("device.time.unknownError") }),
        type: "error"
      });
      addConsole(t("device.time.writeFailed", { msg: error || t("device.time.unknownError") }));
    }
  } finally {
    progressDialog.value.hide();
  }
};

// 处理日期变化
const handleDateChange = (newDate: string) => {
  if (newDate) {
    const fullDate = new Date(`${newDate}.${milliseconds.value}`);
    formattedDateTime.value = formatDateTime(fullDate);
  }
};

// 处理毫秒变化
const handleMillisecondsChange = (newMilliseconds: string) => {
  if (selectedDate.value) {
    const fullDate = new Date(`${selectedDate.value}.${newMilliseconds}`);
    formattedDateTime.value = formatDateTime(fullDate);
  }
};
</script>

<style lang="scss" scoped>
.datetime-container {
  width: 100%;
  padding: 20px;
  margin-top: 5px;

  // margin: 0 auto;
}
.el-form-item {
  margin-bottom: 15px;
}
.el-button {
  margin-right: 10px;
  &:last-child {
    margin-right: 0;
  }
}
p {
  margin: 0;
  font-size: 16px;
}
</style>
