import fs from "node:fs";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";

/**
 * Word导出工具类
 * <AUTHOR>
 */
export class WordUtils {
    
  public static buildWordFile(templatePath: string, data: any, outPath: string): Promise<boolean> {
    const content = fs.readFileSync(templatePath, "binary");
    const zip = new PizZip(content);
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    doc.render(data);

    const buf = doc.getZip().generate({
      type: "nodebuffer",
      compression: "DEFLATE",
    });

    fs.writeFileSync(outPath, buf);
    return fs.existsSync(outPath);
  }
}
