/**
 * @description 接口
 * @license Apache License Version 2.0
 */

import RoleSelector from "./index.vue";

/** 角色选择器属性 */
export interface RoleSelectProps {
  /** 组织树api列表 */
  orgTreeApi: (data?: any) => Promise<any>;
  /** 角色选择api列表 */
  roleSelectorApi: (data?: any) => Promise<any>;
  /** 是否可带数据权限 */
  permission?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 最大角色数 */
  maxCount?: number;
}

/** 角色选择器表格初始化参数 */
export interface RoleSelectTableInitParams {
  /** 组织ID */
  orgId?: number | string | null;
}

/**
 * @description 角色选择器实例类型
 */
export type RoleSelectorInstance = Omit<InstanceType<typeof RoleSelector>, keyof ComponentPublicInstance | keyof RoleSelectProps>;
