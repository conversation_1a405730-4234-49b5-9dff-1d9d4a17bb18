import { calculateRect, calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "1600",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.83,
        y1: 28.33,
        x2: 4.83,
        y2: 35.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.83,
        y1: 0,
        x2: 4.83,
        y2: 10
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(0, 7.67, 9.33, 21.17)
      }
    },
    {
      tagName: "polygon",
      groupSelector: "triangle",
      attrs: {
        points: calculateTriangle(2.33, 10, 4.83, 6.67, "south")
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    },
    triangle: {
      fill: "#000",
      stroke: "#000"
    }
  }
};

export default e;
