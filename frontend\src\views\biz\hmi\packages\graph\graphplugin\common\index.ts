import { Disposable, Graph } from "@antv/x6";
import { CommonImpl, CommonPluginInfo } from "./common";

export class CommonPlugin extends Disposable implements Graph.Plugin {
  public name = "common";
  private dataImpl!: CommonImpl;
  public options: CommonPluginInfo.Options;

  constructor(options: CommonPluginInfo.Options) {
    super();
    this.options = { enabled: true, ...options };
  }

  init(graph: Graph) {
    this.dataImpl = new CommonImpl({
      ...this.options,
      graph
    });
  }

  // #region api

  isEnabled() {
    return !this.dataImpl.disabled;
  }

  enable() {
    this.dataImpl.enable();
  }

  disable() {
    this.dataImpl.disable();
  }
  getData() {
    return this.dataImpl.data;
  }

  getPluginImpl() {
    return this.dataImpl;
  }
  // #endregion

  //@Disposable.dispose()
  dispose() {
    this.dataImpl.dispose();
  }
}
