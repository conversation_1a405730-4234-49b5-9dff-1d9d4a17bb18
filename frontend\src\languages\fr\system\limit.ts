export default {
  limit: {
    module: {
      title: "Nom du module",
      icon: "Icône",
      status: "Statut",
      sort: "Tri",
      description: "Description",
      createTime: "Date de création",
      operation: "Opération",
      add: "Ajouter un module",
      edit: "Modifier le module",
      delete: "Supprimer le module",
      deleteConfirm: "Supprimer les modules sélectionnés",
      deleteConfirmWithName: "Supprimer le module 【{name}】",
      form: {
        title: "Veuillez entrer le nom du module",
        status: "Veuillez sélectionner le statut",
        sort: "Veuillez entrer le tri",
        icon: "Veuillez sélectionner une icône"
      }
    },
    menu: {
      title: "Nom du menu",
      icon: "Icône du menu",
      type: "Type de menu",
      component: "Nom du composant",
      path: "Adresse de route",
      componentPath: "Chemin du composant",
      sort: "Tri",
      status: "Statut",
      description: "Description",
      operation: "Opération",
      add: "Ajouter un menu",
      edit: "Modifier le menu",
      delete: "Supprimer le menu",
      deleteConfirm: "Supprimer les menus sélectionnés",
      deleteConfirmWithName: "Supprimer le menu 【{name}】",
      form: {
        title: "Veuillez entrer le nom du menu",
        parent: "Veuillez sélectionner le menu parent",
        type: "Veuillez sélectionner le type de menu",
        path: "Veuillez entrer l'adresse de route",
        component: "Veuillez entrer l'adresse du composant",
        sort: "Veuillez entrer le tri",
        icon: "Veuillez sélectionner une icône",
        status: "Veuillez sélectionner le statut",
        link: "Veuillez entrer l'adresse de lien"
      }
    },
    button: {
      title: "Nom du bouton",
      code: "Code du bouton",
      sort: "Tri",
      description: "Description",
      operation: "Opération",
      add: "Ajouter un bouton",
      edit: "Modifier le bouton",
      delete: "Supprimer le bouton",
      deleteConfirm: "Supprimer les boutons sélectionnés",
      deleteConfirmWithName: "Supprimer le bouton 【{name}】",
      batch: {
        title: "Ajouter des boutons en lot",
        shortName: "Nom court de permission",
        codePrefix: "Préfixe de code",
        form: {
          shortName: "Veuillez entrer le nom court de permission",
          codePrefix: "Veuillez entrer le préfixe de code"
        }
      },
      form: {
        title: "Veuillez entrer le nom du bouton",
        code: "Veuillez entrer le code du bouton",
        sort: "Veuillez entrer le tri"
      }
    },
    role: {
      title: "Nom du rôle",
      org: "Organisation d'appartenance",
      category: "Type de rôle",
      status: "Statut",
      sort: "Tri",
      description: "Description",
      createTime: "Date de création",
      operation: "Opération",
      add: "Ajouter un rôle",
      edit: "Modifier le rôle",
      delete: "Supprimer le rôle",
      deleteConfirm: "Supprimer les rôles sélectionnés",
      deleteConfirmWithName: "Supprimer le rôle 【{name}】",
      grant: {
        resource: "Ressources d'autorisation",
        permission: "Permissions d'autorisation",
        dataScope: "Portée des données"
      },
      form: {
        title: "Veuillez entrer le nom du rôle",
        org: "Veuillez sélectionner l'organisation d'appartenance",
        category: "Veuillez sélectionner le type de rôle",
        status: "Veuillez sélectionner le statut"
      }
    },
    spa: {
      title: "Nom de la page unique",
      icon: "Icône",
      type: "Type de page unique",
      path: "Adresse de route",
      component: "Chemin du composant",
      sort: "Tri",
      description: "Description",
      createTime: "Date de création",
      operation: "Opération",
      add: "Ajouter une page unique",
      edit: "Modifier la page unique",
      delete: "Supprimer la page unique",
      deleteConfirm: "Supprimer les pages uniques sélectionnées",
      deleteConfirmWithName: "Supprimer la page unique 【{name}】",
      form: {
        title: "Veuillez entrer le nom de la page unique",
        type: "Veuillez sélectionner le type de page unique",
        path: "Veuillez entrer l'adresse de route",
        component: "Veuillez entrer l'adresse du composant",
        sort: "Veuillez entrer le tri",
        icon: "Veuillez sélectionner une icône",
        link: "Veuillez remplir l'adresse de lien, ex: http://www.baidu.com"
      }
    }
  }
};
