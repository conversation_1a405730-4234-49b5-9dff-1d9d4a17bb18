import { calculateArc } from "../../graph/GraphUtil";

const e = {
  shape: "1A00",
  markup: [
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(5, 6.67, 16, 16, -66.93991158951788, 315.9759887326147)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 10.33,
        y1: 15,
        x2: 0,
        y2: 15
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0.33,
        y1: 28,
        x2: 0.33,
        y2: 15
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 12.67,
        y1: 6.83,
        x2: 12.67,
        y2: 0
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 25,
        y1: 28,
        x2: 25,
        y2: 15
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 10.33,
        y1: 22,
        x2: 10.33,
        y2: 15
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 15.33,
        y1: 22,
        x2: 15.33,
        y2: 15
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 25,
        y1: 15,
        x2: 15,
        y2: 15
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    arc: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
