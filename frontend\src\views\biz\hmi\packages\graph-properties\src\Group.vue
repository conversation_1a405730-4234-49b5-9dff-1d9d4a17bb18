<!-- eslint-disable vue/html-closing-bracket-newline -->
<template>
  <el-tabs>
    <el-tab-pane :label="t(`hmi.graphProperties.group.groupProperty`)">
      <el-form :label-width="80">
        <el-collapse v-model="activeName">
          <el-collapse-item :title="t(`hmi.graphProperties.group.basic`)" name="first"
            ><el-form-item :label="t(`hmi.graphProperties.group.width`)">
              <el-input-number v-model="form.width" :min="0" @change="onBorder(Attrs.WIDTH)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.group.height`)">
              <el-input-number v-model="form.height" :min="0" @change="onBorder(Attrs.HEIGHT)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.group.x`)">
              <el-input-number v-model="form.x" :min="0" @change="onBorder(Attrs.X)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.group.y`)">
              <el-input-number v-model="form.y" :min="0" @change="onBorder(Attrs.Y)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.group.angle`)">
              <el-input-number v-model="form.angle" :min="0" :max="360" @change="onBorder(Attrs.ANGLE)"></el-input-number>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { inject, Ref, ref } from "vue";
import { Attrs, EventTypeParams, EventTypeParamsName } from "../../graph/Graph";
import { getNodeAttrValue } from "../../graph/GraphUtil";
import { Graph, Node } from "@antv/x6";
import { DataPlugin } from "../../graph/graphplugin/dataplugin";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const eventTypeParams = inject(EventTypeParamsName) as Ref<EventTypeParams>;
const node = eventTypeParams.value.eventParam.node as Node;
const graph = eventTypeParams.value.eventParam.graph as Graph;
const nodeAttrValue = getNodeAttrValue(node);
const form = ref(nodeAttrValue);
const dataplugin = graph.getPlugin("data") as DataPlugin;
const activeName = ref("first");
const onBorder = (type: Attrs) => {
  switch (type) {
    case Attrs.WIDTH:
      const size = node.getSize();
      size.width = form.value.width;
      node.setSize(size);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.HEIGHT:
      const sizeHeight = node.getSize();
      sizeHeight.height = form.value.height;
      node.setSize(sizeHeight);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.X:
      const position = node.getPosition();
      position.x = form.value.x;
      node.setPosition(position);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.Y:
      const positionY = node.getPosition();
      positionY.y = form.value.y;
      node.setPosition(positionY);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.ANGLE:
      // 记录旋转前的角度
      dataplugin.getData().operator.rotate(node, dataplugin.getData().group);
      node.rotate(form.value.angle, { absolute: true });
      dataplugin.getData().operator.rotating(node, dataplugin.getData().group);
      break;
    default:
      break;
  }
};
</script>
