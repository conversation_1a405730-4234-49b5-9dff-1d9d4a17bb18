# Manual de Usuario

## 1. Descripción General de Funciones

**VisualDebug** es una herramienta de depuración diseñada específicamente para dispositivos de plataforma de visualización Sieyuan (I+D y depuración e ingeniería y gestión), integrando funciones ricas de depuración, configuración, gestión y auxiliares para ayudar a los ingenieros a completar eficientemente varias tareas de depuración.

![Interfaz General de la Herramienta](./help-es/工具整体界面.png)

Las funciones principales incluyen:

- Depuración y gestión de dispositivos
- Herramientas de configuración
- Herramientas IT
- Configuración de herramientas, configuración de temas, cambio de idioma, más funciones

A través de las funciones anteriores, VisualDebug puede satisfacer las necesidades de I+D de proceso completo y depuración de ingeniería, diseño de configuración y gestión diaria de dispositivos de la plataforma de visualización Sieyuan.

### 1.1 Depuración y Gestión de Dispositivos

- Configuración de información de conexión del dispositivo
- Visualización de información básica del dispositivo
- Valores de configuración, valores analógicos, valores de estado, señalización remota, medición remota, control remoto, ajuste remoto, transmisión de salida, informes
- Sincronización de tiempo del dispositivo, depuración de variables, importación y exportación de valores de configuración
- Carga de archivos, descarga de archivos

![Depuración y Gestión de Dispositivos](./help-es/装置调试与管理.png)

### 1.2 Herramientas de Configuración

- Dibujo de gráficos de configuración (vista previa, agregar, editar, símbolos personalizados)
- Asociación de información del dispositivo con elementos gráficos

![Herramientas de Configuración](./help-es/组态工具.png)

### 1.3 Herramientas IT

- Descarga por lotes, importación por lotes de valores de configuración
- Empaquetado de programas
- Formateo XML/JSON
- Conversión de base, conversión de temperatura
- Cifrado y descifrado de texto

![Herramientas IT](./help-es/IT小工具.png)

### 1.4 Configuración de Ingeniería y Configuración del Sistema

- Importación y exportación de configuración de ingeniería
- Configuración del sistema, configuración de parámetros
- Personalización de temas, cambio de múltiples idiomas

![Configuración de Ingeniería y Configuración del Sistema](./help-es/工程配置导入和导出.png)

## 2. Módulos Principales

- **Barra de Menú**: La barra de menú izquierda predeterminada muestra centralmente todas las entradas de funciones, incluyendo depuración, configuración, herramientas, configuración, temas, idiomas, más, etc.
- **Depuración de Dispositivos**: Admite conexión de múltiples dispositivos, conexión en tiempo real para ver el estado del dispositivo, parámetros, variables, sincronización de tiempo del dispositivo, carga de archivos, descarga de archivos, etc.
- **Función de Configuración**: Dibujo de gráficos de configuración (vista previa, agregar, editar, símbolos personalizados), visualización en tiempo real de la información de la interfaz de configuración del dispositivo.
- **Consola**: Visualización en tiempo real de registros del sistema, información de depuración, avisos de error, conveniente para la localización de problemas.
- **Soporte Multiidioma**: Puede cambiar entre chino simplificado, inglés y otros idiomas en la configuración.
- **Cambio de Tema**: Puede cambiar libremente entre temas claros, oscuros y otros para mejorar la experiencia visual.
- **Gestión de Configuración**: Admite importación, exportación, copia de seguridad y recuperación de archivos de configuración de ingeniería, conveniente para migración de proyectos y colaboración.

![Módulos Generales de la Herramienta](./help-es/工具整体模块.png)

## 3. Requisitos del Entorno Operativo

Para asegurar que el software VisualDebug pueda funcionar de manera estable y eficiente, se recomienda usar el siguiente entorno de hardware y software:

- **Sistema Operativo**: Windows 10 y superior (se recomienda 64 bits), admite algunas distribuciones de Linux (como Debian12+).
- **Procesador**: CPU de arquitectura x86 mainstream de doble núcleo y superior.
- **Memoria**: Se recomienda 4GB y superior, se recomienda 8GB y superior para una mejor experiencia.
- **Espacio de Almacenamiento**: Se requiere al menos 500MB de espacio disponible en disco para descompresión y operación.
- **Resolución de Pantalla**: Se recomienda 1366×768 y superior, se recomienda resolución Full HD 1920×1080.
- **Entorno de Red**: Al depurar dispositivos, asegúrese de estar en la misma LAN que el dispositivo objetivo.

- **Nota:**
  - Se recomienda ejecutar el software con privilegios de administrador para evitar problemas de configuración causados por permisos insuficientes.
  - Esta herramienta es software verde, no requiere instalación compleja, puede ejecutarse directamente después de la descompresión, no necesita modificar el registro o variables de entorno del sistema. Todos los datos de usuario y archivos de configuración se guardan en el directorio del software, conveniente para copia de seguridad y migración.

## 4. Activación del Software

El software VisualDebug adopta un mecanismo de autorización de activación local, y la operación de activación debe completarse al usarlo por primera vez. El proceso de activación es el siguiente:

![Activación de Autorización](./help-es/授权激活.png)

1. **Obtener Código de Máquina**

   - Después de iniciar el software, si no está activado, la ventana de activación aparecerá automáticamente, mostrando el código de máquina único de esta máquina.
   - También puede ver el código de máquina en la página "Más" > "Acerca de" de la barra de menú.

2. **Solicitar Código de Activación**

   - Envíe el código de máquina al administrador del software o personal de soporte técnico para solicitar el código de activación.

3. **Ingresar Código de Activación**

   - Ingrese el código de activación recibido en la ventana de activación y haga clic en el botón "Activar".
   - Después de la activación exitosa, el software entrará en estado de uso normal

4. **Notas**
   - El código de activación corresponde al código de máquina uno a uno y solo puede usarse en el dispositivo solicitado. Por defecto, la activación solo se requiere por primera vez.
   - Si cambia de computadora o reinstala el sistema, necesita volver a solicitar el código de activación.
   - La información de activación se guarda en el administrador de credenciales del sistema operativo - credenciales de Windows - credenciales generales, por favor no la elimine arbitrariamente.

![Administrador de Credenciales](./help-es/凭据管理器.png)

> Si encuentra fallas de activación o problemas relacionados con autorización, por favor contacte al soporte técnico para asistencia.

## 5. Funciones de Depuración

### 5.1 Instrucciones de Configuración del Dispositivo

La función de configuración del dispositivo se usa para gestionar y mantener la información de dispositivos que necesitan ser depurados, facilitando la conexión posterior y gestión de parámetros. Las operaciones principales son las siguientes:

1. **Entrar a la Interfaz de Configuración del Dispositivo**

   - Haga clic en "Depurar" > "Lista de Dispositivos" en la barra de menú para entrar a la página de gestión de dispositivos.

2. **Agregar Nuevo Dispositivo**

   - Haga clic en el botón "Agregar Dispositivo", complete el nombre del dispositivo, dirección IP, puerto (predeterminado 58000), si cifrar y otra información.
   - Haga clic en "Expandir Opciones Avanzadas" para configurar el tiempo de espera de conexión (predeterminado 5000 milisegundos), tiempo de espera de solicitud global (predeterminado 30000 milisegundos), tiempo de espera de modificación de configuración (predeterminado 30000 milisegundos)

3. **Editar/Eliminar Dispositivo**

   - En la lista de dispositivos, seleccione el dispositivo correspondiente y haga clic derecho en el botón "Editar" en el menú emergente para modificar la información del dispositivo.
   - Haga clic en el botón "Eliminar" para quitar dispositivos innecesarios. La operación de eliminación debe hacerse con precaución.

4. **Estado del Dispositivo y Conexión**

   - La lista de dispositivos mostrará el estado en línea/fuera de línea de los dispositivos en tiempo real.
   - Admite conexión/desconexión de dispositivos con un clic, el estado de conexión tendrá identificación obvia.

5. **Búsqueda y Filtro de Dispositivos**

   La lista de dispositivos admite función de **filtro de búsqueda**, conveniente para localizar rápidamente dispositivos objetivo:

   - Ingrese cualquier parte del nombre del dispositivo, dirección IP o número de puerto en el cuadro de entrada en la esquina superior derecha de la página de gestión de dispositivos, y la lista filtrará automáticamente los dispositivos coincidentes.
   - Admite búsqueda difusa, no necesita ingresar información completa para localizar.
   - Después de limpiar el cuadro de búsqueda, restaura la visualización de todos los dispositivos.

6. **Función de Expandir/Contraer Dispositivo**

   - Hay botones de expandir/contraer en la posición media de la página de lista de configuración del dispositivo, admitiendo **expandir dispositivo** y **contraer dispositivo**, conveniente para que los usuarios naveguen rápidamente o contraigan la información del dispositivo.

![Configuración del Dispositivo](./help-es/装置配置.png)

> **Consejo:**
>
> - Los datos de configuración del dispositivo se guardarán automáticamente en archivos de configuración locales y se cargarán automáticamente después de reiniciar el software.
> - La configuración del dispositivo puede exportarse regularmente para prevenir pérdida de datos.

### 5.2 Gestión de Información de Grupos

Después de conectarse exitosamente al dispositivo, el software mostrará automáticamente la **información de grupos** del dispositivo, facilitando a los usuarios gestionar varias funciones y datos bajo el dispositivo. La interfaz de información de grupos se muestra en la figura:

![Ejemplo de Interfaz de Información de Grupos](./help-es/分组信息界面示例.png)

1. **Visualización de Información de Grupos**

   - El área izquierda listará automáticamente todos los grupos bajo el dispositivo actual (como valores de configuración, valores de estado, medición remota, señalización remota, control remoto, ajuste remoto, transmisión de salida, informes, etc.), cada grupo contiene elementos de función correspondientes o puntos de datos.
   - La estructura de grupos admite anidamiento multinivel, conveniente para gestión jerárquica.

2. **Búsqueda de Grupos**

   - Se proporciona un **cuadro de búsqueda** arriba de la lista de grupos, donde puede ingresar nombres de grupos o palabras clave para localizar rápidamente grupos objetivo o elementos de función.
   - Admite búsqueda difusa, no necesita ingresar nombres completos para filtrar.

3. **Crear Menú Personalizado**
   - Los usuarios pueden crear menús personalizados según las necesidades reales haciendo clic en el botón "Nuevo Menú", agregando grupos o elementos de función comúnmente usados a menús personalizados para mejorar la eficiencia operativa.
   - Al agregar un nuevo menú personalizado, puede configurar el nombre y descripción del menú. Después de la creación exitosa, la descripción del menú se muestra en el menú de grupos.

![Ejemplo de Interfaz de Crear Menú Personalizado](./help-es/创建自定义菜单界面示例.png)

4. **Crear Informe Personalizado**
   - Admite crear informes personalizados bajo menús personalizados, clasificando y gestionando elementos de función relacionados.
   - Al crear un nuevo informe personalizado, puede personalizar el nombre del informe, descripción, palabras clave, heredar informes, y filtrar datos de informes heredados según palabras clave

![Ejemplo de Interfaz de Crear Informe Personalizado](./help-es/创建自定义报告界面示例.png)

5. **Operaciones de Menú Personalizado**
   - Haga clic derecho en el menú personalizado para realizar operaciones como "Editar Menú", "Eliminar Menú", etc.

![Ejemplo de Interfaz de Operación de Menú Personalizado](./help-es/自定义菜单操作界面示例.png)

> **Consejo:**
>
> - Los menús personalizados e información de grupos se actualizarán automáticamente con la conexión del dispositivo para asegurar que se muestre la estructura de grupos más reciente. Por defecto, la información de grupos creada solo tiene efecto para este dispositivo.
> - Los menús personalizados y grupos solo se guardan localmente y permanecen efectivos después de reiniciar el software.

## 6. Funciones de Configuración

### 6.1 Propósito

Este capítulo describe las instrucciones de operación de la herramienta de configuración, describiendo los pasos del proceso operativo para los usuarios, facilitando a los usuarios entender y usar rápidamente la herramienta de configuración.

### 6.2 Ubicación

Abrir la herramienta, barra de navegación izquierda - [Configuración]

![Interfaz de Ubicación de Función de Configuración](./help-es/组态功能位置界面.png)

### 6.3 Diseño de Interfaz

- Superior izquierda: Mostrar lista de dispositivos y lista de depuración común, las listas de dispositivos en ambos lados reutilizan un dato.
- Inferior izquierda: Lista de configuración, clic derecho admite operaciones como agregar, editar, eliminar, renombrar. Clic izquierdo muestra interfaz de configuración.
- Lado derecho: Mostrar interfaz de configuración

### 6.4 Lista de Dispositivos

La configuración de monitoreo de datos del dispositivo requiere conectar dispositivos, superior izquierda muestra lista de dispositivos.

#### 6.4.1 Agregar

El [+] en la esquina superior derecha puede agregar información del dispositivo.

![Interfaz de Agregar Dispositivo](./help-es/新增装置界面.png)

#### 6.4.2 Operaciones

Seleccionar un dispositivo en la lista de dispositivos, clic derecho para seleccionar operaciones correspondientes. Las operaciones aquí comparten datos con depuración, la eliminación también ajustará la lista de dispositivos en el lado de depuración.

![Interfaz de Operación de Lista de Dispositivos](./help-es/装置列表操作界面.png)

### 6.5 Lista de Configuración

La interfaz inferior izquierda muestra información de configuración en forma de árbol, nodo raíz monitoreo de configuración. Las configuraciones se clasifican por proyecto, por lo que necesita agregar proyectos primero, luego agregar configuraciones bajo proyectos.

#### 6.5.1 Agregar Proyecto

Clic derecho en nodo [Monitoreo de Dispositivos], seleccionar agregar proyecto.
![Interfaz de Agregar Proyecto](./help-es/新增项目界面.png)

Ingresar nombre del proyecto, hacer clic en [Aceptar].
![Interfaz de Agregar Proyecto 2](./help-es/新增项目界面2.png)

Aparece un nuevo nodo de proyecto bajo el nodo de monitoreo de dispositivos.
![Interfaz de Agregar Proyecto 3](./help-es/新增项目界面3.png)

#### 6.5.2 Agregar Configuración

Las configuraciones pertenecen a diferentes proyectos, necesita seleccionar nodo de proyecto, clic derecho seleccionar agregar.
![Interfaz de Agregar Configuración 1](./help-es/新增组态界面1.png)

Ingresar nombre de configuración, hacer clic en [Aceptar], aparece nodo de nombre de configuración bajo nodo de proyecto.
![Interfaz de Agregar Configuración 2](./help-es/新增组态界面2.png)

#### 6.5.3 Editar Configuración

Después de agregar configuración, es una interfaz en blanco, necesita agregar símbolos para convertirse en interfaz de configuración. Clic derecho en [Monitoreo de Datos del Dispositivo], seleccionar [Editar Configuración].
![Interfaz de Editar Configuración 1](./help-es/编辑组态界面1.png)

Las operaciones específicas se refieren al capítulo [Dibujo de Configuración].
![Interfaz de Editar Configuración 2](./help-es/编辑组态界面2.png)

#### 6.5.4 Eliminar Configuración

Seleccionar nodo de configuración bajo proyecto, clic derecho seleccionar [Eliminar].

## 7. Herramientas IT

Este capítulo introduce 6 herramientas IT integradas en el software, ayudando a los usuarios a completar eficientemente conversiones de datos comunes y operaciones auxiliares en escenarios de depuración, procesamiento de datos, gestión de ingeniería.

### 7.1 Descarga por Lotes

**Introducción de Función:**
La herramienta de descarga por lotes integra tres módulos de función principales: configuración de lista de dispositivos, configuración de archivos de descarga, configuración de parámetros de configuración, admite descarga por lotes e importación de archivos y parámetros de configuración de múltiples dispositivos, mejorando enormemente la eficiencia de gestión y archivo de archivos de ingeniería.

**Estructura de Función e Instrucciones de Operación Principales:**

1. **Configuración de Lista de Dispositivos**

   - Muestra todos los dispositivos operables, admite selección por lotes, agregar, eliminar, ordenar y otras operaciones.
   - Puede configurar información de conexión, puerto, si cifrar, si participar en descarga de archivos e importación de configuración y otros parámetros para cada dispositivo.
   - Admite seleccionar todo/deseleccionar todo con un clic, importación y exportación por lotes de información de dispositivos.

2. **Configuración de Archivos de Descarga**

   - Muestra directorios de archivos de dispositivos seleccionados, admite selección por lotes de múltiples archivos, importación, exportación, eliminación, ordenar y otras operaciones.
   - Muestra información detallada como nombre de archivo, tamaño, ruta, última hora de modificación.
   - Admite configurar ruta de guardado local, todos los archivos descargados se guardarán automáticamente en el directorio especificado.
   - Admite importación/exportación de lista de archivos con un clic, conveniente para gestión por lotes de tareas y recuperación.

3. **Configuración de Parámetros de Configuración**
   - Muestra todos los parámetros de configuración de dispositivos, admite filtrado de grupos, búsqueda por palabras clave, selección por lotes, importación, exportación y otras operaciones.
   - Muestra información detallada como nombre de parámetro, descripción, valor mínimo, valor máximo, paso, unidad, valor actual.
   - Admite exportación por lotes de parámetros de configuración, conveniente para archivo y configuración por lotes posterior.

**Escenarios de Aplicación Típicos:**

- Archivo por lotes de archivos de configuración y parámetros de configuración de múltiples dispositivos después de depuración de ingeniería.
- Exportación unificada de respaldo de parámetros de configuración de múltiples dispositivos, conveniente para gestión de proyectos y análisis de datos.
- Recuperación rápida o migración de configuraciones de dispositivos, mejorando la eficiencia de ingeniería.

### 7.2 Función de Empaquetado de Programas

**Introducción de Función:**
La herramienta de empaquetado de programas se usa para comprimir por lotes múltiples archivos locales en archivos zip, conveniente para descarga unificada posterior a directorios especificados del dispositivo, logrando despliegue por lotes, actualización o migración. Admite selección flexible de archivos y carpetas, operación conveniente y eficiente.

![Interfaz de Función de Empaquetado de Programas](./help-es/程序打包界面示例.png)

**Instrucciones de Operación Principales:**

- Hacer clic en el botón "Seleccionar Archivos", aparece ventana de selección de archivos, admite selección múltiple, agregar archivos o carpetas seleccionados a la lista de empaquetado.
- La lista de archivos puede ver información detallada como nombre de archivo, tamaño, ruta, última hora de modificación.
- Admite eliminación de archivo único, limpiar todo, ajustar flexiblemente el contenido de empaquetado.
- Hacer clic en el botón "Empaquetar", comprimir todos los archivos en la lista actual en un archivo zip, nombrado automáticamente.

**Escenarios de Aplicación Típicos:**

- Despliegue por lotes en sitio de ingeniería, actualización de múltiples archivos de programa.
- Empaquetado unificado de múltiples archivos luego descarga al dispositivo, mejorando la eficiencia operativa.
- Cuando se necesita migración general, archivo o respaldo de múltiples archivos relacionados.

### 7.3 Formateo XML

**Introducción de Función:**
La herramienta de formateo XML puede embellecer rápidamente cadenas XML originales en formato claro y fácil de leer, admite sangría personalizada y vista previa de formateo, conveniente para visualización y edición de datos.

![Ejemplo de Función de Formateo XML](./help-es/XML格式化功能示例.png)

**Instrucciones de Operación Principales:**

- Pegar o ingresar contenido XML original en el cuadro de entrada "XML a formatear".
- Puede elegir si colapsar la visualización a través del interruptor "Colapsar Contenido", ajustar "Tamaño de Sangría" para personalizar espacios para cada nivel de sangría.
- Después de hacer clic en el botón de formateo, el área "XML Formateado" debajo muestra automáticamente el contenido XML embellecido.
- Admite copia con un clic de resultados de formateo, conveniente para uso posterior.

**Escenarios de Aplicación Típicos:**

- Ver, editar y embellecer archivos XML como configuración de dispositivos, registros de depuración.
- Procesar datos XML exportados de sistemas de terceros, mejorar la legibilidad de datos y eficiencia de resolución de problemas.

### 7.4 Formateo JSON

**Introducción de Función:**
La herramienta de formateo JSON puede embellecer rápidamente cadenas JSON originales en formato claro y fácil de leer, admite ordenamiento de diccionario, sangría personalizada y vista previa de formateo, conveniente para visualización, depuración y edición de datos.

![Ejemplo de Función de Formateo JSON](./help-es/JSON格式化功能示例.png)

**Instrucciones de Operación Principales:**

- Pegar o ingresar contenido JSON original en el cuadro de entrada "JSON a formatear".
- Puede elegir si ordenar nombres de claves JSON a través del interruptor "Ordenamiento de Diccionario", ajustar "Tamaño de Sangría" para personalizar espacios para cada nivel de sangría.
- El área "JSON Formateado" debajo muestra automáticamente el contenido JSON embellecido.
- Admite copia con un clic de resultados de formateo, conveniente para uso posterior.

**Escenarios de Aplicación Típicos:**

- Ver, editar y embellecer datos JSON como retornos de interfaz, archivos de configuración.
- Procesar contenido JSON exportado de sistemas de terceros, mejorar la legibilidad de datos y eficiencia de resolución de problemas.

### 7.5 Conversión de Base

**Introducción de Función:**
La herramienta de conversión de base admite conversión rápida de valores entre diferentes bases (como binario, octal, decimal, hexadecimal, Base64 y bases personalizadas), adecuada para varios escenarios como depuración de protocolos, análisis de datos.

![Ejemplo de Función de Conversión de Base](./help-es/进制转换功能示例.png)

**Instrucciones de Operación Principales:**

- Ingresar valores de cualquier base (como decimal, hexadecimal, etc.) en el cuadro de entrada "Número a convertir".
- La herramienta convertirá automáticamente los valores ingresados en tiempo real a bases comunes como binario, octal, decimal, hexadecimal, Base64, y mostrará resultados en campos correspondientes.
- Admite conversión de base personalizada, puede configurar base objetivo a través del cuadro de entrada de base personalizada debajo, mostrar automáticamente resultados de conversión.
- Todos los resultados pueden copiarse directamente, conveniente para uso posterior.

**Escenarios de Aplicación Típicos:**

- Depuración de protocolos, análisis de direcciones de registro, conversión de base de contenido de mensajes.
- Conversión múltiple de base y verificación de parámetros de dispositivos, datos de ingeniería.

### 7.6 Conversión de Temperatura

**Introducción de Función:**
La herramienta de conversión de temperatura admite conversión rápida entre múltiples escalas de temperatura comunes y profesionales (como Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur, Rømer), satisfaciendo necesidades de ingeniería, investigación científica y otros escenarios.

![Ejemplo de Función de Conversión de Temperatura](./help-es/温度转换功能示例.png)

**Instrucciones de Operación Principales:**

- Ingresar valores de temperatura en cualquier cuadro de entrada de escala de temperatura, la herramienta calculará automáticamente y mostrará sincrónicamente resultados de conversión de todas las otras escalas de temperatura.
- Admite conversión entre escalas de temperatura comunes (K, ℃, ℉, °R) y escalas de temperatura profesionales (°De, °N, °Ré, °Rø).
- Todos los resultados pueden copiarse directamente, conveniente para uso posterior.

**Escenarios de Aplicación Típicos:**

- Depuración y verificación de parámetros de temperatura de dispositivos.
- Conversión de unidades de múltiples escalas de temperatura en sitios de ingeniería, experimentos científicos.

### 7.7 Cifrado y Descifrado de Texto

**Introducción de Función:**
La herramienta de cifrado y descifrado de texto admite múltiples algoritmos de cifrado principales (como AES, TripleDES, Rabbit, RC4, etc.), puede cifrar y descifrar rápidamente datos de texto, adecuada para protección de información sensible, soporte técnico y otros escenarios.

**Algoritmos Admitidos:**
Actualmente la herramienta admite los siguientes algoritmos de cifrado principales, los usuarios pueden elegir según necesidades reales:

- **AES** (Estándar de Cifrado Avanzado)
- **TripleDES** (Estándar de Cifrado de Datos Triple)
- **Rabbit** (Algoritmo de Cifrado de Flujo)
- **RC4** (Algoritmo de Cifrado de Flujo)

![Ejemplo de Función de Cifrado y Descifrado de Texto](./help-es/文本加解密功能示例.png)

**Instrucciones de Operación Principales:**

- Cifrado: Ingresar contenido de texto plano en el cuadro de entrada "Texto a cifrar", llenar clave, seleccionar algoritmo de cifrado, el sistema genera automáticamente texto cifrado encriptado.
- Descifrado: Ingresar contenido de texto cifrado en el cuadro de entrada "Texto a descifrar", llenar clave, seleccionar algoritmo de descifrado, el sistema restaura automáticamente contenido de texto plano.
- Admite selección de múltiples algoritmos de cifrado y descifrado, el área de resultados puede copiarse directamente, conveniente para uso posterior.

**Escenarios de Aplicación Típicos:**

- Almacenamiento y transmisión cifrados de parámetros sensibles, archivos de configuración.
- Descifrado rápido y verificación de información cifrada durante soporte técnico, resolución de problemas.

## 8. Más Funciones

Hacer clic en el botón "Más" en la parte inferior de la barra de menú izquierda para acceder rápidamente a las siguientes funciones auxiliares, mejorando la usabilidad y extensibilidad del software:

![Descripción de Ejemplo de Más Funciones](./help-es/更多功能示例说明.png)

### 8.1 Importar Configuración de Ingeniería

Admite importar archivos de configuración de herramientas exportados al entorno de software de herramientas actual. Puede seleccionar tipo de configuración (como todo, parcial), y especificar ruta de archivo de importación a través del selector de archivos. Después de la importación, las configuraciones relacionadas se aplicarán automáticamente al proyecto actual, conveniente para migración de proyectos y configuración por lotes.

**Pasos de Operación:**

1. Hacer clic en la opción "Importar Configuración de Ingeniería" bajo el menú "Más"
2. Seleccionar archivo de configuración a importar en el selector de archivos emergente
3. Seleccionar tipo de importación (toda la configuración o configuración parcial)
4. Confirmar importación, el sistema aplicará automáticamente la configuración al entorno actual

**Notas:**

- Se recomienda hacer respaldo de la configuración actual antes de importar para evitar pérdida de datos
- Por favor no cierre el software o realice otras operaciones durante la importación
- Si la importación falla, por favor verifique el formato del archivo y la integridad del contenido

![Ejemplo de Función de Importar Configuración de Ingeniería](./help-es/导入工程配置功能示例.png)

### 8.2 Exportar Configuración de Ingeniería

Admite exportar toda o parte de la configuración de ingeniería actual como archivos, conveniente para respaldo, archivo o migración a otros entornos. Puede seleccionar directorio de exportación, los archivos de configuración generados después de la exportación pueden usarse para operaciones de importación posteriores.

**Pasos de Operación:**

1. Hacer clic en la opción "Exportar Configuración de Ingeniería" bajo el menú "Más"
2. Seleccionar tipo de exportación (toda la configuración o configuración parcial)
3. Seleccionar directorio de exportación
4. Confirmar exportación, el sistema generará archivos de configuración

**Contenido de Exportación:**

- Todo
- Lista de Dispositivos
- Lista de Configuración

**Escenarios de Uso:**

- Respaldo y archivo de proyectos
- Migración de configuración a otros entornos
- Compartir configuración de colaboración en equipo
- Resolución de problemas de soporte técnico

![Ejemplo de Función de Exportar Configuración de Ingeniería](./help-es/导出工程配置功能示例.png)

### 8.3 Función de Captura de Pantalla

Captura con un clic de la interfaz de software actual, conveniente para guardar proceso de depuración, retroalimentación de problemas o soporte técnico. Los archivos de captura de pantalla pueden copiarse al portapapeles o guardarse en directorio especificado, admite visualización y compartir posteriores.

**Características de Función:**

- Admite captura de pantalla completa y captura de área
- Admite copiar al portapapeles o guardar en directorio especificado

**Pasos de Operación:**

1. Hacer clic en la opción "Captura de Pantalla" bajo el menú "Más"
2. Seleccionar modo de captura de pantalla (pantalla completa o área)
3. Si se selecciona captura de área, arrastrar para seleccionar área de captura de pantalla

**Escenarios de Aplicación:**

- Registro de proceso de depuración
- Retroalimentación de problemas y soporte técnico
- Documentación de pasos de operación
- Referencia de diseño de interfaz

**Notas:**

- Por favor asegúrese de que la visualización de la interfaz esté completa antes de la captura de pantalla
- Se recomienda ocultar información sensible al tomar capturas de pantalla
- Los archivos de captura de pantalla ocuparán espacio en disco, se recomienda limpieza regular

### 8.4 Búsqueda de Menú

Admite búsqueda rápida por nombre de menú o ruta, conveniente para localizar rápidamente entradas de función requeridas cuando hay muchas funciones. Después de ingresar palabras clave, el sistema filtra automáticamente elementos de menú coincidentes, mejorando la eficiencia operativa.

**Funciones de Búsqueda:**

- Admite búsqueda difusa y búsqueda exacta
- Visualización en tiempo real de resultados de búsqueda
- Visualización resaltada de resultados de búsqueda

**Pasos de Operación:**

1. Hacer clic en la opción "Búsqueda de Menú" bajo el menú "Más"
2. Ingresar palabras clave en el cuadro de búsqueda
3. El sistema muestra automáticamente elementos de menú coincidentes
4. Hacer clic en resultados de búsqueda para saltar directamente a la función correspondiente

![Ejemplo de Función de Búsqueda de Menú](./help-es/菜单搜索功能示例.png)

### 8.5 Documentación de Ayuda

Abrir esta documentación de ayuda, ver descripciones de funciones del software, guías de operación, preguntas frecuentes y otro contenido, conveniente para que principiantes se inicien rápidamente y encuentren soluciones de autoayuda cuando encuentren problemas.

**Contenido de Documentación:**

- Introducción y descripción general de funciones del software
- Instrucciones detalladas de pasos de operación
- Preguntas Frecuentes (FAQ)
- Guía de resolución de problemas
- Recomendaciones de mejores prácticas

**Métodos de Uso:**

- Visualización en línea: Hacer clic en la opción "Ayuda" bajo el menú "Más"
- Visualización sin conexión: La documentación de ayuda está integrada en el software
- Función de búsqueda: Admite búsqueda por palabras clave para localización rápida
- Navegación de directorio: Salto rápido a capítulos especificados a través del directorio

**Usuarios Aplicables:**

- Inicio rápido de nuevos usuarios
- Referencia de uso diario
- Resolución y resolución de problemas
- Aprendizaje e investigación en profundidad de funciones

![Ejemplo de Función de Ayuda](./help-es/帮助功能示例.png)

### 8.6 Información Acerca de

Ver información básica del software, incluyendo nombre de herramienta, número de versión, código de máquina, etc. Puede usarse para registro de software, soporte técnico y gestión de versiones.

**Información Mostrada:**

- Nombre del software y número de versión
- Información de derechos de autor e información de la empresa
- Código de máquina (usado para activación del software)
- Tiempo de construcción e identificación de versión

**Usos Principales:**

- Confirmación de versión del software
- Solicitud de código de activación (requiere código de máquina)

**Instrucciones de Operación:**

1. Hacer clic en la opción "Acerca de" bajo el menú "Más"
2. Ver información detallada del software
3. Copiar código de máquina para solicitud de activación

**Notas:**

- El código de máquina es identificador único para activación del software
- Por favor mantenga adecuadamente la información del código de máquina
- La información de versión ayuda en la resolución de problemas

![Ejemplo de Función Acerca de](./help-es/关于功能示例.png)

> **Consejo:**
>
> - Las funciones bajo el menú "Más" son herramientas auxiliares, dirigidas a mejorar la flexibilidad del software y la experiencia del usuario.
> - Se recomienda realizar operaciones de importación y exportación regularmente para prevenir pérdida de datos.
> - Si ocurren anormalidades de función o preguntas de operación, por favor consulte la documentación de ayuda o contacte al soporte técnico.
> - Se recomienda respaldo regular de configuraciones importantes y archivos de captura de pantalla.
> - El uso razonable de la función de búsqueda puede mejorar significativamente la eficiencia operativa.

## 9. Preguntas Frecuentes (FAQ)

- **P: ¿El menú de grupos se muestra incorrectamente después de conectar el dispositivo?**
  - Verificar si urpc en el directorio shr del dispositivo está actualizado a la versión compatible con la herramienta.
  - Leer debug_info.xml en el directorio shr y subirlo localmente, contactar soporte técnico para localización de problemas.
- **P: ¿No se puede conectar el dispositivo?**
  - Verificar alimentación del dispositivo y conexión de red, asegurar estar en la misma LAN que la computadora.
  - Verificar configuración de firewall, asegurar que el software tenga permiso de acceso a la red.
- **P: ¿Falló la importación de configuración?**
  - Por favor confirme que el formato del archivo de configuración importado es correcto y compatible con **VisualDebug**.
- **P: ¿No se pueden importar archivos Excel cifrados?**
  - Debido al impacto de políticas IT, Excel cifrado no puede reconocerse. Si necesita importar archivos Excel cifrados, por favor descifre primero.
- **P: ¿Visualización anormal de la interfaz del software?**
  - Intente cambiar temas o reiniciar el software, si el problema persiste por favor contacte al soporte técnico.
- **P: ¿Los valores de configuración con diferencias no se muestran después de la importación de configuración?**
  - Por favor confirme si los nombres de grupos de configuración en el archivo Excel/xml/csv de configuración importado son completamente consistentes con los nombres de grupos en el menú de grupos de la herramienta. La inconsistencia no puede comparar diferencias.
- **P: ¿A qué directorio del dispositivo se deben descargar los archivos empaquetados de la función de empaquetado de programas?**
  - El zip empaquetado está en formato cifrado, necesita descargarse al directorio /dwld del dispositivo, se requiere reinicio después de completar la descarga para que tome efecto.
- **P: ¿Por qué no toma efecto el reinicio cuando se marca reinicio al completar descarga en la interfaz de descarga de archivos?**
  ![Fallo de Reinicio](./help-es/重启失败.png)
  - El reinicio tiene requisitos para el firmware de la placa CPU. Si ls -l /sbin/reboot muestra enlace suave como en la imagen superior, reboot no es compatible, se requiere actualización de firmware.

## 10. Soporte Técnico

Si encuentra problemas que no pueden resolverse, por favor contáctenos a través de los siguientes métodos:

- **Empresa**: Sieyuan Electric Co., Ltd.
- **Departamento**: Instituto de Investigación Central - Departamento de Desarrollo de Aplicaciones Embebidas
- **Grupo Profesional**: Grupo de Desarrollo de Software de Herramientas

¡Gracias por usar este software, le deseamos una depuración exitosa!
