export default {
  search: {
    placeholder: "搜索设备",
    button: "搜索",
    success: "搜索成功"
  },
  device2: {
    search: {
      placeholder: "搜索装置",
      add: "添加装置",
      duplicate: "该IP和端口已存在，请勿重复添加"
    },
    list: {
      empty: "没有找到设备",
      unnamed: "未命名装置",
      status: {
        connected: "连接",
        disconnected: "断开"
      },
      contextMenu: {
        connect: "连接",
        edit: "编辑",
        disconnect: "断开",
        remove: "删除"
      },
      message: {
        disconnectFirst: "请先断开连接再编辑",
        disconnectFirstDelete: "请先断开连接再删除",
        connectSuccess: "装置{name}：连接成功",
        connectExists: "装置{name}：连接已存在",
        connectFailed: "装置{name}：连接失败",
        connectFailedReason: "装置连接失败原因：{reason}",
        disconnected: "装置{name}：已断开",
        operationFailed: "装置{name}：操作失败"
      }
    },
    report: {
      group: {
        openWaveConfirm: "是否通过第三方工具打开波形文件?",
        tips: "温馨提示",
        noWaveTool: "未配置第三方波形工具路径"
      },
      common: {
        selectRow: "请选中要操作的行"
      }
    },
    backup: {
      savePath: "保存路径",
      setPath: "请设置备份路径",
      setPathTitle: "设置备份路径",
      locateFolder: "定位文件夹",
      startBackup: "开始备份",
      cancelBackup: "取消备份",
      backup: "备份",
      sequence: "序号",
      backupType: "备份类型",
      backupDesc: "备份说明",
      progress: "进度",
      status: "状态",
      noTypeSelected: "请选择备份类型",
      backupSuccess: "备份成功",
      backupFailed: "备份失败",
      openFolderFailed: "打开文件夹失败",
      backupTypes: {
        paramValue: "参数值",
        faultInfo: "故障信息",
        cidConfigPrjLog: "CID配置工程日志",
        waveReport: "波形报告"
      },
      backupDescTypes: {
        paramValue: "备份装置的所有参数设定值",
        faultInfo: "备份装置的故障录波信息",
        cidConfigPrjLog: "备份CID配置文件和工程日志",
        waveReport: "备份波形分析报告文件"
      },
      backupStatus: {
        userCancelled: "用户取消",
        transferring: "传输中"
      },
      console: {
        pathNotSet: "未设置备份路径，无法开始备份",
        noTypeSelected: "未选择备份类型，无法开始备份",
        startBackup: "开始备份，类型：{types}, 路径：{path}",
        backupException: "备份异常：{error}",
        pathSelected: "已选择备份路径：{path}",
        pathNotSelected: "未选择备份路径",
        pathNotSetForLocate: "未设置备份路径，无法定位文件夹",
        folderOpened: "已打开备份文件夹：{path}",
        openFolderFailed: "打开备份文件夹失败：{error}",
        taskCompleted: "任务处理完成",
        taskCancelled: "任务已取消",
        typeError: "类型[{type}]发生错误：{error}",
        typeCompleted: "类型[{type}]备份完成",
        typeCancelled: "类型[{type}]已取消",
        typeFailed: "类型[{type}]失败"
      }
    },
    remoteControl: {
      directControl: "直控",
      selectControl: "选控"
    },
    messageMonitor: {
      title: "报文监视",
      start: "开始监视",
      stop: "停止监视",
      clear: "清空",
      export: "导出",
      expand: "展开",
      collapse: "折叠",
      close: "关闭",
      messageType: "报文",
      noMessages: "暂无报文数据",
      noMessagesToExport: "没有可导出的报文数据",
      startSuccess: "开始监视报文",
      stopSuccess: "停止监视报文",
      clearSuccess: "清空报文成功",
      exportSuccess: "导出报文成功",
      exportFailed: "导出报文失败",
      toggleFailed: "切换监视状态失败"
    }
  }
};
