import { Graph, Node } from "@antv/x6";
import { Selection } from "@antv/x6-plugin-selection";
//import { Transform } from "@antv/x6-plugin-transform";
import { Dnd } from "@antv/x6-plugin-dnd";
import { History } from "@antv/x6-plugin-history";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Export } from "@antv/x6-plugin-export";
import { DataPlugin } from "./graphplugin/dataplugin";
import { DataInfo } from "./Graph";
import { CommonPlugin } from "./graphplugin";
import { Transform } from "./graphplugin";
class GraphUsePlugin {
  graph: Graph;
  dnd!: Dnd;
  constructor(graph: Graph) {
    this.graph = graph;
  }
  use(data: DataInfo) {
    const graph = this.graph;
    graph.use(
      new Transform({
        resizing: {
          enabled: true,
          orthogonal: true
        },
        rotating: {
          enabled: true,
          grid: 1
        }
      })
    );
    graph.use(
      new Snapline({
        enabled: true
      })
    );
    this.dnd = new Dnd({
      target: graph,
      scaled: false,
      getDropNode(draggingNode, options) {
        const data = options.sourceNode.getData();
        if (data && data.dndMock === true && data.dndCells) {
          // 返回group节点
          const cloneNode = (data.dndCells[0] as Node).clone();
          cloneNode.setData({ dndCells: data.dndCells });
          // 设置位置为拖拽点的位置
          cloneNode.setPosition(draggingNode.getPosition());
          return cloneNode;
        }
        return draggingNode.clone();
      }
    });

    graph.use(
      new History({
        enabled: true
      })
    );
    graph.use(
      new Keyboard({
        enabled: true
      })
    );
    graph.use(
      new Clipboard({
        enabled: true
      })
    );
    graph.use(
      new Selection({
        enabled: true,
        showNodeSelectionBox: true,
        showEdgeSelectionBox: true,
        rubberband: true,
        strict: true
      })
    );
    graph.use(new Export());
    graph.use(new DataPlugin({ data: data }));
    graph.use(new CommonPlugin({ enabled: true, data: {} }));
  }
}

export default GraphUsePlugin;
