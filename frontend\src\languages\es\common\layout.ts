export default {
  language: {
    title: "Idioma",
    zh: "Chino",
    en: "Inglés",
    es: "Español",
    fr: "<PERSON>an<PERSON><PERSON>",
    tooltip: "Idioma"
  },
  about: {
    title: "Acerca de",
    introduction: "Introducción",
    description:
      "Una herramienta de depuración de plataforma de última generación basada en Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc. desarrollado con las últimas tecnologías.",
    versionInfo: "Información de Versión",
    toolName: "Nombre de la Herramienta",
    version: "Número de Versión",
    machineCode: "Código de Máquina",
    loading: "Cargando...",
    machineCodeError: "Error al obtener",
    copySuccess: "Código de máquina copiado al portapapeles",
    copyError: "Error al copiar",
    versionFeatures: "Características de la Versión",
    features: {
      visualTool:
        "Incluye herramientas de visualización, ver información de dispositivo, configurar, simular, estado, señal remota, medir, controlar, reportar, sincronizar dispositivo, importar y exportar valor, función de depuración de variable",
      configTool:
        "Incluye herramientas de configuración para vista previa, agregar, editar, símbolo personalizado, función de asociación de información de dispositivo",
      themeTool: "Incluye personalización de tema, herramienta pequeña, función de importar y exportar configuración"
    }
  },
  footer: {
    copyright: "{version}"
  },
  header: {
    minimize: "Minimizar",
    maximize: "Maximizar",
    restore: "Restaurar",
    close: "Cerrar",
    company: {
      name: "Sieyuan Electric",
      englishName: "Sieyuan"
    },
    collapse: {
      expand: "Expandir Dispositivo",
      fold: "Colapsar Dispositivo",
      expandTool: "Expandir Lista de Herramientas",
      foldTool: "Colapsar Lista de Herramientas"
    },
    breadcrumb: {
      home: "Inicio"
    },
    assemblySize: {
      title: "Configuración de Tamaño",
      default: "Predeterminado",
      large: "Grande",
      small: "Pequeño"
    },
    avatar: {
      profile: "Perfil",
      switchApp: "Cambiar Aplicación",
      logout: "Cerrar Sesión",
      logoutConfirm: {
        title: "Aviso",
        message: "¿Está seguro de que desea cerrar sesión?",
        confirm: "Confirmar",
        cancel: "Cancelar"
      },
      logoutSuccess: "¡Cierre de sesión exitoso!"
    },
    changeModule: {
      title: "Cambiar Módulo"
    },
    enginConfig: {
      configType: "Tipo de Configuración",
      openDirectory: "Abrir Directorio de Archivos",
      cancel: "Cancelar",
      confirm: "Confirmar",
      all: "Todo",
      deviceList: "Lista de Dispositivos",
      configureList: "Lista de Configuración",
      exportSuccess: "Exportación de configuración exitosa",
      importSuccess: "Importación de configuración exitosa",
      disconnectDeviceFirst: "Por favor, desconecte primero el dispositivo conectado",
      overrideConfirm: "La lista de configuración ya existe, ¿desea sobrescribir?",
      warmTips: "Aviso",
      importConfigFile: "Importar archivo de configuración"
    },
    userInfo: {
      title: "Información del Usuario",
      cancel: "Cancelar",
      confirm: "Confirmar"
    },
    password: {
      title: "Cambiar Contraseña",
      cancel: "Cancelar",
      confirm: "Confirmar"
    },
    globalSetting: {
      title: "Config",
      tooltip: "Config"
    },
    moreInfo: {
      title: "Más",
      tooltip: "Más",
      items: {
        importConfig: "Importar Config",
        printScreen: "Captura",
        search: "Buscar Menú",
        exportConfig: "Exportar Config",
        about: "Acerca de",
        help: "Ayuda"
      },
      importConfig: {
        title: "Importar Configuración del Proyecto",
        placeholder: "Por favor, seleccione la ruta del archivo de configuración a importar"
      },
      exportConfig: {
        title: "Exportar Configuración del Proyecto",
        placeholder: "Por favor, seleccione el directorio de exportación"
      }
    },
    searchMenu: {
      placeholder: "Buscar en el menú: admite nombre y ruta del menú",
      empty: "Sin menú"
    },
    theme: {
      title: "Tema",
      tooltip: "Tema"
    }
  },
  main: {
    maximize: {
      exit: "Salir de Maximizar"
    }
  },
  theme: {
    title: "Configuración de Diseño",
    quickTheme: {
      title: "Configuración de Tema"
    },
    layoutSettings: {
      title: "Configuración de Diseño"
    },
    layout: {
      title: "Estilo de Diseño",
      columns: "Columnas",
      classic: "Clásico",
      transverse: "Transversal",
      vertical: "Vertical"
    },
    global: {
      title: "Tema Global",
      primary: "Color del Tema",
      dark: "Modo Oscuro",
      grey: "Modo Gris",
      weak: "Modo Débil",
      special: "Modo Especial"
    },
    mode: {
      light: "Claro",
      dark: "Oscuro"
    },
    interface: {
      title: "Configuración de Interfaz",
      watermark: "Marca de Agua",
      breadcrumb: "Miga de Pan",
      breadcrumbIcon: "Icono de Miga de Pan",
      tabs: "Barra de Pestañas",
      tabsIcon: "Icono de Barra de Pestañas",
      footer: "Pie de Página",
      drawerForm: "Formulario de Cajón"
    },
    presetThemes: {
      title: "Temas Preestablecidos",
      default: {
        name: "Tema Predeterminado",
        description: "Tema azul clásico"
      },
      dark: {
        name: "Tema Oscuro",
        description: "Modo oscuro para protección ocular"
      },
      techBlue: {
        name: "Azul Tech",
        description: "Azul tecnológico moderno"
      },
      deepBlue: {
        name: "Azul Profundo",
        description: "Azul océano profundo"
      },
      nature: {
        name: "Tema Natural",
        description: "Verde fresco"
      },
      forestGreen: {
        name: "Verde Bosque",
        description: "Verde bosque profundo"
      },
      warm: {
        name: "Tema Cálido",
        description: "Naranja cálido"
      },
      sunsetOrange: {
        name: "Naranja Atardecer",
        description: "Naranja atardecer cálido"
      },
      elegant: {
        name: "Tema Elegante",
        description: "Púrpura noble"
      },
      lavender: {
        name: "Lavanda",
        description: "Morado lavanda suave"
      },
      sakura: {
        name: "Rosa Sakura",
        description: "Rosa sakura romántico"
      },
      rose: {
        name: "Rojo Rosa",
        description: "Rojo rosa apasionado"
      },
      lime: {
        name: "Verde Lima",
        description: "Verde lima vibrante"
      },
      skyBlue: {
        name: "Azul Cielo",
        description: "Azul cielo claro"
      },
      eyeCare: {
        name: "Modo de Cuidado Ocular",
        description: "Tema gris para cuidado ocular"
      }
    },
    colors: {
      techBlue: {
        name: "Azul Tecnológico",
        description: "Sensación tecnológica moderna"
      },
      natureGreen: {
        name: "Verde Natural",
        description: "Fresco y natural"
      },
      vibrantOrange: {
        name: "Naranja Vibrante",
        description: "Cálido y vibrante"
      },
      elegantPurple: {
        name: "Púrpura Elegante",
        description: "Noble y elegante"
      },
      romanticPink: {
        name: "Rosa Romántico",
        description: "Suave y romántico"
      },
      freshCyan: {
        name: "Cian Fresco",
        description: "Fresco y elegante"
      },
      brightYellow: {
        name: "Amarillo Brillante",
        description: "Brillante y animado"
      },
      warmOrange: {
        name: "Naranja Cálido",
        description: "Cálido y cómodo"
      },
      limeGreen: {
        name: "Verde Lima",
        description: "Lima fresca"
      },
      deepBlue: {
        name: "Azul Profundo",
        description: "Profundo y estable"
      },
      golden: {
        name: "Dorado",
        description: "Oro clásico"
      },
      chinaRed: {
        name: "Rojo Chino",
        description: "Rojo tradicional"
      }
    }
  },
  tabs: {
    moreButton: {
      refresh: "Actualizar",
      closeCurrent: "Cerrar Actual",
      closeLeft: "Cerrar Izquierda",
      closeRight: "Cerrar Derecha",
      closeOthers: "Cerrar Otros",
      closeAll: "Cerrar Todo"
    }
  }
};
