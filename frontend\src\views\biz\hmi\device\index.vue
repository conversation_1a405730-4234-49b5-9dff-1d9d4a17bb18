<template>
  <div class="content-box">
    <!-- 左侧 -->
    <div class="configure-left" ref="deviceLeft" :style="configureLeftStyle.showDeviceList">
      <!-- 装置列表部分 -->
      <DeviceSearch></DeviceSearch>
      <DeviceList></DeviceList>
      <!-- 装置关联组态列表部分 -->
      <ConfigureList></ConfigureList>
    </div>
    <!-- 分隔线 -->
    <div ref="resizeVerticalRef" class="resize-vertical-no-border">
      <!-- 折叠按钮 - 垂直居中 -->
      <div class="collapse-button-container">
        <DeviceListCollapse></DeviceListCollapse>
      </div>
    </div>
    <!-- 右侧 -->
    <div class="configure-right" ref="deviceRight">
      <Configures></Configures>
    </div>
  </div>
</template>

<script setup lang="ts" name="treeFilter">
import DeviceSearch from "@/views/biz/hmi/device/components/DeviceSearch.vue";
import DeviceList from "@/views/biz/hmi/device/components/DeviceList.vue";
import ConfigureList from "@/views/biz/hmi/device/components/ConfigureList.vue";
import DeviceListCollapse from "@/views/biz/hmi/device/components/DeviceListCollapse.vue";
import { useResizeVertical } from "@/scripts/resizeVertical";
import { useGlobalStore } from "@/stores/modules";
import Configures from "@/views/biz/hmi/device/components/Configures.vue";
const deviceLeft = ref();
const resizeVerticalRef = ref();
const deviceRight = ref();
const globalStore = useGlobalStore();
const configureLeftStyle = computed<any>(() => {
  let value = "none";
  const show = globalStore.isDeviceList;
  let offset = 51;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  if (show == true) {
    value = "flex";
  }
  return { showDeviceList: { display: `${value}`, height: `calc(100vh  - ${offset}px)` } };
});
useResizeVertical(deviceLeft, deviceRight, resizeVerticalRef, undefined, {
  persistent: true,
  keyOne: "configureLeft",
  keyTwo: "configureRight",
  defaultOne: "240",
  defaultTwo: "",
  maxOne: 340,
  minOne: 240
});
</script>

<style scoped lang="scss">
@import "./index";
@import "@/styles/resize";
</style>
