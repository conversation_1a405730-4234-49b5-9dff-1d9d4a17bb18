/**
 * @description  props类型定义
 * @license Apache License Version 2.0
 */

/** 表单props属性 */
declare namespace FormProps {
  // 表单基础props属性
  interface Base<T> {
    /** 操作类型 */
    opt: string;
    /** id数据 */
    id?: string | number;
    /** 当前行数据 */
    record: Partial<T>;
    /** 是否禁止操作 */
    disabled?: boolean;
    /** 行内表单模式 */
    inline?: boolean;
    /** 表单布局 */
    successful?: () => void;
  }
}
