export const transformers = {
  number: {
    fromQuery: (value: string): any => Number(value),
    toQuery: (value: number): any => String(value)
  },
  string: {
    fromQuery: (value: string): any => value,
    toQuery: (value: string): any => value
  },
  boolean: {
    fromQuery: (value: string): boolean => value.toLowerCase() === "true",
    toQuery: (value: boolean): string => (value ? "true" : "false")
  }
};
