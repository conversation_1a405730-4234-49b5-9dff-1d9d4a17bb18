export default {
  device: {
    configure: {
      selectConfigure: "请选择组态图符文件！",
      loading: "加载中",
      operating: "操作中",
      loadFailed: "加载失败，原因：",
      getCustomDeviceFailed: "获取自定义设备失败",
      registerDataFailed: "注册数据失败，原因：",
      variablesNotExist: "部分变量不存在，：",
      getDataError: "获取数据错误",
      remoteSet: "遥设",
      remoteControlFailed: "遥控失败，原因：",
      remoteControlSuccess: "遥控成功",
      noRemoteControlType: "未配置遥控类型",
      symbolChangeReload: "图符变动从新加载",
      deviceChangeReload: "设备变动从新加载",
      deviceConnectReload: "设备连接成功，从新加载"
    },

    configureList: {
      searchPlaceholder: "根据关键词查找",
      deviceMonitor: "设备监控",
      newProject: "新建项目",
      addCustomComponent: "新增自定义组件",
      newConfigure: "新建组态",
      renameProject: "重命名项目",
      deleteProject: "删除项目",
      editConfigure: "编辑组态",
      renameConfigure: "重命名组态",
      deleteConfigure: "删除组态",
      openFolder: "打开所在文件夹",
      inputProjectName: "请输入项目名称(10个字符以内，不得出现特殊字符)",
      inputConfigureName: "请输入组态名称(10个字符以内，不得出现特殊字符)",
      confirm: "确定",
      cancel: "取消",
      invalidName: "名称长度或内容不合法",
      projectAddSuccess: "项目：{name}新增成功",
      projectAddFailed: "项目：{name}新增失败，原因：",
      configureAddSuccess: "组态：{name}新增成功",
      configureAddFailed: "组态：{name}新增失败，原因：",
      renameSuccess: "重命名成功",
      renameFailed: "重命名失败，原因：",
      confirmDelete: "确定删除吗?",
      confirmBatchDelete: "该项目下有关联组态内容，确定批量删除吗?",
      deleteSuccess: "删除成功",
      deleteFailed: "删除失败，原因：",
      remoteSet: "删除确认"
    },
    configures: {
      customComponent: "自定义组件",
      selectDevice: "请选择关联装置！",
      edit: "编辑："
    },
    customConfigure: {
      getCustomDeviceFailed: "获取自定义设备失败",
      saveDeviceFailed: "保存设备图符失败",
      saveSuccess: "保存成功",
      deleteDeviceFailed: "删除设备图符失败",
      deleteSuccess: "删除成功",
      tip: "提示信息"
    },
    deviceList: {
      unnamed: "未命名装置",
      connect: "连接",
      disconnect: "断开",
      edit: "编辑",
      delete: "删除",
      notFound: "没有找到设备",
      editWarn: "请先断开连接再编辑",
      deleteWarn: "请先断开连接再删除",
      connectSuccess: "装置{name}：连接成功",
      connectExist: "装置{name}：连接已存在",
      connectFailed: "装置{name}：连接失败",
      connectFailedReason: "装置连接失败原因：{reason}",
      disconnectSuccess: "装置{name}：已断开",
      operateFailed: "装置{name}：操作失败",
      remove: "删除"
    },
    deviceSearch: {
      searchPlaceholder: "搜索装置"
    },
    editConfigure: {
      saveSuccess: "保存成功",
      saveFailed: "保存失败，原因：",
      loadFailed: "加载失败，原因：",
      getCustomDeviceFailed: "获取自定义设备失败",
      tip: "提示信息"
    },
    remoteSet: {
      inputValue: "输入值",
      write: "写入",
      cancel: "取消",
      setFailed: "遥设失败，原因：",
      operateSuccess: "操作成功",
      noSetType: "未配置遥设类型"
    }
  },
  graph: {
    component: {
      electricSymbols: "电气符号",
      customComponents: "自定义组件",
      basicComponents: "基础组件"
    },
    toolbar: {
      undo: "撤销",
      redo: "重做",
      bringToFront: "置前",
      sendToBack: "置后",
      ratio: "等比例缩放",
      delete: "删除",
      save: "保存"
    },
    contextMenu: {
      group: "组合",
      ungroup: "解组",
      linkData: "关联数据",
      equipmentSaddr: "设备地址"
    },
    dialog: {
      dataConfig: "数据配置",
      tip: "提示信息",
      selectOneGraph: "请选中一个图形"
    },
    message: {
      waitForCanvasInit: "请等待画布初始化完成",
      loadEquipmentFailed: "加载图符失败",
      loadEquipmentError: "加载设备失败",
      equipmentLoaded: "设备加载完成"
    },
    basic: {
      title: "基础组件",
      components: {
        line: "线",
        text: "文本",
        rectangle: "矩形",
        circle: "圆形",
        ellipse: "椭圆",
        triangle: "三角形",
        arc: "圆弧"
      }
    },
    selectEquipment: {
      sequence: "序号",
      name: "名称",
      type: "类型",
      symbol: "图符",
      operation: "操作",
      reference: "引用"
    },
    setSAddr: {
      telemetry: "遥信/遥测",
      format: "格式化",
      factor: "系数",
      remoteControl: "遥控",
      controlType: "遥控方式",
      controlValue: "遥控值",
      remoteSet: "遥设",
      setType: "遥设方式",
      displayConfig: "显示配置",
      addRow: "增加行",
      sequence: "序号",
      type: "类型",
      originalValue: "原始值",
      displayValue: "显示值",
      operation: "操作",
      text: "文本",
      symbol: "图符",
      selectSymbol: "选择图符",
      confirm: "确定",
      cancel: "取消",
      confirmDelete: "确定删除吗?",
      tip: "提示信息",
      selectControl: "选控",
      directControl: "直控",
      controlClose: "控合",
      controlOpen: "控分",
      cancelDelete: "取消删除"
    },
    equipmentType: {
      CBR: "断路器",
      DIS: "隔离刀闸",
      GDIS: "接地刀闸",
      PTR2: "2卷变",
      PTR3: "3卷变",
      VTR: "电压互感器",
      CTR: "电流互感器",
      EFN: "中性点接地装置",
      IFL: "出线",
      EnergyConsumer: "负荷",
      GND: "接地",
      Arrester: "避雷器",
      Capacitor_P: "并联电容器",
      Capacitor_S: "串联电容器",
      Reactor_P: "并联电抗器",
      Reactor_S: "串联电抗器",
      Ascoil: "消弧线圈",
      Fuse: "熔断器",
      BAT: "电池",
      BSH: "套管",
      CAB: "电缆",
      LIN: "架空线",
      GEN: "发电机",
      GIL: "电气绝缘线",
      RRC: "旋转无功元件",
      TCF: "晶闸管控制变频器",
      TCR: "晶闸管控制无功元件",
      LTC: "分接头",
      IND: "电感器"
    },
    equipmentName: {
      breaker_vertical: "断路器-竖",
      breaker_horizontal: "断路器-横",
      breaker_invalid_vertical: "断路器-无效-竖",
      breaker_invalid_horizontal: "断路器-无效-横",
      disconnector_vertical: "刀闸-竖",
      disconnector_horizontal: "刀闸-横",
      disconnector_invalid_vertical: "刀闸-无效竖",
      disconnector_invalid_horizontal: "刀闸-无效横",
      hv_fuse: "高压熔断器",
      station_transformer_2w: "站用变（两绕组）",
      transformer_y_d_11: "变压器（Y/△-11）",
      transformer_d_y_11: "变压器（△/Y-11）",
      transformer_d_d: "变压器（△/△）",
      transformer_y_y_11: "变压器（Y/Y-11）",
      transformer_y_y_12_d_11: "变压器（Y/Y-12/△-11）",
      transformer_y_d_11_d_11: "变压器（Y/△-11/△-11）",
      transformer_y_y_v: "变压器（Y/Y/V）",
      transformer_autotransformer: "变压器（自耦）",
      voltage_transformer_2w: "电压互感器（两绕组）",
      voltage_transformer_3w: "电压互感器（三绕组）",
      voltage_transformer_4w: "电压互感器（四绕组）",
      arrester: "避雷器",
      capacitor_horizontal: "电容-横",
      capacitor_vertical: "电容-竖",
      reactor: "电抗",
      split_reactor: "分裂电抗",
      power_inductor: "电力电感器",
      feeder: "出线",
      ground: "接地",
      tap_changer: "分接头",
      connection_point: "连接点",
      transformer_y_y_12_d_11_new: "变压器(Y/Y-12/△-11)(新)",
      pt: "PT",
      arrester_new: "避雷器(新)",
      disconnector_vertical_new: "刀闸-竖(新)",
      disconnector_horizontal_new: "刀闸-横(新)",
      arrester_new_vertical: "避雷器(新)-竖",
      disconnector_vertical_left_new: "刀闸-竖左(新)"
    }
  },
  graphProperties: {
    blank: {
      propertySetting: "属性设置"
    },
    graph: {
      canvasSetting: "画布设置",
      grid: "网格",
      backgroundColor: "背景色"
    },
    group: {
      groupProperty: "组合属性",
      basic: "基本",
      width: "宽度",
      height: "高度",
      x: "位置(X)",
      y: "位置(Y)",
      angle: "旋转角度"
    },
    node: {
      nodeProperty: "节点属性",
      style: "样式",
      backgroundColor: "背景色",
      borderWidth: "边框宽度",
      borderColor: "边框颜色",
      borderDasharray: "边框样式",
      rx: "边框rx",
      ry: "边框ry",
      position: "位置",
      width: "宽度",
      height: "高度",
      x: "位置(X)",
      y: "位置(Y)",
      property: "属性",
      angle: "旋转角度",
      zIndex: "层级(z)",
      fontFamily: "字体",
      fontColor: "字体颜色",
      fontSize: "字体大小",
      text: "文本"
    },
    pathLine: {
      lineSetting: "线设置",
      style: "样式",
      lineHeight: "宽度",
      lineColor: "颜色",
      borderDasharray: "边框",
      position: "位置",
      width: "宽度",
      height: "高度",
      x: "位置(X)",
      y: "位置(Y)",
      property: "属性",
      angle: "旋转角度",
      zIndex: "层级(z)"
    }
  },
  business: {
    hmi: {
      title: "画面管理",
      form: {
        add: "新增画面",
        edit: "编辑画面",
        view: "查看画面",
        name: "画面名称",
        type: "画面类型",
        template: "画面模板",
        description: "描述",
        cancel: "取消",
        confirm: "确定",
        validation: {
          name: "请输入画面名称",
          type: "请选择画面类型",
          template: "请选择画面模板"
        }
      },
      columns: {
        name: "画面名称",
        type: "画面类型",
        template: "画面模板",
        createTime: "创建时间",
        updateTime: "更新时间",
        status: "状态",
        operation: "操作"
      },
      type: {
        device: "设备画面",
        process: "工艺画面",
        alarm: "告警画面",
        custom: "自定义画面"
      },
      status: {
        draft: "草稿",
        published: "已发布",
        archived: "已归档"
      },
      editor: {
        title: "画面编辑",
        save: "保存",
        preview: "预览",
        publish: "发布",
        cancel: "取消",
        tools: {
          select: "选择",
          rectangle: "矩形",
          circle: "圆形",
          line: "线条",
          text: "文本",
          image: "图片",
          device: "设备",
          alarm: "告警",
          chart: "图表"
        },
        properties: {
          title: "属性",
          position: "位置",
          size: "大小",
          style: "样式",
          data: "数据",
          event: "事件"
        }
      },
      preview: {
        title: "画面预览",
        fullscreen: "全屏",
        exit: "退出",
        zoom: {
          in: "放大",
          out: "缩小",
          fit: "适应"
        }
      },
      publish: {
        title: "发布画面",
        version: "版本号",
        description: "发布说明",
        cancel: "取消",
        confirm: "确定",
        validation: {
          version: "请输入版本号",
          description: "请输入发布说明"
        }
      },
      template: {
        title: "画面模板",
        add: "新增模板",
        edit: "编辑模板",
        delete: "删除模板",
        name: "模板名称",
        category: "模板分类",
        description: "描述",
        preview: "预览",
        cancel: "取消",
        confirm: "确定",
        validation: {
          name: "请输入模板名称",
          category: "请选择模板分类"
        }
      }
    }
  }
};
