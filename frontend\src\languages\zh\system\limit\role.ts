export default {
  limit: {
    role: {
      title: "角色管理",
      form: {
        add: "新增角色",
        edit: "编辑角色",
        view: "查看角色",
        name: "角色名称",
        code: "角色编码",
        category: "角色类别",
        org: "所属组织",
        status: "状态",
        sort: "排序",
        description: "描述",
        cancel: "取消",
        confirm: "确定",
        validation: {
          name: "请输入角色名称",
          code: "请输入角色编码",
          category: "请选择角色类别",
          org: "请选择所属组织",
          status: "请选择状态"
        }
      },
      columns: {
        name: "角色名称",
        code: "角色编码",
        category: "角色类别",
        org: "所属组织",
        status: "状态",
        sort: "排序",
        operation: "操作"
      },
      category: {
        system: "系统角色",
        org: "组织角色"
      },
      status: {
        enable: "启用",
        disable: "禁用"
      },
      grantResource: {
        title: "授权资源",
        warning: "请选择需要授权的资源",
        firstLevel: "一级菜单",
        menu: "菜单",
        buttonAuth: "按钮权限",
        cancel: "取消",
        confirm: "确定",
        selectDataScope: "选择数据范围",
        api: "接口",
        dataScope: "数据范围"
      },
      grantPermission: {
        title: "授权权限",
        warning: "请选择需要授权的权限",
        api: "接口",
        apiPlaceholder: "请输入接口名称",
        dataScope: "数据范围",
        cancel: "取消",
        confirm: "确定"
      },
      dataScope: {
        selectOrg: "选择组织",
        orgList: "组织列表",
        cancel: "取消",
        confirm: "确定"
      }
    }
  }
};
