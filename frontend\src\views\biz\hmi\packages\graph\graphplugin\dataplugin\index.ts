import { Disposable, Graph } from "@antv/x6";
import { DataImpl } from "./data";

export class DataPlugin extends Disposable implements Graph.Plugin {
  public name = "data";
  private dataImpl!: DataImpl;
  public options: DataImpl.Options;

  constructor(options: DataImpl.Options) {
    super();
    this.options = { enabled: true, ...options };
  }

  init(graph: Graph) {
    this.dataImpl = new DataImpl({
      ...this.options,
      graph
    });
  }

  // #region api

  isEnabled() {
    return !this.dataImpl.disabled;
  }

  enable() {
    this.dataImpl.enable();
  }

  disable() {
    this.dataImpl.disable();
  }
  getData() {
    return this.dataImpl.data;
  }
  // #endregion

  //@Disposable.dispose()
  dispose() {
    this.dataImpl.dispose();
  }
}
