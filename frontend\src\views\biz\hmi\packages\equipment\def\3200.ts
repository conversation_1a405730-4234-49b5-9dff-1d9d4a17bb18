import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "3200",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(12, 0, 16, 16)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 12,
        y1: 8.33,
        x2: 0,
        y2: 8.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
