<template>
  <svg
    t="1742955765195"
    class="icon"
    viewBox="0 0 1097 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="32635"
    :width="props.width"
    :height="props.height"
  >
    <path
      :fill="props.color"
      d="M950.857143 36.571429A109.714286 109.714286 0 0 1 1060.571429 146.285714v505.417143a109.714286 109.714286 0 0 1-109.714286 109.714286l-113.078857-0.073143v169.545143a73.142857 73.142857 0 0 1-73.142857 73.142857H73.142857a73.142857 73.142857 0 0 1-73.142857-73.142857V425.545143a73.142857 73.142857 0 0 1 73.142857-73.142857h76.434286V146.285714A109.714286 109.714286 0 0 1 248.758857 37.083429L259.291429 36.571429z m0 73.142857H259.291429a36.571429 36.571429 0 0 0-36.571429 36.571428v206.116572h541.988571a73.142857 73.142857 0 0 1 73.142858 73.142857l-0.073143 262.656H950.857143a36.571429 36.571429 0 0 0 35.986286-29.988572l0.585142-6.582857V146.285714a36.571429 36.571429 0 0 0-36.571428-36.571428z"
      p-id="32636"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";

const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
