interface DebugInfo {
  configVersion: string;
  menus: DebugInfoMenu[];
}

interface DebugInfoMenu {
  name: string;
  desc: string;
  fc?: string;
  items?: DebugInfoItem[];
  menus?: DebugInfoMenu[];
}

interface GroupInfoItem {
  id: number;
  name: string;
  desc: string;
  fc?: string;
  count?: number;
}

interface DebugInfoItem {
  index?: number;
  name: string;
  vKey?: string;
  bType?: string;
  format?: string;
  qKey?: string;
  unit?: string;
  desc: string;
  cn?: string;
  grp?: string;
  inf?: string;
  p_min?: string;
  p_max?: string;
  p_norm?: string;
  s_norm?: string;
  s_min?: string;
  s_max?: string;
  step?: string;
  type?: string;
  value?: unknown;
  quality?: unknown;
}

interface YkInfoItem {
  index?: number;
  name: string;
  vKey?: string;
  bType?: string;
  format?: string;
  qKey?: string;
  unit?: string;
  desc: string;
  cn?: string;
  grp?: string;
  inf?: string;
  p_min?: string;
  p_max?: string;
  p_norm?: string;
  s_norm?: string;
  s_min?: string;
  s_max?: string;
  step?: string;
  type?: string;
  value?: unknown;
  quality?: unknown;
  ctlValue: boolean;
  ctlCheck: string;
}

export type { DebugInfo, DebugInfoMenu, DebugInfoItem, GroupInfoItem, YkInfoItem };
