import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0601",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(4, 0, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 1.66,
        x2: 4,
        y2: 1.66
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(28, 0, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 35.67,
        y1: 1.66,
        x2: 31.67,
        y2: 1.66
      }
    },

    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5.67,
        y1: 0,
        x2: 29.34,
        y2: 0
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
