.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-aside) {
    width: auto;
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-aside-border-color);
    .aside-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: width 0.3s ease;
      .el-scrollbar {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        justify-content: center; // 垂直居中
        height: 100%;
        .el-menu {
          display: flex;
          flex-direction: column;
          justify-content: center; // 垂直居中
          width: 100%;
          padding: 0; // 去除左右padding，避免图标被挤偏
          overflow-x: hidden;
          border-right: none;
          .el-menu-item {
            .el-menu-tooltip__trigger {
              display: flex;
              align-items: center;
              justify-content: center;
              .el-icon {
                margin: 0 auto;
                font-size: 23px;
              }
            }
            .icon-center {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              margin-left: 0 !important;
            }
          }
        }
      }
      .logo {
        box-sizing: border-box;
        height: 55px;
        .logo-img {
          width: 32px;
          object-fit: contain;
        }
        .logo-text {
          margin-right: 6px;
          font-size: 21.5px;
          font-weight: bold;
          color: var(--el-aside-logo-text-color);
          white-space: nowrap;
        }
      }
      .other {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
        padding: 10px 0;
        .iconfont {
          font-size: 22px;
          color: var(--el-menu-text-color);
          cursor: pointer;
          transition: all 0.2s ease-in-out;
          &:hover {
            color: var(--el-color-primary);
            transform: scale(1.2);
          }
        }
      }
    }
  }
  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
  }
}
