import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0700",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5.33,
        y1: 21.33,
        x2: 0,
        y2: 17
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(1, 4, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 2.67,
        y1: 0,
        x2: 2.67,
        y2: 4
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0.67, 28, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 2.33,
        y1: 35.67,
        x2: 2.33,
        y2: 31.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
