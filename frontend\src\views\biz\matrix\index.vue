<template>
  <div class="content-box">
    <!-- 左侧 -->
    <div class="device-left" ref="deviceLeft" :style="deviceLeftStyle.showDeviceList">
      <FuctionSearch></FuctionSearch>
      <FunctionList @change="handleViewChange"></FunctionList>
    </div>
    <!-- 分隔线 -->
    <div ref="resizeVerticalRef" class="resize-vertical-no-border">
      <!-- 折叠按钮 - 垂直居中 -->
      <div class="collapse-button-container">
        <DeviceListCollapse :is-collapse="isCollapse" :is-tool-mode="true" @update:is-collapse="isCollapse = $event" />
      </div>
    </div>
    <!-- 右侧 -->
    <div class="device-right" ref="deviceRight" style="display: flex; flex-direction: column; height: 100%; overflow: hidden">
      <div style="flex: 1 1 0; min-height: 0; overflow: auto">
        <Suspense>
          <!-- 异步组件 -->
          <template #default>
            <Transition name="component-fade" mode="out-in">
              <component :is="currentComponent" :key="activeKey" />
            </Transition>
          </template>

          <!-- 加载中的占位内容 -->
          <template #fallback>
            <div class="loading-placeholder">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>{{ t("common.loading") }}</p>
              </div>
            </div>
          </template>
        </Suspense>
      </div>
      <Console :scopeid="matrix" v-if="showConsole" />
    </div>
  </div>
</template>

<script setup lang="ts" name="treeFilter">
import FuctionSearch from "@/views/biz/matrix/components/FuctionSearch.vue";
import FunctionList from "@/views/biz/matrix/components/FunctionList.vue";
import Console from "@/views/biz/debug/device/components/Console.vue";
import DeviceListCollapse from "@/views/biz/debug/device/components/DeviceListCollapse.vue";
// import { useResizeVertical } from "@/scripts/resizeVertical"; // 暂时不使用
import { useGlobalStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { ref, computed, watch, onMounted, nextTick, onBeforeUnmount, defineAsyncComponent } from "vue";
import { localGet, localSet } from "@/utils/index";
import DetailedSkeletonLoading from "@/components/Loading/DetailedSkeletonLoading.vue";
import { componentSwitchMonitor } from "@/utils/componentSwitchMonitor";

const { t } = useI18n();
const deviceLeft = ref();
const matrix = ref("matrix");
const resizeVerticalRef = ref();
const deviceRight = ref();
const globalStore = useGlobalStore();
const showConsole = ref(false);
const activeKey = ref("batch"); // 默认选中项
const isComponentLoading = ref(false); // 组件加载状态
const componentCache = new Map(); // 组件缓存

// 防抖处理工具切换
let switchTimer: NodeJS.Timeout | null = null;
const handleViewChange = item => {
  console.log("点击菜单项:", item.key, "当前宽度:", userAdjustedWidth.value);

  // 如果正在切换，取消之前的切换
  if (switchTimer) {
    clearTimeout(switchTimer);
    // 只有在确实有正在进行的切换时才调用 endSwitch
    if (componentSwitchMonitor.getCurrentSwitch()) {
      componentSwitchMonitor.endSwitch(true); // 标记为中断
    }
  }

  // 记录当前宽度，防止被意外修改
  const currentWidth = userAdjustedWidth.value;

  // 防抖处理，避免频繁切换
  switchTimer = setTimeout(async () => {
    try {
      isComponentLoading.value = true;

      // 检查是否有缓存
      const fromCache = componentCache.has(item.key);
      componentSwitchMonitor.startSwitch(item.key, fromCache);

      // 预加载组件（如果还没有缓存）
      await preloadComponent(item.key);

      activeKey.value = item.key;

      // 确保宽度不被改变
      nextTick(() => {
        if (deviceLeft.value && deviceLeft.value.style.width !== currentWidth) {
          console.log("检测到宽度被意外修改，恢复为:", currentWidth);
          deviceLeft.value.style.width = currentWidth;
          userAdjustedWidth.value = currentWidth;
        }
        isComponentLoading.value = false;
        componentSwitchMonitor.endSwitch(); // 切换完成
      });
    } catch (error) {
      console.error("组件切换失败:", error);
      isComponentLoading.value = false;
      componentSwitchMonitor.recordError("未知错误");
    }
  }, 100); // 100ms防抖
};
// 组件映射表
const componentMap = {
  xml: () => import("@/views/biz/tools/custom/xml-formatter/Xml-formatter.vue"),
  json: () => import("@/views/biz/tools/custom/json-viewer/Json-viewer.vue"),
  radix: () => import("@/views/biz/tools/custom/integer-base-converter/Integer-base-converter.vue"),
  temperature: () => import("@/views/biz/tools/custom/temperature-converter/Temperature-converter.vue"),
  crypto: () => import("@/views/biz/tools/custom/encryption/Encryption.vue"),
  package: () => import("@/views/biz/matrix/views/PackageProgram.vue"),
  default: () => import("@/views/biz/matrix/components/MatrixContent.vue")
};

// 预加载组件函数
const preloadComponent = async (key: string) => {
  if (componentCache.has(key)) {
    return componentCache.get(key);
  }

  try {
    const loader = componentMap[key] || componentMap.default;
    const component = await loader();
    componentCache.set(key, component);
    return component;
  } catch (error) {
    console.error(`预加载组件失败: ${key}`, error);
    throw error;
  }
};

// 动态组件按需加载（优化版）
const currentComponent = computed(() => {
  const key = activeKey.value;
  const loader = componentMap[key] || componentMap.default;

  return defineAsyncComponent({
    loader,
    loadingComponent: DetailedSkeletonLoading,
    errorComponent: {
      template: `
        <div class="component-error">
          <p>组件加载失败，请重试</p>
        </div>
      `
    },
    delay: 150, // 150ms后显示loading
    timeout: 10000 // 10秒超时
  });
});

watch(
  activeKey,
  val => {
    if (["xml", "json", "radix", "temperature", "crypto", "package"].includes(val)) {
      showConsole.value = false;
    } else {
      showConsole.value = true;
    }
  },
  { immediate: true }
);

// 预加载常用组件的函数
const preloadCommonComponents = async () => {
  // 延迟预加载，避免影响初始渲染
  setTimeout(async () => {
    const commonTools = ["xml", "json", "radix", "temperature", "crypto"];

    // 批量预加载常用工具
    const preloadPromises = commonTools.map(async tool => {
      try {
        await preloadComponent(tool);
        console.log(`✅ 预加载完成: ${tool}`);
      } catch (error) {
        console.warn(`⚠️ 预加载失败: ${tool}`, error);
      }
    });

    await Promise.allSettled(preloadPromises);
    console.log("🚀 工具组件预加载完成");
  }, 2000); // 2秒后开始预加载
};

const isCollapse = ref(false);
// 记录用户调整的宽度，避免被菜单点击影响
// 从 localStorage 读取保存的宽度，如果没有则使用默认值
const userAdjustedWidth = ref(localGet("deviceLeft") || "300px");

// 防抖函数，避免频繁保存
let saveTimer: NodeJS.Timeout | null = null;
const debouncedSave = (width: string) => {
  if (saveTimer) clearTimeout(saveTimer);
  saveTimer = setTimeout(() => {
    localSet("deviceLeft", width);
  }, 300);
};

const deviceLeftStyle = computed<any>(() => {
  const show = globalStore.isDeviceList;

  if (isCollapse.value) {
    // 折叠状态：隐藏并设置宽度为0
    return { showDeviceList: { display: "none", width: "0px", transition: "all 0.3s" } };
  } else if (show) {
    // 展开状态：使用用户调整的宽度
    return { showDeviceList: { display: "flex", width: userAdjustedWidth.value, transition: "all 0.3s" } };
  } else {
    // 全局隐藏状态
    return { showDeviceList: { display: "none", width: userAdjustedWidth.value, transition: "all 0.3s" } };
  }
});

// 暂时注释掉 useResizeVertical，手动实现折叠功能
// const { hideOne, resotreOne } = useResizeVertical(deviceLeft, deviceRight, resizeVerticalRef, undefined, {
//   persistent: false, // 暂时禁用自动持久化，手动控制
//   keyOne: "deviceLeft",
//   keyTwo: "deviceRight",
//   defaultOne: "300",
//   defaultTwo: "",
//   maxOne: 340,
//   minOne: 300
// });

// 手动实现折叠功能
const hideOne = () => {
  if (deviceLeft.value) {
    deviceLeft.value.style.width = "0px";
  }
  if (deviceRight.value) {
    deviceRight.value.style.width = "calc(100% - 2px)";
  }
};

const resotreOne = () => {
  if (deviceLeft.value) {
    deviceLeft.value.style.width = userAdjustedWidth.value;
  }
  if (deviceRight.value) {
    deviceRight.value.style.width = `calc(100% - ${userAdjustedWidth.value} - 2px)`;
  }
};

// 手动实现拖拽调整功能
const setupResizeHandler = () => {
  if (!resizeVerticalRef.value) return;

  const onMousedown = () => {
    const targetRect = deviceLeft.value!.getBoundingClientRect();
    const targetLeft = targetRect.left;

    document.body.style.cursor = "ew-resize";
    document.body.style.userSelect = "none";

    const onMousemove = (e: MouseEvent) => {
      let oneWidth = Math.max(300, Math.min(340, e.clientX - targetLeft));
      const newWidth = `${oneWidth}px`;

      if (deviceLeft.value) {
        deviceLeft.value.style.width = newWidth;
      }
      if (deviceRight.value) {
        deviceRight.value.style.width = `calc(100% - ${newWidth} - 2px)`;
      }

      // 更新用户调整的宽度
      userAdjustedWidth.value = newWidth;
      debouncedSave(newWidth);
    };

    const onMouseup = () => {
      document.body.style.cursor = "auto";
      document.body.style.userSelect = "auto";
      document.removeEventListener("mousemove", onMousemove);
      document.removeEventListener("mouseup", onMouseup);
    };

    document.addEventListener("mousemove", onMousemove);
    document.addEventListener("mouseup", onMouseup);
  };

  resizeVerticalRef.value.addEventListener("mousedown", onMousedown);
};

watch(isCollapse, val => {
  if (val) {
    // 收缩时记录当前宽度并隐藏左侧面板
    if (deviceLeft.value?.style.width) {
      userAdjustedWidth.value = deviceLeft.value.style.width;
    }
    hideOne();
  } else {
    // 展开时恢复宽度
    resotreOne();
    // 确保使用记录的宽度
    if (deviceLeft.value) {
      deviceLeft.value.style.width = userAdjustedWidth.value;
    }
  }
});

// 监听 deviceLeft 的宽度变化，实时更新 userAdjustedWidth
watch(
  () => deviceLeft.value?.style.width,
  (newWidth, oldWidth) => {
    console.log(`宽度变化监听: ${oldWidth} -> ${newWidth}, 折叠状态: ${isCollapse.value}`);
    if (newWidth && newWidth !== "0px" && !isCollapse.value) {
      // 只有当新宽度与当前记录的宽度不同时才更新
      if (newWidth !== userAdjustedWidth.value) {
        console.log(`更新用户宽度: ${userAdjustedWidth.value} -> ${newWidth}`);
        userAdjustedWidth.value = newWidth;
        // 使用防抖保存到 localStorage
        debouncedSave(newWidth);
      }
    }
  }
);

// 强制保持宽度的函数
const forceKeepWidth = () => {
  if (deviceLeft.value && !isCollapse.value) {
    const currentDOMWidth = deviceLeft.value.style.width;
    if (currentDOMWidth !== userAdjustedWidth.value) {
      console.log(`强制恢复宽度: ${currentDOMWidth} -> ${userAdjustedWidth.value}`);
      deviceLeft.value.style.width = userAdjustedWidth.value;
    }
  }
};

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    // 设置初始宽度为用户保存的宽度
    if (deviceLeft.value) {
      deviceLeft.value.style.width = userAdjustedWidth.value;
      console.log("初始化宽度为:", userAdjustedWidth.value);
    }

    // 设置拖拽调整功能
    setupResizeHandler();

    // 定期检查宽度是否被意外修改
    const intervalId = setInterval(forceKeepWidth, 100);

    // 组件卸载时清除定时器
    onBeforeUnmount(() => {
      clearInterval(intervalId);
    });
  });

  // 启动组件预加载
  preloadCommonComponents();
});
</script>

<style scoped lang="scss">
@import "./index";
@import "@/styles/resize";
</style>
