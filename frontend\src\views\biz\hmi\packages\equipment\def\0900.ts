import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "0900",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 22,
        x2: 5,
        y2: 33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 0,
        x2: 5,
        y2: 11.33
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(0, 5, 10, 23.5)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 22.67,
        x2: 8,
        y2: 15.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
