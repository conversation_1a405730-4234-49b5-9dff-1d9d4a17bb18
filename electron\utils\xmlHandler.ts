import { writeFileSync, readFileSync } from "fs";
import { <PERSON><PERSON><PERSON>, Parser, parseString } from "xml2js";
import { Column } from "../interface/debug/exportTypes";
import { ImportResult, SheetData } from "../interface/debug/sheetDara";
import { decodeXML } from "entities"; // For unescaping special characters
import fs from "fs/promises";
import { t } from "../data/i18n/i18n";

const escapeMap: Record<string, string> = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&apos;",
};

function encodeXML(str: string) {
  return str.replace(/[&<>"']/g, (c) => escapeMap[c]);
}

export class XmlHandler {
  // 导出方法（与图片保持一致并修复语法）
  static async export(
    data: any[],
    columns: Column[],
    filePath: string,
    grpName: string,
    rootName = "Config"
  ): Promise<void> {
    const builder = new Builder();
    const XMLData = {
      [rootName]: {
        Group: {
          $: {
            // grpname作为Group节点的属性
            grpname: encodeXML(grpName),
          },
          Item: data.map((item) => ({
            $: columns.reduce(
              (acc, col) => {
                // Item子节点转属性
                // 动态生成属性名，处理特殊字符
                const attrName = col.key.replace(/[^\w]/g, "_");
                acc[attrName] = encodeXML(item[col.key]?.toString() || "");
                return acc;
              },
              {} as Record<string, string>
            ),
          })),
        },
      },
    };

    const xml = builder.buildObject(XMLData);
    writeFileSync(filePath, xml);
  }

  // 增强版导出方法
  static async exportMutiSheet(
    groups: SheetData[], // 改为接收多个分组
    columns: Column[],
    filePath: string,
    rootName = "Config"
  ): Promise<void> {
    const builder = new Builder();
    const XMLData = {
      [rootName]: {
        Group: groups.map((group) => ({
          $: {
            grpname: group.sheetName,
          },
          Item: group.data.map((item) => ({
            $: columns.reduce(
              (acc, col) => {
                // Item子节点转属性
                // 动态生成属性名，处理特殊字符
                const attrName = col.key.replace(/[^\w]/g, "_");
                acc[attrName] = encodeXML(item[col.key]?.toString() || "");
                return acc;
              },
              {} as Record<string, string>
            ),
          })),
        })),
      },
    };

    const xml = builder.buildObject(XMLData);
    writeFileSync(filePath, xml);
  }

  /**
   * 实现对称导入方法
   * @param filePath XML文件路径
   * @param columns 列定义（需与导出时的columns一致）
   */
  static async import<T = Record<string, any>>(
    filePath: string,
    columns: Column[],
    grpName?: string
  ): Promise<T[]> {
    try {
      // 1. 读取文件（与导出的writeFile对称）
      const xmlContent = await fs.readFile(filePath, "utf-8");

      // 2. 解析XML（使用相同配置）
      const parser = new Parser({
        explicitArray: false, // 保持与导出时一致的数组格式
      });

      const result = await new Promise<any>((resolve, reject) => {
        parser.parseString(xmlContent, (err, data) => {
          err ? reject(err) : resolve(data);
        });
      });
      const rootName = Object.keys(result)[0]; // 动态获取根节点名（如 "config"）

      // 3. 提取Group数据（逆向导出结构）
      const group = result[rootName]?.Group;
      if (!group) throw new Error(t("fileOperations.xml.missingGroupNode"));

      // 支持多分组
      const groupList = Array.isArray(group) ? group : [group];
      let targetGroup = groupList;
      if (grpName) {
        targetGroup = groupList.filter((g) => g.$.grpname === grpName);
      }
      if (targetGroup.length === 0) return [];

      // 5. 逆向Item数据（动态映射列）
      // 只返回第一个匹配分组的数据
      const items = targetGroup[0].Item || [];
      const itemList = Array.isArray(items) ? items : [items];
      const data: T[] = itemList.map((item: any) => {
        return columns.reduce((acc, col) => {
          const attrName = col.key.replace(/[^\w]/g, "_");
          acc[col.key] = decodeXML(item.$[attrName]?.toString());
          return acc;
        }, {} as T);
      });
      return data;
    } catch (error) {
      throw new Error(t("fileOperations.xml.importFailed"));
    }
  }

  static async importMultiGroups<T = Record<string, any>>(
    filePath: string,
    columns: Column[]
  ): Promise<ImportResult<T>> {
    try {
      // 1. 读取 XML 文件（与原图导出方法对称）
      const xmlContent = await fs.readFile(filePath, "utf-8");

      // 2. 解析 XML（保持与导出相同的配置）
      const result = await new Promise<any>((resolve, reject) => {
        parseString(
          xmlContent,
          {
            explicitArray: false, // 与原图导出配置一致
            // attrValueProcessors: [this.xmlAttrProcessor], // 自定义属性处理
          },
          (err, data) => {
            err ? reject(err) : resolve(data);
          }
        );
      });

      // 3. 提取所有 Group 节点（兼容单/多 Group）
      const rootName = Object.keys(result)[0]; // 动态获取根节点名（如 "config"）
      const groups = result[rootName]?.Group;
      if (!groups) throw new Error(t("fileOperations.xml.groupNodeNotFound"));

      // 4. 统一转换为数组格式处理
      const groupList = Array.isArray(groups) ? groups : [groups];

      // 5. 遍历每个 Group 提取数据
      return groupList.map((group) => {
        // 提取 grpname 属性
        const grpname = decodeXML(group.$.grpname);

        // 提取 Item 数据（兼容单/多 Item）
        const items = group.Item || [];
        const itemList = Array.isArray(items) ? items : [items];

        // 动态映射列数据
        const data: T[] = itemList.map((item: any) => {
          return columns.reduce((acc, col) => {
            // 动态生成属性名（与导出逻辑完全对称）
            const attrName = col.key.replace(/[^\w]/g, "_");
            acc[col.key] = decodeXML(item.$[attrName]?.toString());
            return acc;
          }, {} as T);
        });

        return { grpname, data: data };
      });
    } catch (error) {
      throw new Error(t("fileOperations.xml.importFailed"));
    }
  }

  /**
   * 自定义属性处理器（与导出的encodeXML对称）
   */
  private static xmlAttrProcessor(value: string) {
    return decodeXML(value.replace(/_/g, "")); // 还原动态生成的属性名
  }
}
