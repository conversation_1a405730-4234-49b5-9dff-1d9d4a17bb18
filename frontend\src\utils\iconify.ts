import { addCollection } from "@iconify/vue";

// 缓存已加载的图标集合
const loadedCollections = new Set<string>();

// 按需加载图标集合
export async function downloadAndInstall() {
  // 优先加载最常用的图标集合
  const priorityCollections = ["ep", "ant-design"];

  for (const collectionName of priorityCollections) {
    if (!loadedCollections.has(collectionName)) {
      await loadIconCollection(collectionName);
    }
  }

  // 延迟加载其他图标集合
  setTimeout(async () => {
    const otherCollections = ["et", "uiw", "zondicons", "eva", "flat-color-icons", "line-md"];

    for (const collectionName of otherCollections) {
      if (!loadedCollections.has(collectionName)) {
        await loadIconCollection(collectionName);
      }
    }
  }, 1000); // 1秒后加载其他图标集合
}

// 动态加载单个图标集合
async function loadIconCollection(collectionName: string) {
  try {
    let iconJson;

    switch (collectionName) {
      case "ant-design":
        iconJson = await import("@iconify/json/json/ant-design.json");
        break;
      case "ep":
        iconJson = await import("@iconify/json/json/ep.json");
        break;
      case "et":
        iconJson = await import("@iconify/json/json/et.json");
        break;
      case "uiw":
        iconJson = await import("@iconify/json/json/uiw.json");
        break;
      case "zondicons":
        iconJson = await import("@iconify/json/json/zondicons.json");
        break;
      case "eva":
        iconJson = await import("@iconify/json/json/eva.json");
        break;
      case "flat-color-icons":
        iconJson = await import("@iconify/json/json/flat-color-icons.json");
        break;
      case "line-md":
        iconJson = await import("@iconify/json/json/line-md.json");
        break;
      default:
        return;
    }

    addCollection(iconJson.default);
    loadedCollections.add(collectionName);
    console.log(`图标集合 ${collectionName} 加载完成`);
  } catch (error) {
    console.warn(`加载图标集合 ${collectionName} 失败:`, error);
  }
}
