<script setup lang="ts">
import { ref, computed } from "vue";
import { formatXml, isValidXML } from "./xml-formatter.service";
import type { UseValidationRule } from "@/composable/validation";
import { useStorage } from "@vueuse/core";
import CInputText from "@/components/Tools/c-input-text.vue";
import TextareaCopyable from "@/components/Tools/TextareaCopyable.vue";
import { useI18n } from "vue-i18n";
import { withDefaultOnError } from "@/utils/defaults";

const { t } = useI18n();
const inputElement = ref<typeof CInputText>();
const inputValue = ref("<hello><world>foo</world><world>bar</world></hello>");
const indentSize = useStorage("xml-formatter:indent-size", 2);
const collapseContent = useStorage("xml-formatter:collapse-content", true);

const formattedXml = computed(() =>
  withDefaultOnError(
    () =>
      formatXml(inputValue.value, {
        indentation: " ".repeat(indentSize.value),
        collapseContent: collapseContent.value,
        lineSeparator: "\n"
      }),
    ""
  )
);

const rules: UseValidationRule<string>[] = [
  {
    validator: isValidXML,
    message: t("tools.xml.invalid")
  }
];
</script>

<template>
  <div class="xml-formatter-container">
    <div class="head">
      {{ t("tools.xml.title") }}
      <div class="sub-head">{{ t("tools.xml.description") }}</div>
    </div>
    <!-- <div class="main-index">
      <div flex justify-center>
        <el-form-item :label="t('tools.xml.collapseContent')" label-placement="left" label-width="100" style="margin-right: 20px">
          <el-switch v-model="collapseContent" />
        </el-form-item>
        <el-form-item :label="t('tools.xml.indentSize')" label-placement="left" label-width="100" :show-feedback="false">
          <el-input-number v-model="indentSize" :min="0" :max="10" w-100px />
        </el-form-item>
      </div>
    </div> -->
    <div class="main-index">
      <div flex justify-center>
        <el-form-item :label="t('tools.jsonViewer.sortKeys')" label-placement="left" label-width="150" style="margin-right: 20px">
          <el-switch v-model="collapseContent" />
        </el-form-item>
        <el-form-item :label="t('tools.jsonViewer.indentSize')" label-placement="left" label-width="150" :show-feedback="false">
          <el-input-number v-model="indentSize" :min="0" :max="10" w-100px />
        </el-form-item>
      </div>
    </div>
    <div class="sub-title-span">{{ t("tools.xml.inputLabel") }}</div>
    <div class="first-form">
      <el-form-item>
        <c-input-text
          ref="inputElement"
          v-model:value="inputValue"
          :placeholder="t('tools.xml.inputPlaceholder')"
          rows="20"
          multiline
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
          :validation-rules="rules"
          monospace
        />
      </el-form-item>
    </div>

    <div class="output-title">
      <span class="output-icon">✨</span>
      {{ t("tools.xml.outputLabel") }}
    </div>
    <div class="output-form">
      <el-form-item>
        <textarea-copyable :value="formattedXml" language="xml" :follow-height-of="inputElement?.inputWrapperRef" />
      </el-form-item>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "@/styles/utils";
.xml-formatter-container {
  width: 100%;
  height: 100%;
}
.head {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.main-index {
  padding: 16px;
  margin-bottom: 24px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  div {
    @include flex(row, flex-start, center);

    gap: 20px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
    .el-form-item__label {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-regular);
    }
  }
  :deep(.el-switch) {
    --el-switch-on-color: var(--el-color-primary);
  }
  :deep(.el-input-number) {
    .el-input__inner {
      text-align: center;
    }
  }
}
.sub-title-span {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  &::before {
    font-size: 16px;
    content: "📝";
  }
}
.first-form {
  margin-bottom: 24px;
  :deep(.feedback-wrapper .feedback) {
    display: none;
  }
  :deep(.c-input-text) {
    .input-wrapper {
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      transition: border-color 0.2s ease;
      &:hover {
        border-color: var(--el-border-color-hover);
      }
      &:focus-within {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
      }
    }
  }
}
.output-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .output-icon {
    font-size: 16px;
  }
}
:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  .textarea-copyable {
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
  }
}
</style>
