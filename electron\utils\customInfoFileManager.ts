import { readFileSync, writeFileSync, existsSync, mkdirSync } from "fs";
import { Parse<PERSON>, Builder } from "xml2js";
import path from "path";
import { logger } from "ee-core/log";
import IECCONSTANTS from "../data/debug/iecConstants";

const DEFAULT_CONFIG_VERSION = "V1.00";

export interface DebugReport {
  uuid: string;
  name: string;
  newname?: string;
  desc: string;
  keyword: string;
  inherit: string;
  fc: string;
  method: string;
}

export interface DebugItem {
  name: string;
  desc: string;
  grp?: string;
  inf?: string;
  fc?: string;
  unit?: string;
  type?: string;
}

export interface DebugMenu {
  uuid: string;
  name: string;
  newname?: string;
  desc: string;
  fc?: string;
  keyword: string; // 用于存储自定义组所选 fc
  method?: string;
  reports?: DebugMenu[]; // 组下的子Menu即为报告节点
  items?: DebugItem[]; // 自定义组内选择的点
}

export interface DebugInfo {
  configVersion: string;
  menus: DebugMenu[];
}

export class DebugInfoFileManager {
  private filePath: string;
  private debugInfoPath: string = "/shr/custom_info.xml";
  private debugInfoFilePath: string = "/shr/debug_info.xml";
  private deviceId: string;

  constructor(deviceId: string) {
    this.deviceId = deviceId;
    this.filePath =
      IECCONSTANTS.PATH_CONFIG + "/" + deviceId + this.debugInfoPath;
  }

  /** 读取 custom_info.xml，返回结构化对象 */
  public async read(): Promise<DebugInfo> {
    if (!existsSync(this.filePath)) {
      // 文件不存在，返回空结构
      return { configVersion: DEFAULT_CONFIG_VERSION, menus: [] };
    }
    const xmlData = readFileSync(this.filePath, { encoding: "utf-8" });
    const result = await new Parser({
      explicitArray: false,
    }).parseStringPromise(xmlData);
    const root = result.DebugInfo || {};
    const configVersion = root.$?.configVersion || DEFAULT_CONFIG_VERSION;
    let menus: DebugMenu[] = [];
    if (root.Menu) {
      const menuArr = Array.isArray(root.Menu) ? root.Menu : [root.Menu];
      menus = menuArr.map((m: any) => {
        let reports: DebugMenu[] = [];
        if (m.Menu) {
          const reportArr = Array.isArray(m.Menu) ? m.Menu : [m.Menu];
          reports = reportArr.map((r: any) => {
            // 解析报告中的 Item（用于自定义点组）
            let reportItems = [] as DebugItem[];
            if (r.Item) {
              const reportItemArr = Array.isArray(r.Item) ? r.Item : [r.Item];
              reportItems = reportItemArr.map((it: any) => ({
                name: it.$.name,
                desc: it.$.desc,
                grp: it.$.grp,
                inf: it.$.inf,
                fc: it.$.fc,
                unit: it.$.unit || "",
                type: it.$.type || "",
              }));
            }

            return {
              uuid: r.$.uuid || "",
              name: r.$.name,
              newname: r.$.newname || r.$.name,
              desc: r.$.desc,
              fc: r.$.fc,
              method: r.$.method,
              keyword: r.$.keyword || "",
              inherit: r.$.inherit || "",
              items: reportItems, // 添加items属性
            };
          });
        }
        // 解析组内 Item（可选）
        let items = [] as DebugItem[];
        if (m.Item) {
          const itemArr = Array.isArray(m.Item) ? m.Item : [m.Item];
          items = itemArr.map((it: any) => ({
            name: it.$.name,
            desc: it.$.desc,
            grp: it.$.grp,
            inf: it.$.inf,
            fc: it.$.fc,
            unit: it.$.unit,
            type: it.$.type,
          }));
        }
        return {
          uuid: m.$.uuid || "",
          name: m.$.name,
          newname: m.$.newname || m.$.name,
          desc: m.$.desc,
          fc: m.$.fc,
          keyword: m.$.keyword || "",
          reports,
          items,
        };
      });
    }
    return { configVersion, menus };
  }

  /** 保存 custom_info.xml */
  public async save(data: DebugInfo): Promise<void> {
    const builder = new Builder();
    const xmlObj: any = {
      DebugInfo: {
        $: { configVersion: data.configVersion || DEFAULT_CONFIG_VERSION },
        Menu: data.menus.map((m) => ({
          $: {
            name: m.name,
            newname: m.newname,
            desc: m.desc,
            uuid: m.uuid,
            fc: m.fc,
            keyword: m.keyword,
          },
          // 写入报告
          Menu: m.reports?.map((r) => ({
            $: {
              uuid: r.uuid,
              name: r.name,
              newname: r.newname,
              desc: r.desc,
              fc: r.fc,
              method: r.method,
              keyword: r.keyword,
            },
            // 如果报告中包含items（自定义点组），也要写入Item子元素
            Item: r.items?.map((it) => ({
              $: {
                name: it.name,
                desc: it.desc,
                grp: it.grp,
                inf: it.inf,
                fc: it.fc,
                unit: it.unit,
                type: it.type,
              },
            })),
          })),
          // 写入组内点 Item
          Item: m.items?.map((it) => ({
            $: {
              name: it.name,
              desc: it.desc,
              grp: it.grp,
              inf: it.inf,
              fc: it.fc,
              unit: it.unit,
              type: it.type,
            },
          })),
        })),
      },
    };
    const xml = builder.buildObject(xmlObj);
    // 写入前确保目录存在
    mkdirSync(path.dirname(this.filePath), { recursive: true });
    logger.info(this.filePath);
    writeFileSync(this.filePath, xml, { encoding: "utf-8" });
  }

  /** 获取所有菜单和报告 */
  public async getAll(): Promise<DebugInfo> {
    return this.read();
  }

  /** 检查名称是否与 debug_info.xml 中的菜单名称重复 */
  private async checkNameConflictWithDebugInfo(name: string): Promise<boolean> {
    const debugInfoFilePath =
      IECCONSTANTS.PATH_CONFIG + "/" + this.deviceId + this.debugInfoFilePath;

    if (!existsSync(debugInfoFilePath)) {
      logger.warn(`debug_info.xml 文件不存在: ${debugInfoFilePath}`);
      return false; // 文件不存在时不检查重复
    }

    try {
      const xmlData = readFileSync(debugInfoFilePath, { encoding: "utf-8" });
      const result = await new Parser({
        explicitArray: false,
      }).parseStringPromise(xmlData);

      const root = result.DebugInfo || {};

      // 递归检查所有菜单名称
      const checkMenuNames = (menus: any): boolean => {
        if (!menus) return false;

        const menuArray = Array.isArray(menus) ? menus : [menus];
        for (const menu of menuArray) {
          if (menu.$ && menu.$.name === name) {
            return true; // 找到重复名称
          }
          // 递归检查子菜单
          if (menu.Menu && checkMenuNames(menu.Menu)) {
            return true;
          }
        }
        return false;
      };

      return checkMenuNames(root.Menu);
    } catch (error) {
      logger.error(`检查 debug_info.xml 名称重复时出错: ${error}`);
      return false;
    }
  }

  /** 检查名称是否与现有自定义菜单重复 */
  private async checkNameConflictWithCustomInfo(
    name: string
  ): Promise<boolean> {
    const customInfo = await this.read();

    // 递归检查自定义菜单的 newname（显示名称）
    const checkCustomMenuNames = (menus: DebugMenu[]): boolean => {
      for (const menu of menus) {
        // 只检查 newname，因为 name 可能是继承报告的原始名称
        if (menu.newname === name) {
          return true; // 找到重复的显示名称
        }
        // 检查报告（子菜单）
        if (menu.reports && menu.reports.length > 0) {
          if (checkCustomMenuNames(menu.reports)) {
            return true;
          }
        }
      }
      return false;
    };

    return checkCustomMenuNames(customInfo.menus);
  }

  /** 验证菜单显示名称的唯一性 */
  public async validateMenuName(
    newname: string
  ): Promise<{ isValid: boolean; message?: string }> {
    // 检查与 debug_info.xml 的重复
    const conflictWithDebugInfo =
      await this.checkNameConflictWithDebugInfo(newname);
    if (conflictWithDebugInfo) {
      return {
        isValid: false,
        message: `菜单名称 "${newname}" 与系统菜单重复，请使用其他名称`,
      };
    }

    // 检查与现有自定义菜单的重复（检查 newname）
    const conflictWithCustomInfo =
      await this.checkNameConflictWithCustomInfo(newname);
    if (conflictWithCustomInfo) {
      return {
        isValid: false,
        message: `菜单名称 "${newname}" 与现有自定义菜单重复，请使用其他名称`,
      };
    }

    return { isValid: true };
  }

  /** 新增菜单 */
  public async addMenu(menu: DebugMenu): Promise<void> {
    // 如果有 newname，验证其唯一性
    if (menu.newname) {
      const validation = await this.validateMenuName(menu.newname);
      if (!validation.isValid) {
        throw new Error(validation.message);
      }
    }

    const data = await this.read();
    data.menus.push(menu);
    await this.save(data);
  }

  /** 编辑菜单 */
  public async editMenu(uuid: string, newMenu: DebugMenu): Promise<void> {
    const data = await this.read();
    const idx = data.menus.findIndex((m) => m.uuid === uuid);
    if (idx !== -1) {
      data.menus[idx] = newMenu;
      await this.save(data);
    }
  }

  /** 删除菜单 */
  public async deleteMenu(uuid: string): Promise<void> {
    const data = await this.read();
    data.menus = data.menus.filter((m) => m.uuid !== uuid);
    await this.save(data);
  }

  /** 新增报告 */
  public async addReport(menuUuid: string, report: DebugMenu): Promise<void> {
    // 如果有 newname，验证其唯一性
    if (report.newname) {
      const validation = await this.validateMenuName(report.newname);
      if (!validation.isValid) {
        throw new Error(validation.message);
      }
    }

    const data = await this.read();
    const menu = data.menus.find((m) => m.uuid === menuUuid);
    if (menu) {
      if (!menu.reports) menu.reports = [];
      menu.reports.push(report);
      await this.save(data);
    }
  }

  /** 编辑报告 */
  public async editReport(
    menuUuid: string,
    reportUuid: string,
    newReport: DebugMenu
  ): Promise<void> {
    logger.info(newReport);
    const data = await this.read();
    const menu = data.menus.find((m) => m.uuid === menuUuid);
    if (menu && menu.reports) {
      const idx = menu.reports.findIndex((r) => r.uuid === reportUuid);
      if (idx !== -1) {
        menu.reports[idx] = newReport;
        await this.save(data);
      }
    }
  }

  /** 删除报告 */
  public async deleteReport(
    menuUuid: string,
    reportUuid: string
  ): Promise<void> {
    const data = await this.read();
    const menu = data.menus.find((m) => m.uuid === menuUuid);
    if (menu && menu.reports) {
      menu.reports = menu.reports.filter((r) => r.uuid !== reportUuid);
      await this.save(data);
    }
  }
}
