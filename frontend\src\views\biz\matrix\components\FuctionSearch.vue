<template>
  <div class="header-bar">
    <!-- 搜索 -->
    <div class="flx-justify-between">
      <el-input
        v-model="searchInput"
        :placeholder="t('matrix.search.placeholder')"
        style="width: 100%; margin-right: 5px"
        :prefix-icon="Search"
        @input="debounceSearch"
        clearable
      >
      </el-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { ref } from "vue";
import { debounce } from "lodash";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const searchInput = ref<string>("");

defineOptions({
  name: "Search"
});

const debounceSearch = debounce(() => {
  // setSearchDevice(searchInput.value);
}, 200);
</script>

<style lang="scss" scoped>
.header-bar {
  width: 100%;
  height: auto;
  margin-bottom: 5px;
  .suffix {
    position: absolute;
    right: 10px;
    z-index: 5;
    font-size: 12px;
    pointer-events: none;
  }
}
.header-search-add {
  min-width: 32px;
  height: 32px;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  background: #54b4ef;
  border-radius: 2px;
}
.el-button:focus {
  outline: none;
}
</style>
