<template>
  <div class="main-box">
    <TreeFilter
      label="grpname"
      :multiple="false"
      style="overflow: auto"
      :title="t('matrix.paramList.paramGroup')"
      :icon="ArrowRightBold"
      :data="treeFilterData"
      :request-api="getInitData"
      :default-value="initParam.grpName"
      @change="changeTreeFilter"
    />

    <div class="table-box">
      <ProTable
        :columns="columns"
        ref="proTable"
        :init-param="initParam"
        @search="getData"
        highlight-current-row
        :pagination="true"
        :data="tabledata"
        :request-auto="false"
        @select-all="selectionChange"
        @select="handleSelect"
        :data-callback="dataCallback"
        row-key="paramName"
        table-key="matrixParam"
        style="flex: 1; min-height: 0"
      >
        <template #tableHeader="">
          <div class="flex flex-wrap gap-4 items-center header">
            <el-button type="success" :icon="Upload" @click="batchImport">{{ t("matrix.common.import") }}</el-button>
            <el-button type="success" :icon="Download" @click="batchExport">{{ t("matrix.common.export") }}</el-button>
            <el-button type="danger" :icon="Delete" plain @click="clearAll">{{ t("matrix.common.clear") }}</el-button>
          </div>
        </template>
        <!-- Expand -->
        <template #expand="scope">
          {{ scope.row }}
        </template>
        <!-- 表格操作 -->
        <!-- <template #operation="scope">
          <el-button type="primary" link :icon="Delete" @click="deleteSingleFile(scope.row)"> 删除 </el-button>
        </template> -->
      </ProTable>
    </div>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive, computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { matrixApi } from "@/api/modules/biz/matrix/matrix";
import { GroupInfo, ParamItem, SheetData } from "@/api/interface/biz/debug/diffitem";
import { ArrowRightBold, Delete, Download, Upload } from "@element-plus/icons-vue";
import { osControlApi } from "@/api/modules/biz/os";
import ProgressDialog from "../../debug/device/dialog/ProgressDialog.vue";
import { createScopeDebugStore } from "@/stores/modules/debug";
import { useGlobalStore } from "@/stores/modules";

const { addConsole } = createScopeDebugStore("matrix")();
const globalStore = useGlobalStore();

import { useMatrixStore } from "@/stores/modules/matrix";
import { storeToRefs } from "pinia";
const matrixStire = useMatrixStore();
const { paramList, selectParamIds } = storeToRefs(matrixStire);
const tabledata = ref<ParamItem[]>([]);
let resultTableValue = paramList;
const selectIds = selectParamIds;

const proTable = ref<ProTableInstance>();
const progressDialog = ref();
const initParam = reactive({ grpName: "" });

// 动态计算表格容器高度
const tableContainerHeight = computed(() => {
  // 获取页面可用高度
  const windowHeight = window.innerHeight;
  // 头部区域高度（如有固定头部可写死或动态获取）
  const headerHeight = 64; // 例如顶部导航栏高度
  // 控制台区域高度（如有固定高度可写死或动态获取）
  const consoleHeight = globalStore.consoleHeight || 200; // 200为默认控制台高度
  // 其它可能的间距
  const margin = 24; // 额外边距
  // 计算表格区域高度
  return `${windowHeight - headerHeight - consoleHeight - margin}px`;
});

const treeFilterData = ref<GroupInfo[]>([]);
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const getTreeFilter = async (resultValue: any[]) => {
  resultValue.forEach(item => {
    treeFilterData.value.push({
      id: item.grpname,
      grpname: item.grpname
    });
  });
  initParam.grpName = "";
};
// const batchDelete = async (rows: any[]) => {
//   console.log(rows);
//   const fileMap = rows.map(row => row.path);
//   nextTick(() => {
//     rows.forEach(row => {
//       proTable.value?.element?.toggleRowSelection(row, false);
//     });
//   });
//   tabledata.value = tabledata.value.filter(item => !fileMap.includes(item.paramName));
//   resultTableValue = resultTableValue.filter(item => !fileMap.includes(item.paramName));
//   nextTick(() => {
//     tabledata.value.forEach(row => {
//       proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.paramName));
//     });
//   });
//   addConsole("定值删除完成");
// };

const clearAll = async () => {
  console.log("clearAll");
  resultTableValue.value = [];
  treeFilterData.value = [];
  tabledata.value = [];
  selectIds.value = [];
  addConsole(t("matrix.paramList.clearSuccess"));
};
const batchExport = async () => {
  const defaultPath = t("matrix.paramList.exportDefaultPath");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("matrix.paramList.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  // 导出路径不存在返回
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  console.log("selectPath:", selectPath);
  progressDialog.value.show();
  try {
    const paramItems: SheetData[] = [];
    treeFilterData.value.forEach(item => {
      const resultData = resultTableValue.value.filter(data => {
        return data.grpName == item.grpname;
      });
      resultData.forEach((item, index) => (item.index = index + 1));
      if (item.grpname !== "") {
        paramItems.push({
          data: resultData,
          sheetName: item.grpname,
          headers: []
        });
      }
    });
    const result = await matrixApi.exportParanList({
      path,
      paramItems
    });
    // 判断返回结果中的code为0，提示成功，否则提示失败
    if (Number(result.code) === 0) {
      addConsole(t("matrix.paramList.exportSuccess"));
      ElMessageBox.alert(t("matrix.paramList.exportSuccess"), t("matrix.common.title"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.paramList.exportFailed"));
      ElMessageBox.alert(t("matrix.paramList.exportFailed"), t("matrix.common.title"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.paramList.exportFailed"), error);
    ElMessageBox.alert(t("matrix.paramList.exportFailed"), t("matrix.common.title"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};
const getInitData = async () => {
  return { data: [] };
};
// const initSelect = async (params: any) => {
//   console.log(params);
//   nextTick(() => {
//     diffTabledata.value.forEach(row => {
//       proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.paramName));
//     });
//   });
// };
async function getData() {
  console.log("getData");
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));

  const grpName = initParam.grpName;
  let resultData: ParamItem[] = toRaw(resultTableValue.value);
  if (grpName !== "") {
    resultData = resultTableValue.value.filter(data => {
      return data.grpName == grpName;
    });
  }
  if (newParams.paramName) {
    resultData = resultData?.filter(data => {
      return data.paramName.toLowerCase().includes(newParams.paramName.toLowerCase());
    });
  }
  if (newParams.paramDesc) {
    resultData = resultData?.filter(data => {
      return data.paramDesc.toLowerCase().includes(newParams.paramDesc.toLowerCase());
    });
  }
  tabledata.value = resultData;
  proTable.value?.refresh();
  nextTick(() => {
    tabledata.value.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.paramName));
    });
  });
}

const batchImport = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("matrix.paramList.importTitle"),
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] },
      { name: "xml", extensions: ["xml"] }
    ]
  });
  // 导入路径不存在返回
  if (!selectPath.path) {
    return;
  }

  progressDialog.value.show();
  const path = String(selectPath.path);

  try {
    // 比较差异
    const response = await matrixApi.importParamList({ path });
    // 修改 diffData 声明

    console.log("Fetched data:", response);

    // 修改赋值语句（251行附近）
    if (Number(response.code) === 0 && response.data) {
      clearAll();
      const resultValue: ParamItem[] = [];
      // 添加类型断言（假设接口返回数组结构）
      if (Array.isArray(response.data)) {
        response.data.forEach(item => {
          resultValue.push(...item.data);
        });
      }
      resultTableValue.value = resultValue;
      tabledata.value = resultValue;
      resultTableValue.value.forEach(row => {
        selectIds.value = [...new Set([...selectIds.value, row.paramName])];
      });
      // addConsole(response.msg);
      proTable.value?.refresh();
      nextTick(() => {
        if (Array.isArray(response.data)) {
          getTreeFilter(response.data);
          tabledata.value.forEach(row => {
            proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.paramName));
          });
        }
      });
      addConsole(t("matrix.paramList.importSuccess"));
      ElMessageBox.alert(t("matrix.paramList.importSuccess"), t("matrix.common.title"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.paramList.importFailed"));
      ElMessageBox.alert(t("matrix.paramList.importFailed"), t("matrix.common.title"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("matrix.paramList.importFailed"), error);
    ElMessageBox.alert(t("matrix.paramList.importFailed"), t("matrix.common.title"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    progressDialog.value.hide();
  }
};
// 树形筛选切换
const changeTreeFilter = (val: string) => {
  console.log(val);
  proTable.value!.pageable.pageNum = 1;
  initParam.grpName = val;
  getData();
};

const columns = reactive<ColumnProps<ParamItem>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("matrix.common.index"), fixed: "left", width: 70 },
  {
    prop: "grpName",
    label: t("matrix.paramList.groupName")
  },
  {
    prop: "paramName",
    label: t("matrix.paramList.paramName"),
    search: { el: "input", tooltip: t("matrix.paramList.searchParamName") }
  },
  {
    prop: "paramDesc",
    label: t("matrix.paramList.paramDesc"),
    search: { el: "input", tooltip: t("matrix.paramList.searchParamDesc") }
  },
  {
    prop: "minValue",
    label: t("matrix.paramList.minValue")
  },
  { prop: "maxValue", label: t("matrix.paramList.maxValue") },
  { prop: "step", label: t("matrix.paramList.step") },
  { prop: "unit", label: t("matrix.paramList.unit") },
  {
    prop: "value",
    label: t("matrix.paramList.paramValue"),
    render: scope => {
      return (
        <div>
          <el-text type="primary">{scope.row.value}</el-text>
        </div>
      );
    }
  }
  // { prop: "operation", label: "操作", fixed: "right", width: 120 }
]);
const dataCallback = (data: any) => {
  console.log("dataCallback");
  return {
    list: data.list,
    total: data.total
  };
};

// onMounted(() => {
//   nextTick(() => {
//     getTreeFilter();
//     getData();
//     proTable.value?.element?.toggleAllSelection();
//   });
// });
// const deleteSingleFile = (row: any) => {
//   console.log("uploadSingFile", row);
//   nextTick(() => {
//     proTable.value?.element?.toggleRowSelection(row, false);
//   });
//   tabledata.value = tabledata.value.filter(item => row.paramName !== item.paramName);
//   resultTableValue = resultTableValue.filter(item => row.paramName !== item.paramName);
//   proTable.value?.refresh();
//   nextTick(() => {
//     tabledata.value.forEach(row => {
//       proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.paramName));
//     });
//   });
//   addConsole("定值" + row.paramName + "删除完成");
// };
const selectionChange = isSelectAll => {
  console.log("selectionChange", isSelectAll);
  const map = isSelectAll.map(item => item.paramName);
  tabledata.value.forEach(row => {
    if (map.includes(row.paramName)) {
      selectIds.value = [...new Set([...selectIds.value, row.paramName])];
    } else {
      selectIds.value = selectIds.value.filter(name => name !== row.paramName);
    }
  });
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.paramName === row.paramName);
    if (isSelected) {
      selectIds.value = [...new Set([...selectIds.value, row.paramName])];
    } else {
      selectIds.value = selectIds.value.filter(name => name !== row.paramName);
    }
  });
};
onMounted(() => {
  loadData();
  // addAllListeners();
});
const loadData = async () => {
  setTimeout(() => {
    tabledata.value = toRaw(resultTableValue.value);
    proTable.value?.refresh();
    const grpMaps = resultTableValue.value.map(itme => itme.grpName);
    const grps = [...new Set(grpMaps)];
    const grpInfos: GroupInfo[] = [];
    for (const ele of grps) {
      grpInfos.push({
        id: String(ele),
        grpname: String(ele)
      });
    }
    getTreeFilter(grpInfos);
    nextTick(() => {
      tabledata.value.forEach(row => {
        proTable.value?.element?.toggleRowSelection(row, selectIds.value.includes(row.paramName));
      });
    });
  }, 100);
};
// onMounted(() => {
//   loadData();
//   addAllListeners();
// });
// onBeforeUnmount(() => {
//   saveData();
//   removeAllListeners();
// });

// function removeAllListeners() {
//   window.removeEventListener("beforeunload", saveData);
// }

// function addAllListeners() {
//   window.addEventListener("beforeunload", saveData);
// }

// function saveData() {
//   console.log("============", selectIds);
//   localStorage.setItem("matirx.paramSelectIds", JSON.stringify(selectIds));
//   localStorage.setItem("matirx.paramData", JSON.stringify(resultTableValue));
// }

// function loadData() {
//   setTimeout(() => {
//     const tableValue = localStorage.getItem("matirx.paramData");
//     if (tableValue) {
//       resultTableValue = JSON.parse(tableValue);
//       tabledata.value = resultTableValue;
//       proTable.value?.refresh();
//       const grpMaps = resultTableValue.map(itme => itme.grpName);
//       const grps = [...new Set(grpMaps)];
//       const grpInfos: GroupInfo[] = [];
//       for (const ele of grps) {
//         grpInfos.push({
//           id: String(ele),
//           grpname: String(ele)
//         });
//       }
//       getTreeFilter(grpInfos);

//       const paramSelectIds = localStorage.getItem("matirx.paramSelectIds");
//       if (paramSelectIds) {
//         selectIds = JSON.parse(paramSelectIds);
//         console.log("get============", selectIds);
//         nextTick(() => {
//           tabledata.value.forEach(row => {
//             proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.paramName));
//           });
//         });
//       }
//     }
//   }, 100);
// }
</script>

<style scoped>
.main-box {
  display: flex;
  width: 100%;
  height: 70%;
  height: v-bind(tableContainerHeight);
}
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: v-bind(tableContainerHeight);
  margin-left: -8px;
  overflow: hidden;
}
.old-value {
  color: #f56c6c;
  text-decoration: line-through;
}
.value {
  font-weight: bold;
  color: #67c23a;
}
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}
:deep(.resizable-dialog) {
  display: flex;
  flex-direction: column;
  min-width: 400px;
  min-height: 200px;
  overflow: auto;
  resize: both;
}
.content {
  flex: 1;
  overflow: auto;
}
.drag-handle {
  /* 覆盖 CSS resize 的默认拖拽柄 */
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2000; /* 确保在最上层 */
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}
.header {
  margin-bottom: 8px;
}
</style>
