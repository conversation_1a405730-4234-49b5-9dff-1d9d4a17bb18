"use strict";

import IECCONSTANTS from "../../data/debug/iecConstants";
import { DebugDeviceInfo } from "../../interface/debug/debuginfo";
import { getUUID, deleteFolderRecursive } from "../../utils/common";
import path from "node:path";
import fs from 'fs';
import fsPromise from "node:fs/promises";
import { logger } from "ee-core/log";
import { ERROR_CODES } from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { t } from "../../data/i18n/i18n";

class DeviceOperateService {
  async addDeviceCfg(data: DebugDeviceInfo): Promise<ApiResponse> {
    logger.info(
      "[DeviceOperateService] addDeviceCfg 入参:",
      JSON.stringify(data)
    );
    try {
      logger.debug("[DeviceOperateService] addDeviceCfg - 开始新增");
      const datas = await this.getList();
      for (const item of datas) {
        if (item.ip == data.ip && item.port == data.port) {
          logger.warn(
            "[DeviceOperateService] addDeviceCfg " +
              t("deviceOperations.deviceIpPortExists")
          );
          return new ApiResponse(
            10001,
            t("deviceOperations.deviceIpPortExists")
          );
        }
      }
      // 设置id
      data.id = getUUID();
      datas.push(data);
      // 设置id
      data.id = getUUID();
      const cfgPath = path.join(IECCONSTANTS.PATH_DEVICE_CFG);
      const stat = await fsPromise.stat(IECCONSTANTS.PATH_CFG).catch((err) => {
        console.log(err);
      });
      if (!stat) {
        await fsPromise.mkdir(IECCONSTANTS.PATH_DEVICE_CFG, {
          recursive: true,
        });
      }
      await fsPromise.writeFile(cfgPath, JSON.stringify(datas));
      logger.info(
        "[DeviceOperateService] addDeviceCfg " +
          t("deviceOperations.addCompleted"),
        data
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        t("deviceOperations.addCompleted"),
        data
      );
    } catch (error) {
      logger.error("[DeviceOperateService] addDeviceCfg 异常:", error);
    }
    return new ApiResponse(10001, t("deviceOperations.addFailed"));
  }

  async getList(): Promise<DebugDeviceInfo[]> {
    logger.info("[DeviceOperateService] getList 调用");
    try {
      logger.debug("[DeviceOperateService] getList - 开始获取");
      const cfgPath = path.join(IECCONSTANTS.PATH_DEVICE_CFG);
      if (!fs.existsSync(cfgPath)) {
        const emptyCfg = [];
        await fsPromise.mkdir(IECCONSTANTS.PATH_CONFIG, {
          recursive: true,
        });
        fs.writeFileSync(cfgPath, JSON.stringify(emptyCfg));
      }
      const buffer = await fsPromise.readFile(cfgPath);
      const json: DebugDeviceInfo[] = JSON.parse(buffer.toString());
      logger.info("[DeviceOperateService] getList 获取完成");
      return json;
    } catch (err) {
      logger.error("[DeviceOperateService] getList 异常:", err);
    }
    return [];
  }
  async getById(id: string): Promise<DebugDeviceInfo | undefined> {
    logger.info("[DeviceOperateService] getById 入参:", id);
    try {
      logger.debug("[DeviceOperateService] getById - 开始查找");
      const list = await this.getList();
      for (const item of list) {
        if (item.id === id) {
          logger.info("[DeviceOperateService] getById 查找完成");
          return item;
        }
      }
      logger.info("[DeviceOperateService] getById 查找完成");
      return undefined;
    } catch (error) {
      logger.error("[DeviceOperateService] getById 异常:", error);
    }
    return undefined;
  }

  async updateDeviceCfg(data: DebugDeviceInfo): Promise<ApiResponse> {
    logger.info(
      "[DeviceOperateService] updateDeviceCfg 入参:",
      JSON.stringify(data)
    );
    try {
      logger.debug("[DeviceOperateService] updateDeviceCfg - 开始修改");
      const datas = await this.getList();
      // check
      for (const item of datas) {
        if (item.id != data.id) {
          if (item.ip == data.ip && item.port == data.port) {
            return new ApiResponse(
              10001,
              t("deviceOperations.deviceIpPortExists")
            );
          }
        }
      }

      let index = 0;
      for (const item of datas) {
        if (item.id == data.id) {
          datas[index] = data;
          break;
        }
        index++;
      }
      const cfgPath = path.join(IECCONSTANTS.PATH_DEVICE_CFG);
      await fsPromise.writeFile(cfgPath, JSON.stringify(datas));
      logger.info(
        "[DeviceOperateService] updateDeviceCfg " +
          t("deviceOperations.updateCompleted")
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        t("deviceOperations.updateCompleted")
      );
    } catch (error) {
      logger.error("[DeviceOperateService] updateDeviceCfg 异常:", error);
    }
    return new ApiResponse(10001, t("deviceOperations.updateFailed"));
  }

  async removeDeviceCfg(id: string): Promise<boolean> {
    logger.info("[DeviceOperateService] removeDeviceCfg 入参:", id);
    try {
      logger.debug("[DeviceOperateService] removeDeviceCfg - 开始删除");
      const datas = await this.getList();

      const tempDatas: DebugDeviceInfo[] = [];
      for (const item of datas) {
        if (item.id == id) {
          continue;
        }
        tempDatas.push(item);
      }
      // 无移除
      if (tempDatas.length == datas.length) {
        logger.info("[DeviceOperateService] removeDeviceCfg 删除完成");
        return true;
      }
      const cfgPath = path.join(IECCONSTANTS.PATH_DEVICE_CFG);
      await fsPromise.writeFile(cfgPath, JSON.stringify(tempDatas));

      // 删除装置对应的配置目录
      const deviceConfigDir = path.join(IECCONSTANTS.PATH_CONFIG, id);
      if (fs.existsSync(deviceConfigDir)) {
        try {
          deleteFolderRecursive(deviceConfigDir);
          logger.info(
            `[DeviceOperateService] removeDeviceCfg 删除装置配置目录: ${deviceConfigDir}`
          );
        } catch (error) {
          logger.error(
            `[DeviceOperateService] removeDeviceCfg 删除装置配置目录失败: ${deviceConfigDir}`,
            error
          );
        }
      }

      logger.info("[DeviceOperateService] removeDeviceCfg 删除完成");
      return true;
    } catch (error) {
      logger.error("[DeviceOperateService] removeDeviceCfg 异常:", error);
    }
    return false;
  }
}

DeviceOperateService.toString = () => "[class DeviceConnectService]";
const deviceOperateService = new DeviceOperateService();

export { DeviceOperateService, deviceOperateService };
