<template>
  <div ref="DeviceContainer" class="device-container">
    <div class="device-main flx-start" :style="deviceMainStyle.mainRoot">
      <div class="left-page" ref="leftPage" style="display: flex; flex-direction: column; height: 100%">
        <!-- 搜索与展开/收缩按钮区域 -->
        <div style="display: flex; gap: 4px; align-items: center; padding: 8px 8px 0; margin-bottom: 8px">
          <el-input
            v-model="treeFilterText"
            :placeholder="t('device.tree.inputGroupName')"
            size="small"
            clearable
            style="flex: 1"
            @input="filterTree"
          />
          <el-tooltip ref="expandTooltipRef" :content="isTreeAllExpanded ? t('device.tree.collapseAll') : t('device.tree.expandAll')" placement="top">
            <el-button :icon="isTreeAllExpanded ? Top : Bottom" size="small" circle @click="toggleExpandCollapse" />
          </el-tooltip>
        </div>
        <el-scrollbar style="flex: 1; min-height: 0">
          <el-tree
            ref="deviceTreeRef"
            default-expand-all
            :data="deviceTreeData"
            :highlight-current="true"
            :indent="20"
            :icon="ArrowRightBold"
            :accordion="false"
            node-key="id"
            :expand-on-click-node="false"
            @node-click="treeClick"
            @node-contextmenu="handleContextMenu"
            :filter-node-method="filterNode"
          >
            <template #default="{ data }">
              <span class="custom-tree-node flx-center">
                <svg-icon v-if="data.svgIcon" :icon="data.svgIcon" :class="[data.type == 'submenu' ? 'tree-submenu' : 'tree-menu']" />
                <span>
                  {{ data.label }}
                </span>
              </span>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
      <!-- 分隔线 -->
      <div ref="resizeVerticalRef" class="resize-vertical"></div>
      <div class="right-page" ref="rightPage">
        <DeviceInnerTabs ref="deviceInnerTabs" :device-id="props.deviceModel.id" />
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <v-contextmenu ref="contextmenu" class="custom-contextmenu">
    <v-contextmenu-item v-for="(item, index) in contextMenuData" :key="index" @click="contextMenuEvent(item.command)" class="custom-contextmenu-item">
      <svg-icon v-if="item.icon" :icon="item.icon" :style="{ marginRight: '6px', verticalAlign: 'middle', color: 'var(--el-color-primary)' }" />
      {{ item.label }}
    </v-contextmenu-item>
  </v-contextmenu>

  <!-- 新增/编辑 自定义组（点）弹窗 -->
  <AddCustomPointGroupDialog
    v-model:visible="pointGroupDialogVisible"
    v-model:is-edit="pointGroupIsEdit"
    v-model:model="pointGroupModel"
    @confirm="onPointGroupConfirm"
  />

  <!-- 自定义菜单弹窗 -->
  <el-dialog v-model="groupDialogVisible" width="500px">
    <template #title>
      <svg-icon icon="ep:folder" style="margin-right: 6px; color: var(--el-color-primary); vertical-align: middle" />
      {{ groupDialogTitle }}
    </template>
    <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="80px">
      <el-form-item :label="t('device.customMenu.menuName')" prop="name">
        <el-input v-model="groupForm.name" :placeholder="t('device.customMenu.inputMenuName')" />
      </el-form-item>
      <el-form-item :label="t('device.customMenu.menuDesc')" prop="desc">
        <el-input v-model="groupForm.desc" type="textarea" :placeholder="t('device.customMenu.inputMenuDesc')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="groupDialogVisible = false">{{ t("device.customMenu.cancel") }}</el-button>
      <el-button type="primary" @click="submitGroup">{{ t("device.customMenu.confirm") }}</el-button>
    </template>
  </el-dialog>

  <!-- 自定义报告弹窗 -->
  <el-dialog v-model="reportDialogVisible" width="600px">
    <template #title>
      <svg-icon icon="ep:document" style="margin-right: 6px; color: var(--el-color-primary); vertical-align: middle" />
      {{ reportDialogTitle }}
    </template>
    <el-form :model="reportForm" :rules="reportRules" ref="reportFormRef" label-width="100px">
      <el-form-item :label="t('device.customMenu.reportName')" prop="newname">
        <el-input v-model="reportForm.newname" :placeholder="t('device.customMenu.inputReportName')" />
      </el-form-item>
      <el-form-item :label="t('device.customMenu.reportDesc')" prop="desc">
        <el-input v-model="reportForm.desc" type="textarea" :placeholder="t('device.customMenu.inputReportDesc')" />
      </el-form-item>
      <el-form-item :label="t('device.customMenu.reportKeyword')" prop="keyword">
        <el-input v-model="reportForm.keyword" :placeholder="t('device.customMenu.inputReportKeyword')" />
      </el-form-item>
      <el-form-item :label="t('device.customMenu.reportInherit')" prop="inherit">
        <el-select
          v-model="reportForm.inherit"
          :placeholder="t('device.customMenu.selectReportInherit')"
          @change="onInheritChange"
          style="width: 100%"
        >
          <el-option v-for="item in lgReportList" :key="item.name" :label="item.desc" :value="item.name" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="reportDialogVisible = false">{{ t("device.customMenu.cancel") }}</el-button>
      <el-button type="primary" @click="submitReport">{{ t("device.customMenu.confirm") }}</el-button>
    </template>
  </el-dialog>

  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts">
// import ParamSetting from "./ParamSetting.vue";

// import Message from "@/scripts/message";
import { useGlobalStore } from "@/stores/modules";
import { ArrowRightBold, Top, Bottom } from "@element-plus/icons-vue";
import { ElTree } from "element-plus";
import DeviceInnerTabs from "@/views/biz/debug/device/components/DeviceInnerTabs.vue";
import { useDebugStore } from "@/stores/modules/debug";
import { useResizeVertical } from "@/scripts/resizeVertical";
import { deviceInfoMenutreeApi } from "@/api/modules/biz/debug";
import { customInfoApi, type CustomGroup, type CustomReport, type LGReport } from "@/api/modules/biz/debug/custominfo";
import { DebugInfoMenu } from "@/api/interface/biz/debug/debuginfo";
import { ResultData } from "@/api";
import { DebugDeviceInfo } from "@/stores/interface";
import { getComponentByMenuName } from "@/utils/iec/iecRpcUtils";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getUUID } from "@/utils/index";
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { useI18n } from "vue-i18n";
import AddCustomPointGroupDialog from "../dialog/AddCustomPointGroupDialog.vue";
const { t, locale } = useI18n();
const progressDialog = ref();
const { debugIndex, currDevice, report } = useDebugStore();
const leftPage = ref();
const rightPage = ref();
const deviceInnerTabs = ref<InstanceType<typeof DeviceInnerTabs>>();
const resizeVerticalRef = ref();
const expandTooltipRef = ref();

// 右键菜单相关
const contextmenu = ref();
const contextMenuData = ref<{ label: string; command: string; icon: string }[]>([]);
const currentNode = ref<any>(null);

// 自定义菜单弹窗相关
const groupDialogVisible = ref(false);
const groupDialogTitle = ref(t("device.customMenu.addMenu"));
const groupForm = ref<{ name: string; desc: string; uuid?: string; fc?: string }>({ name: "", desc: "" });
const groupFormRef = ref();
const groupRules = {
  name: [{ required: true, message: t("device.customMenu.inputMenuName"), trigger: ["blur", "change"] }],
  desc: [{ required: true, message: t("device.customMenu.inputMenuDesc"), trigger: ["blur", "change"] }]
};

// 自定义报告弹窗相关
const reportDialogVisible = ref(false);
const reportDialogTitle = ref(t("device.customMenu.addReport"));
const reportForm = ref<CustomReport & { newname?: string }>({
  uuid: "",
  name: "",
  newname: "",
  desc: "",
  keyword: "",
  inherit: "",
  fc: "",
  method: ""
});
const reportFormRef = ref();
const reportRules = {
  newname: [{ required: true, message: t("device.customMenu.inputReportName"), trigger: ["blur", "change"] }],
  desc: [{ required: true, message: t("device.customMenu.inputReportDesc"), trigger: ["blur", "change"] }],
  // keyword 允许为空，代表不需要按照关键字过滤
  inherit: [{ required: true, message: t("device.customMenu.selectReportInherit"), trigger: ["blur", "change"] }]
};
const lgReportList = ref<LGReport[]>([]);
const currentGroupName = ref("");
const currentGroupUuid = ref("");

// 树搜索相关
const treeFilterText = ref("");

// 动态判断树是否全部展开
const isTreeAllExpanded = computed(() => {
  if (!deviceTreeRef.value) return false;
  const check = nodes => {
    for (const node of nodes) {
      const treeNode = deviceTreeRef.value!.store.nodesMap[node.id];
      if (treeNode && treeNode.childNodes && treeNode.childNodes.length > 0) {
        if (!treeNode.expanded) return false;
        if (!check(node.children || [])) return false;
      }
    }
    return true;
  };
  return check(deviceTreeData.value[0]?.children || []);
});

interface DeviceTree {
  id: number;
  label: string;
  name?: string;
  children?: Array<{
    label: string;
    type?: string;
    pname?: string;
    svgIcon?: string;
    component?: any;
  }>;
  type?: string;
  svgIcon?: string;
  component?: string;
}
useResizeVertical(leftPage, rightPage, resizeVerticalRef, undefined, {
  persistent: true,
  keyOne: "deviceComponetLeft",
  keyTwo: "deviceComponetRight",
  defaultOne: "220",
  defaultTwo: "",
  maxOne: 300,
  minOne: 200
});
const deviceTreeRef = ref<InstanceType<typeof ElTree>>();
const globalStore = useGlobalStore();
const deviceTreeData = ref<DeviceTree[]>([
  {
    id: 0,
    label: t("device.groupInfo.title"),
    name: "Root",
    children: [],
    type: "root",
    component: "DeviceSummary",
    svgIcon: "zondicons:load-balancer"
  }
]);
// 监听语言切换，动态刷新根节点 label
watch(locale, () => {
  deviceTreeData.value[0].label = t("device.groupInfo.title");
});
// 示例菜单数据，已注释
// const getTreeScrollbar = () => {
//   let offset = 0;
//   if (globalStore.tabs) {
//     offset += 40;
//   }
//   if (globalStore.footer) {
//     offset += 30;
//   }
//   if (globalStore.isConsole) {
//     offset += 225;
//   }
//   return `calc(100vh - ${offset}px)`;
// };

const treeClick = async data => {
  console.log(t("device.treeClickLog"), data);
  const component = getComponentByMenuName(data.name, data.children && data.children.length > 0, data.fc, data.method);
  const deviceId = props.deviceModel.id;
  const globalReport = report.get(deviceId);
  if (data.fc === "LG" && globalReport) {
    // 设置当前操作的报告
    const oldNewname = globalReport.newname;
    globalReport.currReportType = data.name;
    globalReport.currReportMethod = data.method;
    globalReport.currReportDesc = data.label;
    globalReport.newname = data.newname || data.name; // 如果newname为空，使用name作为fallback
    globalReport.keyword = data.keyword;

    // 调试日志：检查newname变化
    console.log("报告切换:", {
      oldNewname,
      newNewname: globalReport.newname,
      reportType: data.name,
      reportDesc: data.label,
      dataNewname: data.newname
    });
  }
  debugIndex.currentComponent.set(deviceId, component);
  debugIndex.compData.set(deviceId, data);
  if (deviceInnerTabs.value) {
    deviceInnerTabs.value.changeTab(t("device.innerTabs.contentView"), data.label);
  }
  if (deviceInnerTabs.value) {
    await deviceInnerTabs.value.changeComponent();
  }
};

// 右键菜单处理
const handleContextMenu = (event: any, data: any, node: any) => {
  event.preventDefault();
  currentNode.value = { data, node };

  if (data.type === "root") {
    contextMenuData.value = [{ label: t("device.customMenu.addMenu"), command: "addMenu", icon: "ep:folder-add" }];
  } else if (data.type === "customGroup") {
    contextMenuData.value = [
      { label: t("device.customMenu.addReport"), command: "addReport", icon: "ep:document-add" },
      { label: t("device.customMenu.addPointGroup"), command: "addPointGroup", icon: "ep:collection" },
      { label: t("device.customMenu.editMenu"), command: "editMenu", icon: "ep:edit" },
      { label: t("device.customMenu.deleteMenu"), command: "deleteMenu", icon: "ep:delete" }
    ];
  } else if (data.type === "customReport") {
    contextMenuData.value = [
      { label: t("device.customMenu.editReport"), command: "editReport", icon: "ep:edit" },
      { label: t("device.customMenu.deleteReport"), command: "deleteReport", icon: "ep:delete" }
    ];
  } else if (data.type === "customPointGroup") {
    contextMenuData.value = [
      { label: t("device.customMenu.editPointGroup"), command: "editPointGroup", icon: "ep:edit" },
      { label: t("device.customMenu.deletePointGroup"), command: "deletePointGroup", icon: "ep:delete" }
    ];
  } else {
    // 普通菜单节点不显示任何右键菜单选项
    return;
  }

  contextmenu.value.show(event);
};

// 右键菜单事件处理
const contextMenuEvent = async (command: string) => {
  contextmenu.value.hide();

  switch (command) {
    case "addPointGroup":
      showPointGroupDialog();
      break;
    case "editPointGroup":
      showPointGroupDialog(true);
      break;
    case "addMenu":
      showGroupDialog();
      break;
    case "editMenu":
      showGroupDialog(true);
      break;
    case "deleteMenu":
      deleteMenu();
      break;
    case "addReport":
      showReportDialog();
      break;
    case "editReport":
      showReportDialog(true);
      break;
    case "deleteReport":
      deleteReport();
      break;
    case "deletePointGroup":
      deletePointGroup();
      break;
  }
};

// 显示自定义菜单弹窗
const showGroupDialog = (isEdit = false) => {
  if (isEdit) {
    groupDialogTitle.value = t("device.customMenu.editMenu");
    const groupData = currentNode.value.data;
    groupForm.value = {
      name: groupData.name,
      desc: groupData.desc || "",
      uuid: groupData.uuid,
      fc: groupData.fc
    };
  } else {
    groupDialogTitle.value = t("device.customMenu.addMenu");
    groupForm.value = { name: "", desc: "", uuid: getUUID(), fc: "customGroup" };
  }
  groupDialogVisible.value = true;
};

// 提交自定义菜单
const submitGroup = async () => {
  console.log("submitGroup - 开始提交自定义菜单");
  console.log("submitGroup - 当前表单数据:", groupForm.value);
  console.log("submitGroup - 表单引用:", groupFormRef.value);

  // 检查表单引用是否存在
  if (!groupFormRef.value) {
    console.error("submitGroup - 表单引用不存在");
    ElMessage.error("表单初始化失败，请重试");
    return;
  }

  // 手动检查必填字段
  const formData = groupForm.value;
  console.log("submitGroup - 手动检查表单数据:", formData);

  if (!formData.name || formData.name.trim() === "") {
    console.error("submitGroup - 菜单名称为空");
    ElMessage.error("请输入菜单名称");
    return;
  }

  if (!formData.desc || formData.desc.trim() === "") {
    console.error("submitGroup - 菜单描述为空");
    ElMessage.error("请输入菜单描述");
    return;
  }

  // 使用回调函数方式进行表单校验（作为双重保险）
  groupFormRef.value.validate(async (valid: boolean, fields: any) => {
    console.log("submitGroup - Element Plus 表单校验结果:", { valid, fields });

    if (!valid) {
      console.error("自定义菜单表单校验失败:", fields);
      ElMessage.error("请填写完整的表单信息");
      return; // 校验失败，不关闭对话框
    }

    console.log("submitGroup - 所有校验通过，继续处理");

    try {
      const isEdit = groupDialogTitle.value === t("device.customMenu.editMenu");
      const group: CustomGroup = {
        name: groupForm.value.name, // 保留原始名称
        newname: groupForm.value.name, // 用户输入的名称作为显示名称
        desc: groupForm.value.desc,
        reports: isEdit ? JSON.parse(JSON.stringify(currentNode.value.data.reports || [])) : [],
        uuid: groupForm.value.uuid || getUUID(),
        fc: groupForm.value.fc || "customGroup",
        keyword: undefined,
        inherit: undefined
      };

      // 提交到后端
      if (isEdit) {
        const result = await customInfoApi.editMenuByDevice(currDevice.id, currentNode.value.data.uuid, group);
        if (result.code === 0) {
          ElMessage.success(t("device.customMenu.successEditMenu"));
        } else {
          console.error("编辑自定义菜单失败:", result.msg);
          ElMessage.error(`编辑失败: ${result.msg}`);
          return; // 失败时不关闭对话框
        }
      } else {
        const result = await customInfoApi.addMenuByDevice(currDevice.id, group);
        if (result.code === 0) {
          ElMessage.success(t("device.customMenu.successAddMenu"));
        } else {
          console.error("创建自定义菜单失败:", result.msg);
          ElMessage.error(`创建失败: ${result.msg}`);
          return; // 失败时不关闭对话框
        }
      }

      // 只有在成功提交后才关闭对话框
      groupDialogVisible.value = false;
      await refreshCustomGroups();
    } catch (error) {
      // API调用失败时，不关闭对话框
      console.error("提交自定义菜单失败:", error);
      if (error && typeof error === "object" && "message" in error) {
        ElMessage.error((error as any).message);
      } else {
        ElMessage.error(t("device.customMenu.errorAction"));
      }
    }
  });
};

// 删除自定义菜单
const deleteMenu = async () => {
  try {
    await ElMessageBox.confirm(t("device.customMenu.confirmDeleteMenu"), t("device.customMenu.tip"), {
      confirmButtonText: t("device.customMenu.confirm"),
      cancelButtonText: t("device.customMenu.cancel"),
      type: "warning"
    });
    const result = await customInfoApi.deleteMenuByDevice(currDevice.id, currentNode.value.data.uuid);
    if (result.code === 0) {
      ElMessage.success(t("device.customMenu.successDeleteMenu"));
      await refreshCustomGroups();
    } else {
      console.error("删除自定义菜单失败:", result.msg);
      ElMessage.error(`删除失败: ${result.msg}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除自定义菜单失败:", error);
      ElMessage.error(t("device.customMenu.errorDelete"));
    }
  }
};

// 显示自定义报告弹窗
const showReportDialog = async (isEdit = false) => {
  if (isEdit) {
    reportDialogTitle.value = t("device.customMenu.editReport");
    const reportData = currentNode.value.data;
    reportForm.value = {
      uuid: reportData.uuid || "",
      name: reportData.name || "",
      newname: reportData.newname || reportData.name || "",
      desc: reportData.desc || "",
      keyword: reportData.keyword || "",
      inherit: reportData.name || "",
      fc: reportData.fc || "",
      method: reportData.method || ""
    };
    currentGroupName.value = currentNode.value.node.parent.data.label;
    currentGroupUuid.value = currentNode.value.node.parent.data.uuid;
  } else {
    reportDialogTitle.value = t("device.customMenu.addReport");
    reportForm.value = {
      uuid: getUUID(),
      name: "",
      newname: "",
      desc: "",
      keyword: "",
      inherit: "",
      fc: "",
      method: ""
    };
    currentGroupName.value = currentNode.value.data.label;
    currentGroupUuid.value = currentNode.value.data.uuid;
  }

  // 加载LG报告列表
  if (lgReportList.value.length === 0) {
    try {
      const res = await customInfoApi.getLGReportsByDevice(currDevice.id);
      if (res.code === 0) {
        lgReportList.value = res.data || [];
      }
    } catch (error) {
      console.error("获取LG报告失败:", error);
    }
  }

  reportDialogVisible.value = true;
};

// 继承报告选择变化
const onInheritChange = (value: string) => {
  const selectedReport = lgReportList.value.find(item => item.name === value);
  if (selectedReport) {
    reportForm.value.fc = selectedReport.fc;
    reportForm.value.method = selectedReport.method;
    reportForm.value.name = selectedReport.name;
  }
};

// 提交自定义报告
const submitReport = async () => {
  console.log("submitReport - 开始提交自定义报告");
  console.log("submitReport - 当前表单数据:", reportForm.value);

  // 手动检查必填字段
  const formData = reportForm.value;

  if (!formData.newname || formData.newname.trim() === "") {
    console.error("submitReport - 报告名称为空");
    ElMessage.error("请输入报告名称");
    return;
  }

  if (!formData.desc || formData.desc.trim() === "") {
    console.error("submitReport - 报告描述为空");
    ElMessage.error("请输入报告描述");
    return;
  }

  if (!formData.inherit || formData.inherit.trim() === "") {
    console.error("submitReport - 继承字段为空");
    ElMessage.error("请选择继承字段");
    return;
  }

  // 使用回调函数方式进行表单校验（作为双重保险）
  reportFormRef.value?.validate(async (valid: boolean, fields: any) => {
    console.log("submitReport - Element Plus 表单校验结果:", { valid, fields });

    if (!valid) {
      console.error("自定义报告表单校验失败:", fields);
      ElMessage.error("请填写完整的表单信息");
      return; // 校验失败，不关闭对话框
    }

    console.log("submitReport - 所有校验通过，继续处理");

    try {
      const isEdit = reportDialogTitle.value === t("device.customMenu.editReport");
      const report: CustomReport & { newname?: string } = {
        uuid: reportForm.value.uuid,
        name: reportForm.value.name,
        newname: reportForm.value.newname,
        desc: reportForm.value.desc,
        keyword: reportForm.value.keyword,
        inherit: reportForm.value.inherit,
        fc: reportForm.value.fc,
        method: reportForm.value.method
      };

      // 提交到后端
      if (isEdit) {
        const result = await customInfoApi.editReportByDevice(currDevice.id, currentGroupUuid.value, currentNode.value.data.uuid, report);
        if (result.code === 0) {
          ElMessage.success(t("device.customMenu.successEditReport"));
        } else {
          console.error("编辑自定义报告失败:", result.msg);
          ElMessage.error(`编辑失败: ${result.msg}`);
          return; // 失败时不关闭对话框
        }
      } else {
        const result = await customInfoApi.addReportByDevice(currDevice.id, currentGroupUuid.value, report);
        if (result.code === 0) {
          ElMessage.success(t("device.customMenu.successAddReport"));
        } else {
          console.error("创建自定义报告失败:", result.msg);
          ElMessage.error(`创建失败: ${result.msg}`);
          return; // 失败时不关闭对话框
        }
      }

      // 只有在成功提交后才关闭对话框
      reportDialogVisible.value = false;
      await refreshCustomGroups();
    } catch (error) {
      // API调用失败时，不关闭对话框
      console.error("提交自定义报告失败:", error);
      if (error && typeof error === "object" && "message" in error) {
        ElMessage.error((error as any).message);
      } else {
        ElMessage.error(t("device.customMenu.errorAction"));
      }
    }
  });
};

// 删除自定义报告
const deleteReport = async () => {
  try {
    await ElMessageBox.confirm(t("device.customMenu.confirmDeleteReport"), t("device.customMenu.tip"), {
      confirmButtonText: t("device.customMenu.confirm"),
      cancelButtonText: t("device.customMenu.cancel"),
      type: "warning"
    });

    const groupUuid = currentNode.value.node.parent.data.uuid;
    const reportUuid = currentNode.value.data.uuid;
    const result = await customInfoApi.deleteReportByDevice(currDevice.id, groupUuid, reportUuid);
    if (result.code === 0) {
      ElMessage.success(t("device.customMenu.successDeleteReport"));
      await refreshCustomGroups();
    } else {
      console.error("删除自定义报告失败:", result.msg);
      ElMessage.error(`删除失败: ${result.msg}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除自定义报告失败:", error);
      ElMessage.error(t("device.customMenu.errorDelete"));
    }
  }
};

// 删除自定义点组
const deletePointGroup = async () => {
  try {
    await ElMessageBox.confirm(t("device.customMenu.confirmDeletePointGroup"), t("device.customMenu.tip"), {
      confirmButtonText: t("device.customMenu.confirm"),
      cancelButtonText: t("device.customMenu.cancel"),
      type: "warning"
    });

    const groupUuid = currentNode.value.node.parent.data.uuid;
    const pointGroupUuid = currentNode.value.data.uuid;
    // 自定义点组也是作为report存储的，所以使用相同的删除API
    const result = await customInfoApi.deleteReportByDevice(currDevice.id, groupUuid, pointGroupUuid);
    if (result.code === 0) {
      ElMessage.success(t("device.customMenu.successDeletePointGroup"));
      await refreshCustomGroups();
    } else {
      console.error("删除自定义点组失败:", result.msg);
      ElMessage.error(`删除失败: ${result.msg}`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除自定义点组失败:", error);
      ElMessage.error(t("device.customMenu.errorDelete"));
    }
  }
};

// 自定义组（点）弹窗
const pointGroupDialogVisible = ref(false);
const pointGroupIsEdit = ref(false);
const pointGroupModel = ref<CustomGroup | null>(null);
const showPointGroupDialog = async (isEdit = false) => {
  pointGroupIsEdit.value = isEdit;
  if (isEdit) {
    // 获取完整的组信息（包含 items）
    try {
      const res = await customInfoApi.getAllGroupsByDevice(currDevice.id);
      if (res.code === 0 && Array.isArray(res.data?.menus)) {
        const uuid = currentNode.value?.data?.uuid;
        const group = res.data.menus.find(g => g.uuid === uuid);
        pointGroupModel.value = group || currentNode.value?.data || null;
      } else {
        pointGroupModel.value = currentNode.value?.data || null;
      }
    } catch (e) {
      pointGroupModel.value = currentNode.value?.data || null;
    }
  } else {
    pointGroupModel.value = null;
  }
  pointGroupDialogVisible.value = true;
};
const onPointGroupConfirm = async (group: CustomGroup, isEdit: boolean) => {
  try {
    console.log("Device - onPointGroupConfirm 开始:", {
      group,
      isEdit,
      deviceId: currDevice.id,
      itemsCount: group.items?.length || 0,
      parentNode: currentNode.value?.data
    });

    // 验证数据完整性
    if (!group.name || !group.desc) {
      console.error("Device - 数据验证失败: 缺少必要字段");
      ElMessage.error("数据不完整，请检查组名和描述");
      return;
    }

    if (!group.items || group.items.length === 0) {
      console.error("Device - 数据验证失败: 没有选择任何点");
      ElMessage.error("请至少选择一个点");
      return;
    }

    // 新增模式：在当前菜单下添加自定义点组
    if (!isEdit) {
      console.log("Device - 新增模式：在当前菜单下添加自定义点组");

      // 验证必须在自定义菜单节点下创建
      if (currentNode.value?.data?.type !== "customGroup") {
        console.error("Device - 只能在自定义菜单节点下创建自定义点组");
        ElMessage.error("只能在自定义菜单节点下创建自定义点组");
        return;
      }

      // 创建点组作为报告添加到当前菜单
      const pointGroupReport = {
        uuid: group.uuid,
        name: group.name,
        newname: group.newname || group.name,
        desc: group.desc,
        fc: group.fc || "SP", // 确保 fc 不为 undefined
        method: "", // 点组不需要方法，设为空字符串
        keyword: group.keyword || "",
        inherit: group.inherit || "",
        items: group.items || []
      };

      console.log("Device - 准备调用 addReportByDevice API");
      console.log("Device - 父菜单UUID:", currentNode.value.data.uuid);
      console.log("Device - 点组数据:", pointGroupReport);

      try {
        const result = await customInfoApi.addReportByDevice(currDevice.id, currentNode.value.data.uuid, pointGroupReport);
        console.log("Device - API调用完成，结果:", result);

        if (result.code === 0) {
          console.log("Device - 创建成功，显示成功消息");
          ElMessage.success("成功创建自定义点组");
          await refreshCustomGroups();
          pointGroupDialogVisible.value = false;
          console.log("Device - 新增自定义点组完成");
          return;
        } else {
          console.error("Device - 创建失败，显示错误消息");
          console.error("Device - 错误码:", result.code);
          console.error("Device - 错误信息:", result.msg);
          ElMessage.error(`创建失败: ${result.msg}`);
          return;
        }
      } catch (error) {
        console.error("Device - API调用发生异常:", error);
        ElMessage.error("创建失败，请重试");
        return;
      }
    }

    // 编辑模式：编辑现有的自定义点组
    console.log("Device - 编辑模式：编辑现有的自定义点组");

    // 验证当前节点是点组类型
    if (currentNode.value?.data?.type !== "customPointGroup") {
      console.error("Device - 编辑模式只能用于点组节点");
      ElMessage.error("编辑模式只能用于点组节点");
      return;
    }

    // 获取父菜单的UUID
    const parentMenuUuid = currentNode.value.node.parent?.data?.uuid;
    if (!parentMenuUuid) {
      console.error("Device - 找不到父菜单UUID");
      ElMessage.error("找不到父菜单信息");
      return;
    }

    // 创建更新的点组数据
    const updatedPointGroup = {
      uuid: group.uuid,
      name: group.name,
      newname: group.newname || group.name,
      desc: group.desc,
      fc: group.fc || "SP", // 确保 fc 不为 undefined
      method: "", // 点组不需要方法，设为空字符串
      keyword: group.keyword || "",
      inherit: group.inherit || "",
      items: group.items || []
    };

    console.log("Device - 编辑点组数据:", {
      parentMenuUuid,
      pointGroupUuid: group.uuid,
      updatedData: updatedPointGroup
    });

    try {
      const result = await customInfoApi.editReportByDevice(currDevice.id, parentMenuUuid, group.uuid, updatedPointGroup);
      console.log("Device - 编辑点组API调用完成，结果:", result);

      if (result.code === 0) {
        console.log("Device - 编辑成功，显示成功消息");
        ElMessage.success("成功编辑自定义点组");
        await refreshCustomGroups();
        pointGroupDialogVisible.value = false;
        console.log("Device - 编辑自定义点组完成");
        return;
      } else {
        console.error("Device - 编辑失败，显示错误消息");
        console.error("Device - 错误码:", result.code);
        console.error("Device - 错误信息:", result.msg);
        ElMessage.error(`编辑失败: ${result.msg}`);
        return;
      }
    } catch (error) {
      console.error("Device - 编辑点组API调用发生异常:", error);
      ElMessage.error("编辑失败，请重试");
      return;
    }
  } catch (e: any) {
    // 处理失败时不关闭对话框
    console.error("Device - onPointGroupConfirm 错误:", e);
    ElMessage.error(`操作失败: ${e?.message || e}`);
  }
};

// 刷新自定义菜单数据
const refreshCustomGroups = async () => {
  try {
    console.log("Device - refreshCustomGroups 开始刷新自定义菜单");
    const res = await customInfoApi.getAllGroupsByDevice(currDevice.id);
    console.log("Device - getAllGroupsByDevice 响应:", res);

    if (res.code === 0) {
      const groups = Array.isArray(res.data?.menus) ? res.data.menus : [];
      console.log("Device - 获取到的自定义组数据:", groups);

      const customGroups = groups.map(group => ({
        id: group.uuid,
        label: group.desc,
        name: group.name,
        newname: group.newname || group.name,
        type: "customGroup",
        uuid: group.uuid,
        svgIcon: getIconByFc("customGroup"),
        desc: group.desc,
        fc: "customGroup",
        reports: Array.isArray(group.reports) ? group.reports : [],
        items: Array.isArray(group.items) ? group.items : [], // 添加点组数据
        children: (Array.isArray(group.reports) ? group.reports : []).map(report => ({
          id: report.uuid,
          label: report.desc,
          name: report.name,
          newname: report.newname || report.name,
          type: report.items && report.items.length > 0 ? "customPointGroup" : "customReport", // 区分自定义点组和自定义报告
          svgIcon: getIconByFc(report.fc || ""),
          desc: report.desc,
          uuid: report.uuid,
          fc: report.fc,
          method: report.method,
          keyword: report.keyword,
          inherit: report.inherit,
          items: report.items || [] // 添加items数据
        }))
      }));

      // 更新树数据中的自定义菜单 - 直接添加到根节点下
      console.log("Device - 开始更新自定义菜单到树结构");

      // 清理现有的自定义菜单
      if (deviceTreeData.value[0]?.children) {
        deviceTreeData.value[0].children = deviceTreeData.value[0].children.filter((child: any) => child.type !== "customGroup");
      }

      // 确保根节点有children数组
      if (!deviceTreeData.value[0]?.children) {
        deviceTreeData.value[0].children = [];
      }

      // 将所有自定义菜单添加到根节点下
      deviceTreeData.value[0].children.push(...customGroups);
      console.log(`Device - 已添加 ${customGroups.length} 个自定义菜单到根节点`);

      console.log("Device - 自定义组更新完成，当前树结构:", deviceTreeData.value);
      console.log("Device - refreshCustomGroups 完成");
    } else {
      console.error("Device - getAllGroupsByDevice 返回错误:", res.msg);
    }
  } catch (error) {
    console.error("刷新自定义菜单失败:", error);
  }
};

const deviceMainStyle = computed<any>(() => {
  let offset = 0;
  if (globalStore.tabs) {
    offset += 40;
  }
  if (globalStore.footer) {
    offset += 30;
  }
  if (globalStore.isConsole) {
    offset += globalStore.consoleHeight + 1; // 使用动态控制台高度，+1是为了边框
  }
  return { mainRoot: { height: `calc(100vh - 133px - ${offset}px)` } };
});

const props = defineProps<{
  deviceModel: DebugDeviceInfo;
}>();

// 全局点击事件处理函数
const handleGlobalClick = () => {
  if (contextmenu.value && contextmenu.value.visible) {
    contextmenu.value.hide();
  }
};

onMounted(() => {
  initCurrentTree();
  nextTick(() => {
    deviceTreeRef.value?.setCurrentKey(0);
  });
  // 点击其他区域关闭右键菜单
  document.addEventListener("click", handleGlobalClick);
});

onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener("click", handleGlobalClick);
});

const initCurrentTree = async () => {
  progressDialog.value?.show();
  try {
    if (!props.deviceModel.id) {
      useDebugStore().addConsole(t("device.emptyDeviceId"));
      return;
    }
    const res: ResultData<DebugInfoMenu[]> = await deviceInfoMenutreeApi.getDeviceMenuTree(props.deviceModel.id);
    if (!res.data || !res.data) {
      throw new Error(t("device.invalidResponseStructure"));
    }
    const menus = res.data;
    const formattedMenuData = traverseMenu(menus);
    console.log(t("device.formattedMenuDataLog"), formattedMenuData);
    if (!deviceTreeData.value[0].children) {
      deviceTreeData.value[0].children = [];
    }
    for (const menusFirst of formattedMenuData) {
      const menuName = menusFirst.name;
      if (menuName == "SETTING") {
        let hasSG = traverseChildren(menusFirst.children);
        if (hasSG) {
          const settingMenu = {
            id: 999,
            label: t("device.allEditSpSettings"),
            fc: "ALL_SP",
            name: "ALLSETTING_TABLE",
            pname: menuName,
            type: "submenu",
            svgIcon: "eva:cube-fill",
            data: undefined,
            component: "ItemIndex"
          };
          menusFirst.children?.unshift(settingMenu);
          const allEditSettingMenu = {
            id: 998,
            label: t("device.allEditSgSettings"),
            fc: "ALL_SG",
            name: "ALLEDITSETTING_TABLE",
            pname: menuName,
            type: "submenu",
            svgIcon: "eva:cube-fill",
            data: undefined,
            component: "ItemIndex"
          };
          menusFirst.children?.splice(1, 0, allEditSettingMenu);
        } else {
          const settingMenu = {
            id: 999,
            label: t("device.allSettings"),
            fc: "ALL_SP",
            name: "ALLSETTING_TABLE",
            pname: menuName,
            type: "submenu",
            svgIcon: "eva:cube-fill",
            data: undefined,
            component: "ItemIndex"
          };
          menusFirst.children?.unshift(settingMenu);
        }
      }
    }

    deviceTreeData.value[0].children.push(...formattedMenuData);

    // 加载自定义菜单数据
    await refreshCustomGroups();

    console.log(t("device.deviceTreeDataLog"), JSON.stringify(deviceTreeData.value[0].children));
  } catch (error) {
    console.error(t("device.failedToLoadMenu"), error);
  } finally {
    progressDialog.value?.hide();
  }
};

// 递归遍历menusFirst的children，判断是否存在SG
function traverseChildren(children) {
  if (!children || !Array.isArray(children)) return false;
  let hasSG = false;
  for (const child of children) {
    if (child.fc === "SG") {
      hasSG = true;
    }
    // 递归遍历子节点
    if (child.children && child.children.length > 0) {
      if (traverseChildren(child.children)) {
        hasSG = true;
      }
    }
  }
  return hasSG;
}

function getIconByFc(fc: string) {
  switch (fc) {
    case "LG": // 日志
      return "ep:document";
    case "SG": // 定值组
      return "ep:collection-tag";
    case "ALL_SP": // 全部定值
      return "ep:star";
    case "ALL_SG": // 全部定值组
      return "ep:star-filled";
    case "SP": // 参数
      return "ep:cpu";
    case "ST": // 状态量
      return "ep:bell";
    case "MX": // 模拟量
      return "ep:trend-charts";
    case "CI": // 电度量（遥脉）
      return "ep:odometer";
    case "DC": // 描述
      return "ep:info-filled";
    case "CO": // 控制
      return "ep:switch-button";
    case "AO": // 遥调
      return "ep:aim";
    case "BO": // 开出传动
      return "ep:box";
    case "EX": // 扩展
      return "ep:expand";
    case "CF": // 配置
      return "ep:setting";
    case "AC": // 精度系数
      return "ep:scale-to-original";
    case "root":
      return "ep:menu";
    case "customGroup":
      return "ep:collection"; // 自定义菜单用更现代的文件夹图标
    default:
      return "ep:menu";
  }
}

function traverseMenu(menus, parentId = "", pname = "Root") {
  // 需要排除的LG+method
  const excludeLGMethods = ["QueryHisEvtByTime", "QueryHisFaultByTime", "QueryOpReportByTime", "QueryAuditLogByTime"];
  return menus
    .filter(menu => {
      // 如果是LG且method不在指定列表，则过滤掉
      if (menu.fc === "LG" && !excludeLGMethods.includes(menu.method)) {
        return false;
      }
      return true;
    })
    .map((menu, index) => {
      const currentId = parentId ? `${parentId}.${index + 1}` : `${index + 1}`;
      let menuItem = {
        id: currentId,
        label: menu.desc,
        name: menu.name,
        fc: menu.fc,
        type: "menu",
        pname: pname,
        method: menu.method,
        svgIcon: getIconByFc(menu.fc),
        component: "DeviceInfo",
        children: []
      };
      if (menu.menus && menu.menus.length > 0) {
        menuItem.children = traverseMenu(menu.menus, currentId, menu.name);
      } else {
        menuItem.type = "submenu";
        menuItem.svgIcon = getIconByFc(menu.fc);
        menuItem.component = "ItemIndex";
      }
      return menuItem;
    });
}

// 过滤树节点
const filterTree = () => {
  if (!deviceTreeRef.value) return;
  deviceTreeRef.value!.filter(treeFilterText.value);
  // 搜索时自动展开所有包含匹配节点的分支
  if (treeFilterText.value) {
    expandAll();
  }
};

// 树节点过滤方法
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  const val = value.toLowerCase();
  return (data.label && data.label.toLowerCase().includes(val)) || (data.name && data.name.toLowerCase().includes(val));
};

// 切换展开/收缩
const toggleExpandCollapse = () => {
  // 立即隐藏tooltip
  if (expandTooltipRef.value) {
    expandTooltipRef.value.hide();
  }
  if (isTreeAllExpanded.value) {
    collapseAll();
  } else {
    expandAll();
  }
};

// 全部展开
const expandAll = () => {
  if (!deviceTreeRef.value) return;
  const tree = deviceTreeRef.value;
  const expand = (nodes: any[]) => {
    nodes.forEach(node => {
      tree.store.nodesMap[node.id] && tree.store.nodesMap[node.id].expanded !== undefined && (tree.store.nodesMap[node.id].expanded = true);
      if (node.children && node.children.length > 0) {
        expand(node.children);
      }
    });
  };
  expand(deviceTreeData.value);
};

// 全部收缩
const collapseAll = () => {
  if (!deviceTreeRef.value) return;
  deviceTreeRef.value!.store.root.expanded = false;
  const collapse = (nodes: any[]) => {
    nodes.forEach(node => {
      deviceTreeRef.value!.store.nodesMap[node.id] &&
        deviceTreeRef.value!.store.nodesMap[node.id].expanded !== undefined &&
        (deviceTreeRef.value!.store.nodesMap[node.id].expanded = false);
      if (node.children && node.children.length > 0) {
        collapse(node.children);
      }
    });
  };
  collapse(deviceTreeData.value);
  // 只展开根节点
  deviceTreeRef.value!.store.root.expanded = true;
};
</script>

<style scoped lang="scss">
@import "@/styles/resize";
.device-container {
  flex-direction: row;
  width: 100%;
  .device-main {
    width: 100%;
    .left-page {
      width: 260px;
      height: 100%;
      .custom-tree-node {
        .tree-menu {
          margin-right: 2px;
          color: var(--el-color-primary);
        }
        .tree-submenu {
          margin-right: 2px;
          color: var(--el-color-primary);
        }
      }
    }
    .right-page {
      width: 100%;
      height: 100%;
      margin-right: 5px;
      margin-left: 1px;
      overflow: hidden;
    }
  }
}

// 统一右键菜单样式
.custom-contextmenu {
  min-width: 140px;
  padding: 4px 0;
  color: var(--el-text-color-primary);
  background: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
.custom-contextmenu-item {
  display: flex;
  align-items: center;
  color: var(--el-text-color-primary);
  background: transparent;
  transition: background 0.2s;
  .svg-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    margin-right: 6px;
    font-size: 18px;
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    border-radius: 50%;
    transition: color 0.2s;
  }
  &:hover {
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-8);
    .svg-icon {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
  }
}

// 统一弹窗样式
:deep(.el-dialog) {
  background: var(--el-bg-color);
  border-radius: 8px;
}
:deep(.el-dialog__title) {
  color: var(--el-color-primary);
}
</style>
