.el-container {
  width: 100%;
  height: 100%;
  .aside-split {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    width: 70px;
    height: 100%;
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-aside-border-color);
    .el-menu {
      width: 100%;
      border: unset;
    }
    .el-sub-menu {
      display: flex;
      flex-direction: column;
      place-content: center center;
      align-items: center;
      height: 70px;
      .el-icon {
        font-size: 23px;
      }
      :deep(.el-sub-menu__title) {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }
    .el-menu-item {
      display: flex;
      flex-direction: column;
      place-content: center center;
      align-items: center;
      height: 70px;
      line-height: normal;
    }
    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 55px;
      font-size: 20px;
      .logo-img {
        width: 32px;
        object-fit: contain;
      }
      .company-name-en {
        font-family: Tahoma;
        font-size: 18px;
        font-weight: 600;
        color: #124198;
        user-select: none;
      }
      .company-name-cn {
        font-family: "黑体";
        font-size: 16px;
        font-weight: 600;
        color: #124198;
        user-select: none;
      }
      .logo-mark {
        top: 1px;
        right: 1px;
        margin: 0 4px;
        font-family: Tahoma;
        font-size: 14px;
        font-weight: 600;
        color: #124198;
        user-select: none;
      }
    }
    .el-scrollbar {
      height: calc(100% - 255px);
      .split-list {
        flex: 1;
        .split-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 70px;
          cursor: pointer;
          transition: all 0.3s ease;
          .el-icon {
            font-size: 23px;
          }
          .title {
            margin-top: 6px;
            font-size: 12px;
          }
          .el-icon,
          .title {
            color: var(--el-menu-text-color);
          }
        }
        .split-active {
          background-color: var(--el-color-primary) !important;
          .el-icon,
          .title {
            color: #ffffff !important;
          }
        }
      }
    }
    .other {
      flex-direction: column;
      gap: 8px;
      height: 350px;
      margin-bottom: 0;
      :deep(.iconfont) {
        color: var(--el-menu-text-color);
      }

      // 为工具栏按钮添加与主菜单一致的选中状态样式
      :deep(.item-main) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 65px;
        height: 70px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;
        &:hover {
          background-color: var(--bl-hover-bg-color);
        }

        // 选中状态样式，使用菜单主题变量与左侧主菜单保持一致
        &.active {
          background-color: var(--el-menu-active-bg-color) !important;
          .iconfont,
          .toolBar-icon,
          svg,
          span {
            color: var(--el-menu-active-color) !important;
          }

          // 确保图标和文字都使用菜单激活色
          :deep(svg) {
            color: var(--el-menu-active-color) !important;
          }
          :deep(.iconfont) {
            color: var(--el-menu-active-color) !important;
          }
        }
      }
    }
  }
  .not-aside {
    width: 0 !important;
    border-right: none !important;
  }
  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
  }
}
