.resize-vertical {
  position: relative;
  z-index: 1001;
  width: 1px;
  height: 100%;
  cursor: ew-resize;
  background-color: var(--el-border-color);
  &::before {
    position: absolute;
    left: -1px;
    z-index: 1002;
    display: block;
    width: 3px;
    height: 100%;
    cursor: ew-resize;
    content: "";
    background-color: transparent;
    transition: 0.3s;
  }
  &:hover {
    &::before {
      background-color: var(--el-color-primary-light-5);
    }
  }
}
.resize-vertical-no-border {
  position: relative;
  z-index: 1001;
  width: 1px;
  height: 100%;
  cursor: ew-resize;
  background-color: transparent;
  &::before {
    position: absolute;
    left: -1px;
    z-index: 1002;
    display: block;
    width: 3px;
    height: 100%;
    cursor: ew-resize;
    content: "";
    background-color: transparent;
    transition: 0.3s;
  }
  &:hover {
    &::before {
      background-color: var(--el-color-primary-light-5);
    }
  }
}
.resize-horizontal {
  position: relative;
  z-index: 1001;
  width: 100%;
  height: 1px;
  cursor: ew-resize;
  background-color: var(--el-border-color);
  &::before {
    position: absolute;
    left: -1px;
    z-index: 1002;
    display: block;
    width: 100%;
    height: 3px;
    cursor: ew-resize;
    content: "";
    background-color: transparent;
    transition: 0.3s;
  }
  &:hover {
    &::before {
      background-color: var(--el-color-primary-light-5);
    }
  }
}
