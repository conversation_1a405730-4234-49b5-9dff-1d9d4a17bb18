<template>
  <div class="containerComponent">
    <el-collapse v-model="activeNames">
      <el-collapse-item v-for="(item, index) in componentArrs" name="common" :key="index">
        <template #title>
          {{ item.title }}
        </template>
        <el-row :gutter="10">
          <el-col
            v-for="(componentItem, index1) in item.components"
            :span="6"
            :key="index1"
            draggable="true"
            @dragstart="componentToolClick($event, componentItem.data)"
            class="graph-basic-item"
          >
            <el-tooltip :content="componentItem.title" placement="top" :show-after="500">
              <el-image :src="componentItem.url" class="graph-equipment-img">
                <template #error>
                  <span>{{ componentItem.title }}</span>
                </template>
              </el-image>
            </el-tooltip>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
    <el-form style="padding: 30px 5px 0 0">
      <el-form-item :label="t('graphDefine.graphComponent.deviceType')">
        <el-select v-model="form.type" @change="onTypeChange">
          <el-option v-for="item in equipmentTypeItems" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('graphDefine.graphComponent.deviceName')">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button style="margin-left: 60px" type="success" @click="onSave">{{ t("graphDefine.graphComponent.save") }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { Node } from "@antv/x6";
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ComponentInfo, EquipmentType, Select } from "../../..//graph/Graph";
import { getBasicComponent } from "../..";

const { t } = useI18n();

// 定义事件
const emit = defineEmits<{
  (e: "addComponent", event: MouseEvent, data: Node.Metadata): void;
  (e: "save", data: { name: string; type: EquipmentType }): void;
}>();

// 创建响应式的设备类型映射
const getEquipmentTypeName = (type: EquipmentType): string => {
  const typeNameMap: Record<EquipmentType, string> = {
    [EquipmentType.CBR]: t("hmi.graph.equipmentType.CBR"),
    [EquipmentType.DIS]: t("hmi.graph.equipmentType.DIS"),
    [EquipmentType.GDIS]: t("hmi.graph.equipmentType.GDIS"),
    [EquipmentType.PTR2]: t("hmi.graph.equipmentType.PTR2"),
    [EquipmentType.PTR3]: t("hmi.graph.equipmentType.PTR3"),
    [EquipmentType.VTR]: t("hmi.graph.equipmentType.VTR"),
    [EquipmentType.CTR]: t("hmi.graph.equipmentType.CTR"),
    [EquipmentType.EFN]: t("hmi.graph.equipmentType.EFN"),
    [EquipmentType.IFL]: t("hmi.graph.equipmentType.IFL"),
    [EquipmentType.EnergyConsumer]: t("hmi.graph.equipmentType.EnergyConsumer"),
    [EquipmentType.GND]: t("hmi.graph.equipmentType.GND"),
    [EquipmentType.Arrester]: t("hmi.graph.equipmentType.Arrester"),
    [EquipmentType.Capacitor_P]: t("hmi.graph.equipmentType.Capacitor_P"),
    [EquipmentType.Capacitor_S]: t("hmi.graph.equipmentType.Capacitor_S"),
    [EquipmentType.Reactor_P]: t("hmi.graph.equipmentType.Reactor_P"),
    [EquipmentType.Reactor_S]: t("hmi.graph.equipmentType.Reactor_S"),
    [EquipmentType.Ascoil]: t("hmi.graph.equipmentType.Ascoil"),
    [EquipmentType.Fuse]: t("hmi.graph.equipmentType.Fuse"),
    [EquipmentType.BAT]: t("hmi.graph.equipmentType.BAT"),
    [EquipmentType.BSH]: t("hmi.graph.equipmentType.BSH"),
    [EquipmentType.CAB]: t("hmi.graph.equipmentType.CAB"),
    [EquipmentType.LIN]: t("hmi.graph.equipmentType.LIN"),
    [EquipmentType.GEN]: t("hmi.graph.equipmentType.GEN"),
    [EquipmentType.GIL]: t("hmi.graph.equipmentType.GIL"),
    [EquipmentType.RRC]: t("hmi.graph.equipmentType.RRC"),
    [EquipmentType.TCF]: t("hmi.graph.equipmentType.TCF"),
    [EquipmentType.TCR]: t("hmi.graph.equipmentType.TCR"),
    [EquipmentType.LTC]: t("hmi.graph.equipmentType.LTC"),
    [EquipmentType.IND]: t("hmi.graph.equipmentType.IND")
  };
  return typeNameMap[type] || type;
};

// 创建响应式的设备类型选项列表
const equipmentTypeItems = computed<Select<EquipmentType>[]>(() => {
  return Object.values(EquipmentType).map(type => ({
    label: getEquipmentTypeName(type),
    value: type
  }));
});

const form = ref<{
  type: EquipmentType;
  name: string;
}>({
  type: EquipmentType.CBR,
  name: getEquipmentTypeName(EquipmentType.CBR)
});

const activeNames = ref(["common"]);

const componentArrs: { title: string; components?: ComponentInfo[] }[] = [];
componentArrs.unshift(getBasicComponent(t));

const componentToolClick = (event: MouseEvent, data: unknown) => {
  // 创建节点
  const nodeData = data as any;
  const node: Node.Metadata = {
    ...nodeData
  };
  emit("addComponent", event, node);
};
const onTypeChange = () => {
  const name = getEquipmentTypeName(form.value.type);
  if (name) {
    form.value.name = name;
  }
};
const onSave = () => {
  emit("save", { name: form.value.name, type: form.value.type });
};
</script>
<style>
@import "../../../styles/Graph";
.containerComponent {
  padding: 0 0 0 10px;
}
</style>
