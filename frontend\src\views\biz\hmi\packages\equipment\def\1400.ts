import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "1400",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(12, 0, 16.67, 17)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 14.33,
        y1: 34.33,
        x2: 14.33,
        y2: 27.16
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 16.67, 17)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(6.33, 10.33, 16.67, 17)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
