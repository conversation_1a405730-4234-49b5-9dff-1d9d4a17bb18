import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "0401",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 5,
        x2: 0,
        y2: 5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 33,
        y1: 5,
        x2: 28,
        y2: 5
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(4.5, 0, 23.5, 10)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 28,
        y1: 0.17,
        x2: 5,
        y2: 9.5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 28,
        y1: 9.83,
        x2: 5,
        y2: 0.5
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
