<template>
  <el-dialog
    v-model="showModal"
    :title="t('components.closer.title')"
    style="width: 750px; margin: auto"
    align-center
    destroy-on-close
    draggable
    :before-close="closeDialog"
  >
    <div class="close-dialog flx-center">
      <span>{{ t("components.closer.message") }}</span>
      <el-row just="flex-end" style="margin-top: 20px">
        <el-button type="primary" @click="closeWindow(CloseTypeEnum.QUIT)">{{ t("components.closer.confirm") }}</el-button>
        <el-button type="success" @click="closeWindow(CloseTypeEnum.HIDE)">{{ t("components.closer.minimize") }}</el-button>
        <el-button @click="closeDialog()">{{ t("components.closer.cancel") }}</el-button>
      </el-row>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { CloseTypeEnum } from "@/enums";
import { useDebugStore } from "@/stores/modules/debug";
import { WindowControl, windowControlApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const showModal = defineModel<boolean>();
const debugStore = useDebugStore();

const closeWindow = async (val: WindowControl.CloseType): Promise<void> => {
  if (val == CloseTypeEnum.HIDE) {
    closeDialog();
  }
  if (val == CloseTypeEnum.QUIT) {
    debugStore.disconnectAllDevices();
  }
  await windowControlApi.closeWindow(val);
};
const closeDialog = (): void => {
  showModal.value = false;
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
