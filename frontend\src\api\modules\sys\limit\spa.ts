import { moduleRequest } from "@/api/request";
import { ReqId, ResPage, Spa } from "@/api/interface";
const http = moduleRequest("/sys/limit/spa/");

const spaApi = {
  /** 获取单页分页 */
  page(params: Spa.Page) {
    return http.get<ResPage<Spa.SpaInfo>>("page", params);
  },
  /** 获取单页详情 */
  detail(params: ReqId) {
    return http.get<Spa.SpaInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除单页 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  }
};

export { spaApi };
