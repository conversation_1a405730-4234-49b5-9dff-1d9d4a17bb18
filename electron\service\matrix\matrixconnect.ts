"use strict";

import {
  UpadRpcConnectOptions,
  UpadRpcConnectRes,
} from "iec-upadrpc/dist/src/upadrpc/UpadRpcDef";
import { DebugDeviceInfo } from "../../interface/debug/debuginfo";

import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import { IECResult } from "iec-common/dist/data/iecdata";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import IECCONSTANTS from "../../data/debug/iecConstants";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { XmlFileManager } from "../../utils/xmlFileManager";
import { debugInfoMenuService } from "../debug/debuginfomenu";

const { logger } = require("ee-core/log");

/**
 * 装置连接Service
 * <AUTHOR>
 * @class
 */
class MatrixConnectService {
  /**
   * 连接装置 */
  async connectDeviceByRpc(
    dataStr: string
  ): Promise<IECResult<UpadRpcConnectRes>> {
    logger.info("[MatrixConnectService] connectDeviceByRpc 入参:", dataStr);
    const globalDeviceData = GlobalDeviceData.getInstance();
    const data: DebugDeviceInfo = JSON.parse(dataStr);
    // 判断是否存在错误码信息,首次连接时如果连接出错，提供默认错误码，但是
    let singleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(data.id);
    if (!singleGlobalDeviceInfo) {
      singleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
      await singleGlobalDeviceInfo.initEnumTypeAndFile();
    }
    const enumType = singleGlobalDeviceInfo.enumTypes.get("ServiceError");
    // 调取接口
    const connectOptios: UpadRpcConnectOptions = {
      ip: data.ip,
      port: Number(data.port),
      connectTimeout: Number(data.connectTimeout),
      readTimeout: Number(data.readTimeout),
      libPath: IECCONSTANTS.PATH_DLL + "\\upadrpc",
    };
    logger.info("connectOptios:", connectOptios);
    // 使用装置的id作为key
    const key = data.id;
    // 从全局变量中获取连接对象
    let upadRcpClient = globalDeviceData.deviceInfoMap.get(key)?.deviceClient;
    if (upadRcpClient && upadRcpClient.isConnected()) {
      await upadRcpClient.disconnect();
    }
    upadRcpClient = new UpadRcpClient();

    const connResult = await upadRcpClient.connect(connectOptios);
    if (!connResult.isSuccess()) {
      let msg = enumType?.get(connResult.data?.code + "");
      if (!msg) {
        msg = "连接失败";
      }
      if (connResult.code === 10) {
        msg = "连接超时";
      }
      connResult.msg = msg;
      return connResult;
    }
    logger.info(
      "[MatrixConnectService] connectDeviceByRpc 连接成功",
      connResult
    );
    // 将连接成功的连接对象缓存
    let realSingleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(
      data.id
    );
    if (!realSingleGlobalDeviceInfo) {
      realSingleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
    }
    // 复位数据
    realSingleGlobalDeviceInfo.resetData();
    realSingleGlobalDeviceInfo.deviceClient = upadRcpClient;
    // 比较md5码，然后设置文件位置
    const xmlFileManager = new XmlFileManager();
    await xmlFileManager.setDebugInfoFilePath(
      key,
      upadRcpClient,
      realSingleGlobalDeviceInfo
    );
    // 往realSingleGlobalDeviceInfo添加debugInfo 和debugItemMap
    globalDeviceData.setDeviceInfoGlobal(key, realSingleGlobalDeviceInfo);
    realSingleGlobalDeviceInfo.debugInfo =
      await debugInfoMenuService.getDebugInfo(key);
    return connResult;
  }

  // 断开连接
  async disconnectDevice(deviceId: string): Promise<any> {
    logger.info("[MatrixConnectService] disconnectDevice 入参:", deviceId);
    // 获取连接
    const deviceInfoGlobal =
      GlobalDeviceData.getInstance().getDeviceInfoGlobal(deviceId);
    let upadRcpClient = deviceInfoGlobal?.deviceClient;
    if (upadRcpClient && typeof UpadRcpClient) {
      const result = await upadRcpClient.disconnect();
      if (result) {
        // 删除连接对象
        upadRcpClient = undefined;
      }
      logger.info(
        `[MatrixConnectService] disconnectDevice - 断开连接成功，设备ID: ${deviceId}`
      );
      return result;
    }
  }
}

MatrixConnectService.toString = () => "[class MatrixConnectService]";
const matrixConnectService = new MatrixConnectService();

export { MatrixConnectService, matrixConnectService };
