<template>
  <div class="skeleton-loading">
    <div class="skeleton-box"></div>
  </div>
</template>
<script setup lang="ts"></script>
<style scoped lang="scss">
@import "@/styles/utils";
.skeleton-loading {
  position: absolute;
  inset: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-width: 120px;
  height: 100%;
  min-height: 120px;
  margin: 0;

  @include theme-bg(#f5f5f5, #232324);
}
.skeleton-box {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  animation: skeleton-blink 1.2s infinite ease-in-out;

  @include theme-bg(#e0e0e0, #363636);
}

@keyframes skeleton-blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}
</style>
