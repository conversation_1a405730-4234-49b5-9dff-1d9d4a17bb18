"use strict";

import { Parser } from "xml2js";
import { readFileSync } from "fs";
import { DebugInfo, DebugInfoMenu, DebugInfoItem, Header, MenuIdName, GroupInfoItem } from "../../interface/debug/debuginfo";
import { XmlFileManager } from "../../utils/xmlFileManager";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import { t } from "../../data/i18n/i18n";

/**
 * 装置菜单信息解析Service
 * 负责解析装置文档、菜单树、菜单项等相关业务逻辑
 * <AUTHOR>
 * @class
 */
class DebugInfoMenuService {
  private xmlFileManager: XmlFileManager;

  constructor() {
    this.xmlFileManager = new XmlFileManager();
    logger.info(
      `[DebugInfoMenuService] ${t("logs.debugInfoMenuService.initialized")}`
    );
  }

  // private transformMenu(menu: any, flag: string, deviceId: string): DebugInfoMenu | null {
  //   logger.info("flag:", flag);
  //   // 处理menu.Item可能为对象或数组的情况
  //   const items = menu.Item ? (Array.isArray(menu.Item) ? menu.Item.map((item: any) => this.transformItem(item, deviceId)) : [this.transformItem(menu.Item, deviceId)]) : [];

  //   // 处理menu.Menu可能为对象或数组的情况
  //   const menus = menu.Menu
  //     ? (Array.isArray(menu.Menu)
  //         ? menu.Menu.map((subMenu: any) => {
  //             logger.info("=================1111",  menu.Menu);
  //             this.transformMenu(subMenu, "2", deviceId);
  //           })
  //         : [this.transformMenu(menu.Menu, "2", deviceId)]
  //       ).filter((subMenu: DebugInfoMenu | null) => subMenu !== null)
  //     : [];

  //   logger.info("=================222", menu);
  //   logger.info("=================333", menu.$);
  //   return {
  //     name: menu.$.name,
  //     desc: menu.$.desc,
  //     fc: menu.$.fc,
  //     method: menu.$.method,
  //     items: items || [],
  //     menus: menus || [],
  //   };
  // }
  private transformMenu(
    menu: any,
    flag: string,
    deviceId: string
  ): DebugInfoMenu | null {
    try {
      if (!menu || !menu.$ || !menu.$.name) {
        logger.warn(
          `Invalid menu structure for device ${deviceId}, flag: ${flag}`
        );
        return null;
      }

      // 处理menu.Item可能为对象或数组的情况
      const items = menu.Item
        ? Array.isArray(menu.Item)
          ? menu.Item.map((item: any) =>
              this.transformItem(item, deviceId)
            ).filter((item): item is DebugInfoItem => item !== null)
          : [this.transformItem(menu.Item, deviceId)].filter(
              (item): item is DebugInfoItem => item !== null
            )
        : [];

      // 处理menu.Menu可能为对象或数组的情况
      const menus = menu.Menu
        ? Array.isArray(menu.Menu)
          ? menu.Menu.map((subMenu: any) => {
              if (!subMenu) {
                logger.warn(`Null subMenu found in device ${deviceId}`);
                return null;
              }
              return this.transformMenu(subMenu, "2", deviceId);
            }).filter((subMenu): subMenu is DebugInfoMenu => subMenu !== null)
          : [this.transformMenu(menu.Menu, "2", deviceId)].filter(
              (subMenu): subMenu is DebugInfoMenu => subMenu !== null
            )
        : [];

      // logger.info(
      //   `[DebugInfoMenuService] transformMenu - 处理完成，菜单名: ${menu.$.name}`
      // );
      return {
        name: menu.$.name,
        desc: menu.$.desc || "",
        fc: menu.$.fc || "",
        method: menu.$.method || "",
        items,
        menus,
      };
    } catch (error) {
      logger.error(
        `[DebugInfoMenuService] transformMenu - 处理菜单异常，deviceId: ${deviceId}`,
        error
      );
      return null;
    }
  }

  private transformItem(item: any, deviceId: string): DebugInfoItem | null {
    logger.debug(
      `[DebugInfoMenuService] transformItem - 处理菜单项，item:`,
      item,
      `deviceId: ${deviceId}`
    );
    if (item) {
      this.putValTodebugItemMap(deviceId, item);
      // logger.info(`[DebugInfoMenuService] transformItem - 处理完成，菜单项名: ${item.$.name}`);
      return {
        name: item.$.name,
        vKey: item.$.vKey,
        bType: item.$.bType,
        format: item.$.format,
        qKey: item.$.qKey,
        unit: item.$.unit,
        desc: item.$.desc,
        cn: item.$.cn,
        grp: item.$.grp,
        inf: item.$.inf,
        p_norm: item.$.p_norm,
        s_norm: item.$.s_norm,
        p_min: item.$.p_min,
        p_max: item.$.p_max,
        s_min: item.$.s_min,
        s_max: item.$.s_max,
        step: item.$.step,
        type: item.$.type,
      };
    }
    logger.warn(
      `[DebugInfoMenuService] transformItem - 菜单项为空，deviceId: ${deviceId}`
    );
    return null;
  }

  private transformHeader(header: any): Header {
    logger.debug(
      `[DebugInfoMenuService] transformHeader - 处理header:`,
      header
    );
    return {
      updateTime: header?.$?.updateTime || "",
      md5: header?.$?.md5 || "",
    };
  }

  public async getDebugInfo(id: string): Promise<DebugInfo> {
    logger.info(
      `[DebugInfoMenuService] ${t("logs.debugInfoMenuService.getDebugInfoEntry")}:`,
      id
    );
    try {
      logger.debug(
        `[DebugInfoMenuService] getDebugInfo - 读取xml文件路径:`,
        this.xmlFileManager.getFilePath(id)
      );
      const xmlData = readFileSync(this.xmlFileManager.getFilePath(id), {
        encoding: "utf-8",
      });
      const result = await new Parser({
        explicitArray: false,
      }).parseStringPromise(xmlData);

      if (!result?.DebugInfo?.$?.configVersion) {
        logger.error("Invalid XML structure:", result);
        throw new Error(t("errors.invalidXmlStructure"));
      }

      // logger.info("[DebugInfoMenuService] getDebugInfo return:", result);
      return {
        configVersion: result.DebugInfo.$.configVersion,
        header: this.transformHeader(result.DebugInfo.Header || {}),
        dataTypeTemplates: this.xmlFileManager.transformDataTypeTemplates(
          result.DebugInfo.DataTypeTemplates || {},
          GlobalDeviceData.getInstance().deviceInfoMap.get(id)
        ),
        menus: result.DebugInfo.Menu.map((menu: any) =>
          this.transformMenu(menu, "1", id)
        ).filter((menu: DebugInfoMenu | null) => menu !== null),
      };
    } catch (error) {
      logger.error("[DebugInfoMenuService] getDebugInfo error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      throw new Error("Failed to parse device info: " + errorMessage);
    }
  }

  public async getTreeMenu(id: string): Promise<DebugInfoMenu[]> {
    logger.info(`[DebugInfoMenuService] getTreeMenu 入参:`, id);
    try {
      logger.debug(`[DebugInfoMenuService] getTreeMenu - 获取全局数据`);
      // 如果装置断开后再次连接 。MD5没变，则直接取缓存数据
      const globalDeviceData = GlobalDeviceData.getInstance();
      const client = globalDeviceData.getClient(id);
      const deviceInfoMap = globalDeviceData.deviceInfoMap;
      if (client && deviceInfoMap.get(id)?.compareMd5Result) {
        let deviceMenuTree = deviceInfoMap.get(id)?.deviceInfoMenus;
        if (deviceMenuTree && deviceMenuTree.length > 0) {
          logger.info("[DebugInfoMenuService] getTreeMenu using cached menu");
          return deviceMenuTree;
        }
      }

      logger.info(
        "[DebugInfoMenuService] getTreeMenu xmlFileManager.parseTreeMenu"
      );
      // 获取debug info数据
      const debugInfo = await this.getDebugInfo(id);
      const debugMenus = debugInfo.menus;
      // 通过id获取封装的菜单数据
      const singleGlobalDeviceInfo = deviceInfoMap.get(id);
      if (singleGlobalDeviceInfo) {
        singleGlobalDeviceInfo.deviceInfoMenus = debugMenus;
      }
      logger.info(
        `[DebugInfoMenuService] ${t("logs.debugInfoMenuService.getTreeMenuComplete")}: ${debugMenus.length}`
      );
      return this.dealTreeMenus(debugMenus);
    } catch (error) {
      logger.error(
        `[DebugInfoMenuService] ${t("logs.debugInfoMenuService.getTreeMenuError")}:`,
        error
      );
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      throw new Error(t("errors.failedToGetTreeMenu") + ": " + errorMessage);
    }
  }

  // 处理获取菜单信息，只返回菜单树，不返回菜单数据
  private dealTreeMenus(treeMenus: DebugInfoMenu[]): DebugInfoMenu[] {
    logger.debug(
      `[DebugInfoMenuService] dealTreeMenus - 处理菜单树，菜单数量: ${treeMenus.length}`
    );
    for (const menuItem of treeMenus) {
      if (menuItem.menus) {
        for (const menu of menuItem.menus) {
          menu.items = [];
        }
      }
    }
    logger.info(`[DebugInfoMenuService] dealTreeMenus - 处理完成`);
    return treeMenus;
  }

  // 从缓存中获取对应菜单的数据
  public getTreeItemByName(menuNames: MenuIdName): DebugInfoItem[] {
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(menuNames.id);
    if (singleGlobalDeviceInfo && singleGlobalDeviceInfo.debugInfo) {
      const devicemenus = singleGlobalDeviceInfo.debugInfo?.menus;
      if (devicemenus) {
        return this.getSecondMenuItems(devicemenus, menuNames);
      }
    }
    return [];
  }
  public getTreeItemByFc(fc: string[], id: string, menu = true) {
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(id);
    if (singleGlobalDeviceInfo && singleGlobalDeviceInfo.debugInfo) {
      const devicemenus = singleGlobalDeviceInfo.debugInfo?.menus;
      if (devicemenus) {
        if (menu) {
          return this.getSecondMenuByFc(devicemenus, fc);
        } else {
          return this.getSecondMenuItemByFc(devicemenus, fc);
        }
      }
    }
    return [];
  }

  public getGroupInfoList(req: IECReq<any>) {
    const { grpName } = req.data;
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(req.head.id);

    logger.info(grpName, req.head.id);
    const items: GroupInfoItem[] = new Array();
    if (singleGlobalDeviceInfo && singleGlobalDeviceInfo.debugInfo) {
      const devicemenus = singleGlobalDeviceInfo.debugInfo.menus;
      devicemenus.forEach((item) => {
        this.getGroupItems(item, grpName, items);
      });
    }
    return items;
  }

  private getGroupItems(
    item: DebugInfoMenu,
    grpName: any,
    items: GroupInfoItem[]
  ) {
    if (item.menus) {
      item.menus?.forEach((menuItem, index) => {
        if (item.name == grpName) {
          this.pushItem(items, index, menuItem);
        }
        if (menuItem.menus) {
          this.getGroupItems(menuItem, grpName, items);
        }
      });
    }
  }

  private pushItem(
    items: GroupInfoItem[],
    index: number,
    menuItem: DebugInfoMenu
  ) {
    items.push({
      id: index + 1,
      name: menuItem.name,
      desc: menuItem.desc,
      fc: menuItem.fc,
      count: menuItem.items?.length,
    });
  }

  private getSecondMenuItems(devicemenus, menuNames) {
    for (const firstMenu of devicemenus) {
      if (
        menuNames.type === "submenu" &&
        firstMenu.name === menuNames.names[0] &&
        firstMenu.menus
      ) {
        for (const secondMenu of firstMenu.menus) {
          if (secondMenu.name === menuNames.names[1] && secondMenu.items) {
            return secondMenu.items;
          }
        }
      }
    }
    return [];
  }

  private getSecondMenuByFc(devicemenus, fc) {
    const items: DebugInfoMenu[] = [];
    for (const firstMenu of devicemenus) {
      this.getChildMenu(firstMenu, fc, items);
    }
    return items;
  }
  private getChildMenu(firstMenu: any, fc: any, items: DebugInfoMenu[]) {
    for (const secondMenu of firstMenu.menus) {
      if (fc.includes(secondMenu.fc) && secondMenu.items) {
        items.push(secondMenu);
      }
      if (secondMenu.menus && Array.isArray(secondMenu.menus)) {
        this.getChildMenu(secondMenu, fc, items);
      }
    }
  }

  private getSecondMenuItemByFc(devicemenus, fc) {
    const items: DebugInfoItem[] = [];
    for (const firstMenu of devicemenus) {
      this.getChildItems(firstMenu, fc, items);
    }
    return items;
  }
  private getChildItems(firstMenu: any, fc: any, items: DebugInfoItem[]) {
    for (const secondMenu of firstMenu.menus) {
      if (fc.includes(secondMenu.fc) && secondMenu.items) {
        items.push(...secondMenu.items);
      }
      if (secondMenu.menus && Array.isArray(secondMenu.menus)) {
        this.getChildItems(secondMenu, fc, items);
      }
    }
  }

  /**
   *
   * @param deviceId 装置id
   * @param key item中name
   * @returns
   */
  public getDebugItemMapVal(deviceId: string, key: string): string | null {
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(deviceId);
    if (singleGlobalDeviceInfo && singleGlobalDeviceInfo.debugInfo) {
      const desc = singleGlobalDeviceInfo.debugItemMap.get(key) || null;
      return desc;
    }
    return null;
  }

  public getDebugItemObjMapVal(deviceId: string, key: string) {
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(deviceId);
    if (singleGlobalDeviceInfo && singleGlobalDeviceInfo.debugInfo) {
      const obj = singleGlobalDeviceInfo.debugItemObjMap.get(key);
      return obj;
    }
    return null;
  }

  private putValTodebugItemMap(deviceid, item) {
    const deviceInfoMap = GlobalDeviceData.getInstance().deviceInfoMap;
    // 通过id获取封装的菜单数据
    const singleGlobalDeviceInfo = deviceInfoMap.get(deviceid);
    singleGlobalDeviceInfo?.debugItemMap.set(item.$.name, item.$.desc);
    singleGlobalDeviceInfo?.debugItemObjMap.set(item.$.name, item.$);
  }
}

DebugInfoMenuService.toString = () => "[class DebugInfoMenuService]";
const debugInfoMenuService = new DebugInfoMenuService();

export { DebugInfoMenuService, debugInfoMenuService };
