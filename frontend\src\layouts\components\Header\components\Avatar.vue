<template>
  <el-dropdown trigger="click">
    <div class="avatar">
      <img :src="userInfo?.avatar" alt="avatar" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="openDialog('infoRef')">
          <el-icon><User /></el-icon>{{ t("layout.header.avatar.profile") }}
        </el-dropdown-item>
        <el-dropdown-item @click="openDialog('changeModuleRef')">
          <el-icon><Switch /></el-icon>{{ t("layout.header.avatar.switchApp") }}
        </el-dropdown-item>
        <el-dropdown-item divided @click="logout">
          <el-icon><SwitchButton /></el-icon>{{ t("layout.header.avatar.logout") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <!-- infoDialog -->
  <InfoDialog ref="infoRef" />
  <!-- passwordDialog -->
  <PasswordDialog ref="passwordRef" />
  <ChooseModule ref="changeModuleRef" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { LOGIN_URL, USER_CENTER_URL } from "@/config";
import { useRouter } from "vue-router";
import { loginApi } from "@/api/modules";
import { useUserStore } from "@/stores/modules/user";
import { ElMessageBox, ElMessage } from "element-plus";
import InfoDialog from "./InfoDialog.vue";
import PasswordDialog from "./PasswordDialog.vue";
import ChooseModule from "@/components/ChooseModule/index.vue";
import { useI18n } from "vue-i18n";

const router = useRouter();
const userStore = useUserStore();
const { t } = useI18n();
const { userInfo } = userStore;

// 退出登录
const logout = () => {
  ElMessageBox.confirm(t("layout.header.avatar.logoutConfirm.message"), t("layout.header.avatar.logoutConfirm.title"), {
    confirmButtonText: t("layout.header.avatar.logoutConfirm.confirm"),
    cancelButtonText: t("layout.header.avatar.logoutConfirm.cancel"),
    type: "warning"
  })
    .then(async () => {
      const { accessToken } = userStore;
      // 1.执行退出登录接口
      await loginApi.logout({ token: accessToken });

      // 2.清除 Token
      userStore.clearToken();

      // 3.重定向到登陆页
      router.replace(LOGIN_URL);
      ElMessage.success(t("layout.header.avatar.logoutSuccess"));
    })
    .catch(() => {
      // 用户取消退出登录，不需要处理
    });
};

// 打开修改密码和个人信息弹窗
const infoRef = ref<InstanceType<typeof InfoDialog> | null>(null);
const passwordRef = ref<InstanceType<typeof PasswordDialog> | null>(null);
const changeModuleRef = ref<InstanceType<typeof ChooseModule> | null>(null);
const openDialog = (ref: string) => {
  if (ref == "infoRef") router.replace(USER_CENTER_URL);
  if (ref == "changeModuleRef") changeModuleRef.value?.openDialog();
};
</script>

<style scoped lang="scss">
.avatar {
  width: 40px;
  height: 40px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 50%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
