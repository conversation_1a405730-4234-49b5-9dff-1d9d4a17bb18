import { DebugInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { CtlResult, SelectRequestData } from "@/api/interface/biz/debug/remote";
import { moduleIpcRequest } from "@/api/request";

const remotecontrolIpc = moduleIpcRequest("controller/debug/remotecontrol/");

const remotesignalIpc = moduleIpcRequest("controller/debug/remoteyxandyc/");

const remoteControlApi = {
  /** 获取后台数据（显式指定 deviceId） */
  ykSelectWithValueByDevice(deviceId: string, param: SelectRequestData) {
    return remotecontrolIpc.iecInvokeWithDevice<CtlResult>("ykSelectWithValue", param, deviceId);
  },

  /** 遥控执行（显式指定 deviceId） */
  ykSelectValueConfirmByDevice(deviceId: string, param: SelectRequestData) {
    return remotecontrolIpc.iecInvokeWithDevice<CtlResult>("ykSelectValueConfirm", param, deviceId);
  },

  /** 遥控取消（显式指定 deviceId） */
  ykSelectValueCancelByDevice(deviceId: string, param: SelectRequestData) {
    return remotecontrolIpc.iecInvokeWithDevice<CtlResult>("ykSelectValueCancel", param, deviceId);
  }
};

const remoteSigalApi = {
  /** 获取后台数据（显式指定 deviceId，用于多装置并行） */
  remoteSigalGroupValByDevice(deviceId: string, param: any) {
    return remotesignalIpc.iecInvokeWithDevice<DebugInfoItem[]>("ykycGetGroupData", param, deviceId);
  },

  /** 导出所有分组（显式指定 deviceId） */
  exporrAllGroupValByDevice(deviceId: string, param: { path: string; names: string[] }) {
    return remotesignalIpc.iecInvokeWithDevice("exportAllData", param, deviceId);
  }
};

export { remoteControlApi, remoteSigalApi };
