.content-box {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  .device-left {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 20%;
    min-width: 320px;
    margin-right: 6px;
  }
  .resize-vertical-no-border {
    position: relative;
    &:hover {
      .collapse-button-container {
        .collapse-icon {
          background-color: var(--el-color-primary-light-8);
          border: 1px solid var(--el-color-primary-light-6);
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
          opacity: 1;
        }
      }
    }
    .collapse-button-container {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 10;
      width: 40px;
      height: 100%;
      pointer-events: auto;
      background: transparent;
      transition: all 0.3s ease;
      transform: translateX(-50%);
      &:hover {
        .collapse-icon {
          background-color: var(--el-color-primary-light-8);
          border: 1px solid var(--el-color-primary-light-6);
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
          opacity: 1;
        }
      }
    }
  }
  .device-right {
    box-sizing: border-box;
    flex: 1;
    padding: 5px;
    margin-left: 3px;
    background-color: var(--el-bg-color);
  }

  /* 组件切换过渡动画 */
  .component-fade-enter-active,
  .component-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .component-fade-enter-from {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
  }
  .component-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }

  /* 渐隐渐现过渡 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s;
  }
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  /* 加载占位符样式优化 */
  .loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
    color: var(--el-text-color-secondary);
    .loading-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      p {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  /* 加载动画优化 */
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--el-border-color-light);
    border-top: 3px solid var(--el-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* 组件内部加载状态 */
  .component-loading {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--el-text-color-secondary);
    .loading-spinner {
      width: 24px;
      height: 24px;
      border-width: 2px;
    }
    p {
      margin: 0;
      font-size: 13px;
    }
  }

  /* 组件错误状态 */
  .component-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--el-color-danger);
    p {
      margin: 0;
      font-size: 14px;
    }
  }

  /* 兼容旧的spinner样式 */
  .spinner {
    width: 24px;
    height: 24px;
    margin: 0 auto;
    border: 3px solid var(--el-border-color-light);
    border-top-color: var(--el-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
