<template>
  <div class="more-info-container">
    <div class="item-main" @click="openMoreInfo">
      <div class="more-trigger">
        <el-tooltip :content="t('layout.header.moreInfo.tooltip')" :placement="place as toolTipTypes">
          <i class="iconfont toolBar-icon">
            <svg-icon icon="eva:grid-fill" class="grid-icon"></svg-icon>
            <span v-if="globalStore.checkColumnLayout()" class="grid-text">{{ t("layout.header.moreInfo.title") }}</span>
          </i>
        </el-tooltip>
      </div>
    </div>

    <el-dialog
      :title="t('layout.header.moreInfo.title')"
      :class="[globalStore.layout == 'transverse' ? 'dialog-more-horizontal' : 'dialog-more']"
      v-model="isShowMore"
      height="320"
      width="380"
      style="border-radius: 10px"
      :modal="false"
      :append-to-body="true"
      :destroy-on-close="false"
      :close-on-click-modal="true"
    >
      <div class="grid-container compact">
        <div
          v-for="(item, index) in items"
          :key="item.id"
          @click="execute(item)"
          class="grid-item compact"
          :style="{ animationDelay: `${index * 0.07}s` }"
        >
          <div class="item-content compact">
            <div class="icon-wrapper compact" :style="{ '--icon-color': item.color }">
              <svg-icon :icon="item.icon" class="item-icon compact"></svg-icon>
            </div>
            <span class="item-text compact">{{ t(item.name) }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- <SearchMenu id="searchMenu" ref="searchMenu" /> -->
    <AboutDialog id="aboutDialog" ref="aboutDialog" />
    <EnginConfigDialog :engin-config-param="enginConfigParam" id="enginConfigDialog" ref="enginConfigDialog" />
    <HelpDialog id="helpDialog" ref="helpDialog" />
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules";
import Message from "@/scripts/message";
// import SearchMenu from "@/layouts/components/Header/components/SearchMenu.vue";
import AboutDialog from "@/layouts/components/Header/components/AboutDialog.vue";
import EnginConfigDialog from "@/layouts/components/Header/components/EnginConfigDialog.vue";
import HelpDialog from "@/layouts/components/Header/components/HelpDialog.vue";
import { windowControlApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const isShowMore = ref(false);
// const searchMenu = ref<InstanceType<typeof SearchMenu>>();
const aboutDialog = ref<InstanceType<typeof AboutDialog>>();
const enginConfigDialog = ref<InstanceType<typeof EnginConfigDialog>>();
const helpDialog = ref<InstanceType<typeof HelpDialog>>();
const globalStore = useGlobalStore();
const place = defineModel<toolTipTypes>();
const items = [
  {
    id: 1,
    name: "layout.header.moreInfo.items.importConfig",
    code: "importConfig",
    icon: "ep:upload",
    path: "/",
    color: "#10b981"
  },
  {
    id: 2,
    name: "layout.header.moreInfo.items.exportConfig",
    code: "exportConfig",
    icon: "ep:download",
    path: "/",
    color: "#f59e0b"
  },
  // {
  //   id: 3,
  //   name: "layout.header.moreInfo.items.search",
  //   code: "search",
  //   icon: "ep:search",
  //   path: "/",
  //   color: "#8b5cf6"
  // },
  {
    id: 4,
    name: "layout.header.moreInfo.items.printScreen",
    code: "printScreen",
    icon: "ep:camera",
    path: "/",
    color: "#3b82f6"
  },
  {
    id: 5,
    name: "layout.header.moreInfo.items.help",
    code: "help",
    icon: "ep:question-filled",
    path: "/",
    color: "#22c55e"
  },
  {
    id: 6,
    name: "layout.header.moreInfo.items.about",
    code: "about",
    icon: "ep:info-filled",
    path: "/",
    color: "#ef4444"
  }
];
const enginConfigParam = ref({ type: "", title: "", placeholder: "" });
const openMoreInfo = () => {
  isShowMore.value = true;
};

const execute = item => {
  switch (item.code) {
    // case "search":
    //   showSearchMenu();
    //   break;
    case "about":
      showAboutDialog();
      break;
    case "printScreen":
      printScreen();
      break;
    case "importConfig":
      importConfig();
      break;
    case "exportConfig":
      exportConfig();
      break;
    case "help":
      if (helpDialog.value) {
        helpDialog.value.openDialog();
      }
      break;
    default:
      Message.warning(t(item.name) + "功能--待开发");
      break;
  }
};

// const showSearchMenu = () => {
//   if (searchMenu.value) {
//     searchMenu.value.handleOpen();
//   }
// };

const showAboutDialog = () => {
  if (aboutDialog.value) {
    aboutDialog.value.openDialog();
  }
};

const printScreen = (): void => {
  windowControlApi.printScreen();
};

const importConfig = (): void => {
  enginConfigParam.value.type = "import";
  enginConfigParam.value.title = t("layout.header.moreInfo.importConfig.title");
  enginConfigParam.value.placeholder = t("layout.header.moreInfo.importConfig.placeholder");
  if (enginConfigDialog.value) {
    enginConfigDialog.value.openDialog();
  }
};

const exportConfig = (): void => {
  enginConfigParam.value.type = "export";
  enginConfigParam.value.title = t("layout.header.moreInfo.exportConfig.title");
  enginConfigParam.value.placeholder = t("layout.header.moreInfo.exportConfig.placeholder");
  if (enginConfigDialog.value) {
    enginConfigDialog.value.openDialog();
  }
};
</script>

<style lang="scss">
.more-info-container {
  display: contents;
}
.item-main {
  display: flex;
  flex-flow: column wrap;
  place-content: space-around center;
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}
.more-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
}
.toolBar-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  .grid-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
  }
  .grid-text {
    width: 100%;
    margin-top: 6px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
  }
}
.dialog-more {
  position: absolute !important;
  bottom: 40px;
  left: 80px;
  margin: 0 !important;
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 20px;
    .grid-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100px;
      overflow: hidden;
      cursor: pointer;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 2px solid transparent;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      [class="dark"] & {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        box-shadow: 0 2px 8px rgb(0 0 0 / 30%);
      }
      &::before {
        position: absolute;
        inset: 0;
        content: "";
        background: linear-gradient(135deg, var(--el-color-primary-light-8) 0%, var(--el-color-primary-light-9) 100%);
        border-radius: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
        [class="dark"] & {
          background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
        }
      }
      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
        transform: translateY(-4px);
        [class="dark"] & {
          border-color: #8cd2ff;
          box-shadow: 0 8px 25px rgb(0 0 0 / 40%);
        }
        &::before {
          opacity: 1;
        }
        .item-content {
          transform: scale(1.05);
        }
        .icon-wrapper {
          background: var(--icon-color, var(--el-color-primary));
          box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
          transform: scale(1.1);
          &::before {
            opacity: 1;
          }
          .item-icon {
            color: white;
            filter: drop-shadow(0 2px 4px rgb(0 0 0 / 20%));
            transform: scale(1.1);
          }
        }
      }
      &:active {
        transition: all 0.1s ease;
        transform: translateY(-2px);
      }
      .item-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        .icon-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          overflow: hidden;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
          border: 1px solid rgb(255 255 255 / 80%);
          border-radius: 12px;
          box-shadow:
            0 2px 4px rgb(0 0 0 / 5%),
            inset 0 1px 0 rgb(255 255 255 / 60%);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          &::before {
            position: absolute;
            inset: 0;
            content: "";
            background: linear-gradient(
              135deg,
              var(--icon-color, var(--el-color-primary)) 0%,
              color-mix(in srgb, var(--icon-color, var(--el-color-primary)) 80%, white) 100%
            );
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          .item-icon {
            position: relative;
            z-index: 1;
            font-size: 24px;
            color: var(--icon-color, var(--el-color-primary));
            filter: drop-shadow(0 1px 2px rgb(0 0 0 / 10%));
            transition: all 0.3s ease;
            [class="dark"] & {
              color: #8cd2ff;
            }
          }
        }
        .item-text {
          max-width: 80px;
          font-size: 12px;
          font-weight: 500;
          line-height: 1.4;
          color: var(--el-text-color-primary);
          text-align: center;
          word-break: break-word;
          transition: color 0.3s ease;
          [class="dark"] & {
            color: #b0b8c7;
          }
        }
      }
    }
  }
  .el-dialog {
    --el-dialog-padding-primary: 0;

    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgb(0 0 0 / 15%);
    [class="dark"] & {
      background: rgb(30 41 59 / 95%);
      box-shadow: 0 20px 60px rgb(0 0 0 / 40%);
    }
  }
  .el-dialog__header {
    padding: 20px 20px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    .el-dialog__title {
      font-weight: 600;
      color: var(--el-text-color-primary);
      [class="dark"] & {
        color: #8cd2ff;
      }
    }
    [class="dark"] & {
      border-bottom: 1px solid #334155;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}
.dialog-more-horizontal {
  position: absolute !important;
  top: 50px;
  right: 20px;
  margin: 0 !important;
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 20px;
    .grid-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100px;
      overflow: hidden;
      cursor: pointer;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 2px solid transparent;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      &::before {
        position: absolute;
        inset: 0;
        content: "";
        background: linear-gradient(135deg, var(--el-color-primary-light-8) 0%, var(--el-color-primary-light-9) 100%);
        border-radius: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      &:hover {
        border-color: var(--el-color-primary-light-5);
        box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
        transform: translateY(-4px);
        &::before {
          opacity: 1;
        }
        .item-content {
          transform: scale(1.05);
        }
        .icon-wrapper {
          background: var(--icon-color, var(--el-color-primary));
          box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
          transform: scale(1.1);
          &::before {
            opacity: 1;
          }
          .item-icon {
            color: white;
            filter: drop-shadow(0 2px 4px rgb(0 0 0 / 20%));
            transform: scale(1.1);
          }
        }
      }
      &:active {
        transition: all 0.1s ease;
        transform: translateY(-2px);
      }
      .item-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        .icon-wrapper {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          overflow: hidden;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
          border: 1px solid rgb(255 255 255 / 80%);
          border-radius: 12px;
          box-shadow:
            0 2px 4px rgb(0 0 0 / 5%),
            inset 0 1px 0 rgb(255 255 255 / 60%);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          &::before {
            position: absolute;
            inset: 0;
            content: "";
            background: linear-gradient(
              135deg,
              var(--icon-color, var(--el-color-primary)) 0%,
              color-mix(in srgb, var(--icon-color, var(--el-color-primary)) 80%, white) 100%
            );
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
          .item-icon {
            position: relative;
            z-index: 1;
            font-size: 24px;
            color: var(--icon-color, var(--el-color-primary));
            filter: drop-shadow(0 1px 2px rgb(0 0 0 / 10%));
            transition: all 0.3s ease;
          }
        }
        .item-text {
          max-width: 80px;
          font-size: 12px;
          font-weight: 500;
          line-height: 1.4;
          color: var(--el-text-color-primary);
          text-align: center;
          word-break: break-word;
          transition: color 0.3s ease;
        }
      }
    }
  }
  .el-dialog {
    --el-dialog-padding-primary: 0;

    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgb(0 0 0 / 15%);
  }
  .el-dialog__header {
    padding: 20px 20px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    .el-dialog__title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}

// 添加暗色主题支持
@media (prefers-color-scheme: dark) {
  .dialog-more,
  .dialog-more-horizontal {
    .grid-container .grid-item {
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      .item-content .icon-wrapper {
        background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%);
        border: 1px solid rgb(255 255 255 / 10%);
        box-shadow:
          0 2px 4px rgb(0 0 0 / 20%),
          inset 0 1px 0 rgb(255 255 255 / 10%);
      }
      .item-content .item-text {
        color: var(--el-text-color-primary);
      }
    }
    .el-dialog {
      background: rgb(30 41 59 / 95%);
    }
  }
}

// 添加动画关键帧
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
.grid-item {
  opacity: 0;
  animation: fade-in-up 0.6s ease-out forwards;
}

// 响应式设计
@media (width <= 480px) {
  .dialog-more,
  .dialog-more-horizontal {
    .grid-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      padding: 16px;
      .grid-item {
        height: 80px;
        .item-content {
          gap: 8px;
          .icon-wrapper {
            width: 40px;
            height: 40px;
            .item-icon {
              font-size: 20px;
            }
          }
          .item-text {
            max-width: 60px;
            font-size: 11px;
          }
        }
      }
    }
  }
}

@media (width <= 320px) {
  .dialog-more,
  .dialog-more-horizontal {
    .grid-container {
      grid-template-columns: 1fr;
      gap: 10px;
      .grid-item {
        height: 70px;
        .item-content {
          flex-direction: row;
          gap: 12px;
          .icon-wrapper {
            width: 36px;
            height: 36px;
            .item-icon {
              font-size: 18px;
            }
          }
          .item-text {
            max-width: none;
            font-size: 12px;
            text-align: left;
          }
        }
      }
    }
  }
}
.grid-container.compact {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  padding: 12px;
}
.grid-item.compact {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 68px;
  cursor: pointer;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 10px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 5%);
  transition: all 0.2s;
  &:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}
.item-content.compact {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}
.icon-wrapper.compact {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  [class="dark"] & {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 1px solid #334155;
  }
}
.item-icon.compact {
  font-size: 18px;
}
.item-text.compact {
  max-width: 60px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: var(--el-text-color-primary);
  text-align: center;
}
.dialog-more,
.dialog-more-horizontal {
  .el-dialog {
    width: 380px !important;
    min-width: 320px;
    border-radius: 10px;
  }
  .el-dialog__header {
    padding: 12px 16px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    .el-dialog__title {
      font-size: 15px;
      font-weight: 600;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}

@media (width <= 480px) {
  .grid-container.compact {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 8px;
  }
  .grid-item.compact {
    height: 54px;
  }
  .icon-wrapper.compact {
    width: 24px;
    height: 24px;
    border-radius: 6px;
  }
  .item-icon.compact {
    font-size: 14px;
  }
  .item-text.compact {
    max-width: 40px;
    font-size: 11px;
  }
}
</style>
