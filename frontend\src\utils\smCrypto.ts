/**
 * @description smCrypto 加密解密工具
 * @license Apache License Version 2.0
 */

import smCrypto from "sm-crypto";
const { sm2 } = smCrypto;
const cipherMode = 0; // 1 - C1C3C2，0 - C1C2C3，默认为1
const publicKey =
  "04BD62406DF6789B1FBE8C457AECAE6D7C806CDB39316F190519905C24DF395E8952C47798D76ADECF8CA28C935702AFCDD9B17DE77121FA6448F0EDEFBD8365D6";

/**
 * 国密加解密工具类
 */
export default {
  // SM2加密
  doSm2Encrypt(msgString: string) {
    return sm2.doEncrypt(msgString, publicKey, cipherMode);
  }
};
