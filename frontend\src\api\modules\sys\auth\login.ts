import { Login } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/auth/b/");

const loginApi = {
  /** 用户登录 */
  login(params: Login.LoginForm) {
    return http.post<Login.Login>("login", params, { loading: false }); // 正常 post json 请求  =  application/json
  },
  /** 获取验证码 */
  picCaptcha() {
    return http.get<Login.ReqValidCode>("getPicCaptcha", {}, { loading: false });
  },
  /** 用户退出登录 */
  logout(params: Login.Logout) {
    return http.post("logout", params);
  },
  /** 获取用户信息 */
  getLoginUser() {
    return http.get<Login.LoginUserInfo>("getLoginUser", {}, { loading: false });
  },
  /** 获取短信验证码 */
  getPhoneValidCode(params: Login.ReqPhoneValidCode) {
    return http.get<string>("getPhoneValidCode", params, { loading: false });
  },
  /** 手机号登录 */
  loginByPhone(params: Login.PhoneLoginForm) {
    return http.post<Login.Login>("loginByPhone", params, { loading: false });
  }
};

export { loginApi };
