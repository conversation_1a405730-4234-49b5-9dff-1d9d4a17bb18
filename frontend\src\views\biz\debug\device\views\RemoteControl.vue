<template>
  <div class="table-box">
    <ProTable
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      @search="getData"
      v-loading="tableLoading"
      :init-param="initParam"
      :request-auto="false"
      highlight-current-row
      :pagination="false"
      :data-callback="dataCallback"
      row-key="name"
      :max-height="getTableMaxHeight(220)"
      :stripe="true"
      :border="true"
      :tool-button="false"
      table-key="remoteControl"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="SemiSelect" @click="semiSelect(scope.row)">{{ t("device.remoteControl.directControl") }}</el-button>
        <el-button type="primary" link :icon="SuitcaseLine" @click="suitcaseLine(scope.row)">{{ t("device.remoteControl.selectControl") }}</el-button>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
    <ProgressDialog ref="progressDialog"></ProgressDialog>
  </div>
</template>

<script setup lang="tsx">
import { DebugInfoItem, YkInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { CtlCheck, CtlMode, MenuIdName, RemoteStatus, SelectRequestData } from "@/api/interface/biz/debug/remote";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { SemiSelect, SuitcaseLine } from "@element-plus/icons-vue";
import { getTableMaxHeight } from "@/utils/index";
import { useDebugStore } from "@/stores/modules/debug";
import { deviceInfoMenutreeApi } from "@/api/modules/biz/debug";
import { ResultData } from "@/api/interface";
import { remoteControlApi } from "@/api/modules/biz/debug/remoteoperate";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { currDevice, debugIndex, addConsole } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const tableData = ref<YkInfoItem[]>([]);
const tableLoading = ref(false);
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
// ProTable 实例
const tableRef = ref<ProTableInstance>();

const semiSelect = async (row: any) => {
  menuClick(row.ctlCheck, CtlMode.DIRECT, row.ctlValue ? "2" : "1", row);
  console.log(row.ctlCheck, CtlMode.DIRECT, row.ctlValue ? "2" : "1", row);
};
const suitcaseLine = async (row: any) => {
  menuClick(row.ctlCheck, CtlMode.SBO, row.ctlValue ? "2" : "1", row);
  console.log(row);
};
const columns = reactive<ColumnProps[]>([
  {
    type: "index",
    label: t("device.remoteControl.sequence"),
    width: 60
  },
  {
    prop: "name",
    label: t("device.remoteControl.shortAddress"),
    width: 250,
    search: {
      el: "input",
      tooltip: t("device.remoteControl.shortAddress"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            tableRef.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    label: t("device.remoteControl.description"),
    search: {
      el: "input",
      tooltip: t("device.remoteControl.description"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            tableRef.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "ctlValue",
    label: t("device.remoteControl.control"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-switch
            size="small"
            v-model={scope.row.ctlValue}
            active-text={t("device.remoteControl.controlOpen")}
            inactive-text={t("device.remoteControl.controlClose")}
            style="height:12px;"
            onChange={(value: string) => handleChange(value, scope.row)}
          />
        </div>
      );
    }
  },
  {
    prop: "ctlCheck",
    label: t("device.remoteControl.type"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-select v-model={scope.row.ctlCheck} size="small" onChange={(value: string) => handleChange(value, scope.row)}>
            <el-option key="no_check" label={t("device.remoteControl.noCheck")} value="no_check"></el-option>
            <el-option key="sync_check" label={t("device.remoteControl.syncCheck")} value="sync_check"></el-option>
            <el-option key="dead_check" label={t("device.remoteControl.deadCheck")} value="dead_check"></el-option>
          </el-select>
        </div>
      );
    }
  },
  { prop: "operation", label: t("device.remoteControl.operation"), fixed: "right", width: 250 }
]);

// const showLoading = () => {
//   progressDialog.value.show();
// };
// const hideLoading = () => {
//   progressDialog.value.hide();
// };
const dataCallback = (data: any) => {
  console.log("dataCallback:", data);
  return {
    list: data.list,
    total: data.total
  };
};

const initParam = reactive({ type: 1 });

onMounted(() => {
  getData();
});
async function getData() {
  // showLoading();
  try {
    console.log("getData");
    let newParams = JSON.parse(JSON.stringify(tableRef.value?.searchParam));
    console.log(newParams);
    const param: MenuIdName = {
      id: currDevice.id,
      type: "submenu",
      names: [debugIndex.compData.get(currDevice.id).pname, debugIndex.compData.get(currDevice.id).name]
    };
    const res: ResultData<DebugInfoItem[]> = await deviceInfoMenutreeApi.getDeviceMenuItemByDevice(props.deviceId, param);
    if (!res.data) {
      throw new Error("Invalid response structure");
    }
    let resultData = res.data;
    if (newParams.name) {
      resultData = resultData?.filter(data => {
        return data.name.toLowerCase().includes(newParams.name.toLowerCase());
      });
      console.log(resultData);
    }
    if (newParams.desc) {
      resultData = resultData?.filter(data => {
        return data.desc.toLowerCase().includes(newParams.desc.toLowerCase());
      });
      console.log(resultData);
    }
    const result = resultData?.map(item => ({
      ...item,
      ctlValue: false,
      ctlCheck: CtlCheck.NO_CHECK
    }));
    tableData.value = result;
    tableRef.value?.refresh();
  } finally {
    // hideLoading();
  }
}

const menuClick = async (command: RemoteStatus, ctlMode: CtlMode, ctlValue: string, row: any): Promise<void> => {
  progressDialog.value?.show();
  const param: SelectRequestData = {
    /** 控制源即name */
    ctlObj: row.name,
    /** 值 */
    ctlVal: ctlValue,
    /** 模式 sbo=选控，direct=直控 */
    ctlModel: ctlMode,
    /** 当前时间iso8601格式 */
    opemTm: new Date().toISOString(),
    test: "0",
    check: command
  };
  try {
    if (ctlMode == CtlMode.DIRECT) {
      await remoteConfirm(param, "execute", row);
    } else {
      await remoteTrasmisson(param, row);
    }
  } finally {
    progressDialog.value?.hide();
  }
};

async function remoteTrasmisson(param: SelectRequestData, row: any) {
  console.log(param);
  const res = await remoteControlApi.ykSelectWithValueByDevice(props.deviceId, param);
  if (res.code == 0) {
    ElMessageBox.confirm(t("device.remoteControl.confirmSuccess"), t("device.remoteControl.confirmInfo"), {
      confirmButtonText: t("device.remoteControl.execute"),
      cancelButtonText: t("device.remoteControl.cancel"),
      closeOnClickModal: false,
      type: "info"
    })
      .then(() => {
        remoteConfirm(param, "execute", row);
      })
      .catch(() => {
        remoteConfirm(param, "abort", row);
      });
  } else if (res.code == 2) {
    if (res.data) {
      ElMessageBox.alert(res.msg, t("device.remoteControl.errorInfo"), {
        confirmButtonText: t("device.remoteControl.execute"),
        type: "error"
      });
      addConsole(row.desc + t("device.remoteControl.executeFailed") + res.msg);
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteControl.errorInfo"), {
        confirmButtonText: t("device.remoteControl.execute"),
        type: "error"
      });
      addConsole(row.desc + t("device.remoteControl.executeFailed") + res.msg);
    }
  } else {
    ElMessageBox.alert(res.msg, t("device.remoteControl.errorInfo"), {
      confirmButtonText: t("device.remoteControl.execute"),
      type: "error"
    });
    addConsole(row.desc + t("device.remoteControl.executeFailed") + res.msg);
  }
}

const remoteConfirm = async (param: SelectRequestData, command: string, row: any) => {
  if (command == "execute") {
    const res = await remoteControlApi.ykSelectValueConfirmByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.alert(t("device.remoteControl.executeSuccess"), t("device.remoteControl.promptInfo"), {
        confirmButtonText: t("device.remoteControl.confirm"),
        type: "success"
      });
      addConsole(row.desc + t("device.remoteControl.remoteExecuteSuccess"));
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteControl.errorInfo"), {
        confirmButtonText: t("device.remoteControl.confirm"),
        type: "error"
      });
      addConsole(row.desc + t("device.remoteControl.executeFailed") + res.msg);
    }
  } else {
    const res = await remoteControlApi.ykSelectValueCancelByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.alert(t("device.remoteControl.cancelSuccess"), t("device.remoteControl.promptInfo"), {
        confirmButtonText: t("device.remoteControl.confirm"),
        type: "success"
      });
      addConsole(row.desc + t("device.remoteControl.remoteCancelSuccess"));
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteControl.errorInfo"), {
        confirmButtonText: t("device.remoteControl.confirm"),
        type: "error"
      });
      addConsole(row.desc + t("device.remoteControl.cancelFailed") + res.msg);
    }
  }
};

watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      console.log("grpName", newValue);
      tableRef.value?.reset();
      getData();
    }
  },
  { deep: true }
);
</script>
<style lang="css">
.top {
  margin-bottom: 10px;
}
</style>
