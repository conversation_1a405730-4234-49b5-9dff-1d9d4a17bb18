import { moduleIpcRequest } from "@/api/request";
import { ResultData } from "@/api";

export interface CustomReport {
  uuid: string;
  name: string;
  newname?: string;
  desc: string;
  keyword: string;
  inherit: string;
  fc: string;
  method: string;
}

export interface SelectedItem {
  name: string;
  desc: string;
  grp?: string;
  inf?: string;
  fc?: string;
  unit?: string;
  type?: string;
}

export interface CustomGroup {
  keyword: any;
  inherit: any;
  uuid: string;
  name: string;
  newname?: string;
  desc: string;
  fc?: string;
  method?: string;
  reports?: CustomGroup[]; // 组下的子Menu即为报告节点
  items?: SelectedItem[]; // 自定义组内选择的点
}

export interface CustomInfo {
  configVersion: string;
  menus: CustomGroup[];
}

export interface LGReport {
  name: string;
  desc: string;
  fc: string;
  method: string;
}

const ipc = moduleIpcRequest("controller/debug/custominfo/");

export const customInfoApi = {
  /** 获取所有自定义组和报告（指定 deviceId） */
  getAllGroupsByDevice(deviceId: string): Promise<ResultData<CustomInfo>> {
    return ipc.iecInvokeWithDevice<CustomInfo>("getAllGroups", undefined, deviceId);
  },

  /** 验证菜单名称唯一性（指定 deviceId） */
  validateMenuNameByDevice(deviceId: string, name: string): Promise<ResultData<{ isValid: boolean; message?: string }>> {
    return ipc.iecInvokeWithDevice<{ isValid: boolean; message?: string }>("validateMenuName", { name }, deviceId);
  },

  /** 新增自定义组（指定 deviceId） */
  addMenuByDevice(deviceId: string, group: CustomGroup): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("addMenu", { group }, deviceId);
  },

  /** 编辑自定义组（指定 deviceId） */
  editMenuByDevice(deviceId: string, uuid: string, newGroup: CustomGroup): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("editMenu", { uuid, newGroup }, deviceId);
  },

  /** 删除自定义组（指定 deviceId） */
  deleteMenuByDevice(deviceId: string, uuid: string): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("deleteMenu", { uuid }, deviceId);
  },

  /** 新增自定义报告（指定 deviceId） */
  addReportByDevice(deviceId: string, groupUuid: string, report: CustomReport & { newname?: string }): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("addReport", { groupUuid, report }, deviceId);
  },

  /** 编辑自定义报告（指定 deviceId） */
  editReportByDevice(
    deviceId: string,
    groupUuid: string,
    reportUuid: string,
    newReport: CustomReport & { newname?: string }
  ): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("editReport", { groupUuid, reportUuid, newReport }, deviceId);
  },

  /** 删除自定义报告（指定 deviceId） */
  deleteReportByDevice(deviceId: string, groupUuid: string, reportUuid: string): Promise<ResultData<any>> {
    return ipc.iecInvokeWithDevice<any>("deleteReport", { groupUuid, reportUuid }, deviceId);
  },

  /** 获取可继承报告（LG报告）（指定 deviceId） */
  getLGReportsByDevice(deviceId: string): Promise<ResultData<LGReport[]>> {
    return ipc.iecInvokeWithDevice<LGReport[]>("getLGReports", undefined, deviceId);
  },

  /** 获取所有 fc 列表（指定 deviceId） */
  getFcListByDevice(deviceId: string): Promise<ResultData<string[]>> {
    return ipc.iecInvokeWithDevice<string[]>("getFcList", undefined, deviceId);
  },

  /** 根据 fc 获取 TAB 菜单及点（指定 deviceId） */
  getMenusByFcByDevice(deviceId: string, fc: string): Promise<ResultData<any[]>> {
    return ipc.iecInvokeWithDevice<any[]>("getMenusByFc", { fc }, deviceId);
  }
};
