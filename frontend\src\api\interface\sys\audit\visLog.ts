import { ReqPage } from "@/api";

export namespace VisLog {
  /** 访问日志分页查询 */
  export interface Page extends ReqPage {
    /** 访问日志分类 */
    category: string;
  }

  /** 访问日志详情 */
  export interface VisLogInfo {
    /** id */
    id: number;
    /** 访问日志分类 */
    category: string;
    /** 访问日志标题 */
    name: string;
    /** 执行状态 */
    exeStatus: string;
    /** 操作Ip */
    opIp: string;
    /** 操作地址 */
    opAddress: string;
    /** 操作浏览器 */
    opBrowser: string;
    /** 操作系统 */
    opOs: string;
    /** 操作时间 */
    opTime: string;
    /** 操作人 */
    opUser: string;
    /** 操作账号 */
    opAccount: string;
    /** 操作时间 */
    createTime: string;
  }

  /** 折线图 */
  export interface LineChart {
    /** 日期 */
    date: string;
    /** 登入量 */
    loginCount: number;
    /** 登出量 */
    logoutCount: number;
  }

  /** 饼图 */
  export interface PineChart {
    /** 类型 */
    type: string;
    /** 数量 */
    value: number;
  }
}
