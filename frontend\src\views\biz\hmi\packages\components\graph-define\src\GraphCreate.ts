import { Cell, Graph } from "@antv/x6";
import { getCellStatus, getSelectedCells, isCbrDis } from "../../..//graph/GraphUtil";
import { EquipmentData, EquipmentItem, EquipmentStatus, EquipmentType, GraphResult } from "../../..//graph/Graph";
import { Export } from "@antv/x6-plugin-export";

class GraphCreate {
  graph: Graph;
  private t: (key: string) => string;

  constructor(graph: Graph, t: (key: string) => string) {
    this.graph = graph;
    this.t = t;
  }

  async create(data: { name: string; type: EquipmentType }): Promise<GraphResult<EquipmentData>> {
    const result = new GraphResult<EquipmentData>(0);
    // 根据不同的图符类型，校验图符正确性
    const selectedCells = getSelectedCells(this.graph);
    if (isCbrDis(data.type)) {
      if (selectedCells.length != 2) {
        result.msg = this.t("graphDefine.graphCreate.needTwoDevices");
        return result;
      }
      // 校验开关刀闸的状态是否对
      let hasOpen = false;
      let hasClose = false;
      for (const cell of selectedCells) {
        const status = getCellStatus(cell.value);
        if (status == EquipmentStatus.OPEN) {
          hasOpen = true;
        } else if (status == EquipmentStatus.CLOSE) {
          hasClose = true;
        }
      }
      if (!(hasOpen && hasClose)) {
        result.msg = this.t("graphDefine.graphCreate.needCorrectStatus");
        return result;
      }
    } else if (selectedCells.length != 1) {
      result.msg = this.t("graphDefine.graphCreate.needOneDevice");
      return result;
    }
    const componentList: EquipmentItem[] = [];
    //  为每个图符生成图片
    for (const cell of selectedCells) {
      const url = await this.getCellImage(cell.value);
      componentList.push({
        data: cell,
        img: url,
        equipmentStatus: getCellStatus(cell.value)
      });
    }
    const equipmentData: EquipmentData = {
      id: "",
      name: data.name,
      type: data.type,
      components: componentList
    };
    result.data = equipmentData;
    result.setSuccess();
    return result;
  }

  getCellImage(cell: Cell) {
    const promise = new Promise((resolve: (value: string) => void) => {
      const options: Export.ToImageOptions = {
        preserveDimensions: true,
        backgroundColor: "#fff"
      };
      options.viewBox = cell.getBBox();
      this.graph.toJPEG(url => {
        resolve(url);
      }, options);
    });
    return promise;
  }
}
export default GraphCreate;
