export default {
  equipmentList: {
    sequence: "Index",
    name: "Name",
    type: "Type",
    operation: "Operation",
    preview: "Preview",
    copy: "Copy",
    delete: "Delete",
    confirmDelete: "Are you sure to delete?",
    tip: "Tip",
    error: "Error"
  },
  graphComponent: {
    deviceType: "Device Type",
    deviceName: "Device Name",
    save: "Save"
  },
  contextMenu: {
    group: "Group",
    ungroup: "Ungroup",
    setStatus: "Set Status",
    copy: "Copy",
    delete: "Delete",
    rename: "Rename"
  },
  graphCreate: {
    needTwoDevices: "Switch or disconnector requires two device symbols selected",
    needCorrectStatus: "Please set the correct status property for the switch or disconnector",
    needOneDevice: "Please select a device symbol"
  },
  graphDefine: {
    waitCanvasInit: "Please wait for canvas initialization to complete",
    selectOneGraph: "Please select a symbol",
    tip: "Tip"
  },
  setStatus: {
    open: "Open",
    close: "Close",
    none: "None"
  },
  graphTools: {
    undo: "Undo",
    redo: "Redo",
    front: "Bring to Front",
    back: "Send to Back",
    delete: "Delete",
    save: "Save",
    equipmentList: "Equipment List"
  },
  graphEditor: {
    dataConfig: "Data Configuration",
    loadEquipmentFailed: "Failed to load symbol"
  }
};
