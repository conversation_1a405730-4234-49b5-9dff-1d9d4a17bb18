export default {
  checkCard: {
    default: "默认"
  },
  chooseModule: {
    title: "选择应用",
    noModule: "未找到模块!",
    setDefault: "设为默认",
    cancel: "取消",
    confirm: "确定"
  },
  closer: {
    title: "退出确认",
    message: "是否要退出？",
    confirm: "确 定",
    minimize: "最小化到托盘",
    cancel: "取 消"
  },
  codeHighLight: {
    noCode: "无"
  },
  cropUpload: {
    title: "图片裁剪",
    zoomIn: "放大",
    zoomOut: "缩小",
    rotateLeft: "向左旋转",
    rotateRight: "向右旋转",
    uploadImage: "点击上传图片",
    uploadTip: "请上传图片文件，建议不超过2M",
    cancel: "取消",
    confirm: "确认"
  },
  error: {
    forbidden: "抱歉，您无权访问该页面~🙅‍♂️🙅‍♀️",
    notFound: "抱歉，您访问的页面不存在~🤷‍♂️🤷‍♀️",
    serverError: "抱歉，您的网络不见了~🤦‍♂️🤦‍♀️",
    back: "返回上一页"
  },
  form: {
    input: {
      placeholder: "请填写{label}"
    },
    select: {
      placeholder: "请选择{label}"
    },
    button: {
      add: "新增",
      edit: "编辑",
      delete: "删除",
      view: "查看"
    },
    search: {
      inputPlaceholder: "请输入",
      selectPlaceholder: "请选择",
      rangeSeparator: "至",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  selectIcon: {
    title: "图标选择",
    placeholder: "请选择图标",
    searchPlaceholder: "搜索图标",
    noSearchResult: "未搜索到您要找的图标~",
    moreIcons: "更多图标",
    enterIconifyCode: "请输入您想要的图标iconify代码,如mdi:home-variant",
    iconifyAddress: "iconify地址",
    localIcons: "本地图标"
  },
  selector: {
    add: "添加",
    addCurrent: "添加当前",
    addSelected: "添加选中",
    delete: "删除",
    deleteCurrent: "删除当前",
    deleteSelected: "删除选中",
    cancel: "取消",
    confirm: "确定",
    selected: "已选择",
    maxSelect: "最多选择",
    singleSelectOnly: "只可选择一条",
    maxSelectLimit: "最多选择{count}条",
    person: "人"
  },
  upload: {
    view: "查看",
    edit: "编辑",
    delete: "删除",
    uploadImage: "请上传图片",
    uploadSuccess: "图片上传成功！",
    uploadFailed: "图片上传失败，请您重新上传！",
    invalidFormat: "上传图片不符合所需的格式！",
    fileSizeExceeded: "上传图片大小不能超过 {size}M！",
    maxFilesExceeded: "当前最多只能上传 {limit} 张图片，请移除后上传！",
    fileSizeZero: "文件 {fileName} 大小为0，无法上传！",
    tips: "温馨提示"
  },
  treeFilter: {
    searchPlaceholder: "输入关键字进行过滤",
    expandAll: "展开全部",
    collapseAll: "折叠全部",
    all: "全部"
  },
  proTable: {
    search: {
      reset: "重置",
      search: "搜索",
      expand: "展开",
      collapse: "收起"
    },
    pagination: {
      total: "共 {total} 条",
      pageSize: "条/页",
      goto: "前往",
      page: "页"
    },
    colSetting: {
      title: "列设置",
      fixedLeft: "是否显示",
      fixedRight: "是否排序",
      cancelFixed: "取消固定",
      reset: "恢复默认",
      confirm: "确定",
      cancel: "取消"
    },
    table: {
      empty: "暂无数据"
    }
  },
  basicComponent: {
    title: "基础组件",
    line: "线",
    text: "文本",
    rect: "矩形",
    circle: "圆形",
    ellipse: "椭圆",
    triangle: "三角形",
    arc: "圆弧"
  }
};
