import fspromise from "node:fs/promises";
import path from "node:path";
import { getUUID } from "../../utils/common";
import { EquipmentData } from "../../interface/hmi/graph";
import { pathExists } from "iec-common/dist/file";
import { ApiResponse } from "../../data/debug/apiResponse";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { t } from "../../data/i18n/i18n";
/**
 * 服务提供
 * <AUTHOR>
 * @version 1.0
 */
class GraphDefineService {
  equipmentPath: string;

  /**
   *
   * @param equipmentPath 自定义图符的文件路径
   */
  constructor(equipmentPath: string) {
    this.equipmentPath = equipmentPath;
  }

  /**
   * 添加自定义设备
   * @param data
   * @returns
   */
  async addEquipment(data: EquipmentData): Promise<ApiResponse> {
    const dataList = await this.getEquipmentList();
    for (const item of dataList) {
      if (item.name == data.name) {
        return handleCustomResponse(
          ERROR_CODES.INTERNAL_ERROR,
          t("hmi.deviceNameExists")
        );
      }
    }
    // 校验是否重名
    if (data.id == undefined || data.id.trim() == "") {
      data.id = getUUID();
    }
    // 设置数据
    let index = 0;
    for (const item of data.components) {
      item.data.value.data = {
        ...item.data.value.data,
        // 添加图符信息到data属性
        equipmentData: {
          equipmentId: data.id,
          equipmentIndex: index++,
          quipmentStatus: item.equipmentStatus,
          equipmentType: data.type,
        },
      };
    }
    dataList.push(data);
    await this.writeEquipment(dataList);
    return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
  }
  /**
   * 删除设备
   * @param id
   * @returns
   */
  async RemoveEquipment(id: string): Promise<boolean> {
    const dataList = await this.getEquipmentList();
    const diffList: EquipmentData[] = [];
    for (const data of dataList) {
      if (data.id != id) {
        diffList.push(data);
      }
    }
    await this.writeEquipment(diffList);
    return true;
  }

  /**
   * 查询设备
   * @param path
   * @returns
   */
  async getEquipmentList(): Promise<EquipmentData[]> {
    if (!pathExists(this.equipmentPath, "file")) {
      return [];
    }
    try {
      const buffer = await fspromise.readFile(this.equipmentPath);
      const json = JSON.parse(buffer.toString());
      return json;
    } catch (e) {
      console.log(e);
    }
    return [];
  }

  private async writeEquipment(dataList: EquipmentData[]) {
    const dirPath = path.dirname(this.equipmentPath);
    if (!pathExists(dirPath, "dir")) {
      await fspromise.mkdir(dirPath, { recursive: true });
    }
    await fspromise.writeFile(this.equipmentPath, JSON.stringify(dataList));
  }
}

GraphDefineService.toString = () => "[class GraphDefineService]";

export { GraphDefineService };
