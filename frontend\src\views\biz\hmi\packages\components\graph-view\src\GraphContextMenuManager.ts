import { Graph } from "@antv/x6";
import { ContextMenuItem } from "../../../graph/Graph";
import GrapgGroup from "../../../graph/GraphGroup";
import { useI18n } from "vue-i18n";

class GraphContextMenuManager {
  createContextMenu(graph: Graph): ContextMenuItem[] {
    if (!graph) {
      return [];
    }
    return [];
  }
  trigger(item: ContextMenuItem, graph: Graph, graphGroup: GrapgGroup) {
    const { t } = useI18n();
    console.log(t("graph.messages.equipmentLoaded"), item, graph, graphGroup);
  }
}

export default GraphContextMenuManager;
