export default {
  limit: {
    button: {
      title: "Button Management",
      buttonList: "Button List",
      addButton: "Add Button",
      batchAdd: "Batch Add",
      deleteSelected: "Delete Selected",
      confirm: "Confirm",
      name: "Button Name",
      code: "Button Code",
      sort: "Sort",
      description: "Description",
      operation: "Operation",
      form: {
        add: "Add Button",
        edit: "Edit Button",
        view: "View Button",
        title: "Button Name",
        code: "Button Code",
        sort: "Sort",
        description: "Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          title: "Please enter button name",
          code: "Please enter button code",
          sort: "Please enter sort order"
        }
      },
      batch: {
        title: "Batch Add Buttons",
        module: "Belonging Module",
        buttons: "Button List",
        add: "Add",
        delete: "Delete",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          module: "Please select belonging module",
          buttons: "Please add buttons"
        }
      }
    }
  }
};
