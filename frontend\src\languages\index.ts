import { createI18n } from "vue-i18n";
import { LANGUAGE_CONSTANTS } from "./constants";
import zh from "./zh";
import en from "./en";
import es from "./es";
import fr from "./fr";

// 定义消息对象，使用更灵活的类型
const messages: Record<string, any> = {
  zh,
  en,
  es,
  fr
};

// 获取存储的语言设置
const getStoredLanguage = () => {
  const storedLang = localStorage.getItem("language");
  return storedLang && Object.keys(messages).includes(storedLang) ? storedLang : "zh";
};

// 初始化语言设置
const initLanguage = () => {
  const lang = getStoredLanguage();
  localStorage.setItem("language", lang);
  return lang;
};

const i18n = createI18n({
  legacy: false,
  locale: initLanguage(),
  fallbackLocale: "zh",
  messages,
  sync: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true
});

// 导出常量以便在项目中使用
export { LANGUAGE_CONSTANTS };

// 导出i18n实例
export default i18n;
