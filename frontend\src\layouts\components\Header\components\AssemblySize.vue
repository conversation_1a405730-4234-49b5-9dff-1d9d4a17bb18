<template>
  <el-tooltip :content="t('layout.header.assemblySize.title')" placement="bottom" effect="light">
    <el-dropdown trigger="click" @command="setAssemblySize">
      <i :class="'iconfont icon-contentright'" class="toolBar-icon"></i>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in assemblySizeList" :key="item.value" :command="item.value" :disabled="assemblySize === item.value">
            {{ t(item.label) }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-tooltip>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useGlobalStore } from "@/stores/modules/global";
import { AssemblySizeType } from "@/stores/interface";
import { useI18n } from "vue-i18n";

const globalStore = useGlobalStore();
const { t } = useI18n();
const assemblySize = computed(() => globalStore.assemblySize);

const assemblySizeList = [
  { label: "header.assemblySize.default", value: "default" },
  { label: "header.assemblySize.large", value: "large" },
  { label: "header.assemblySize.small", value: "small" }
];

const setAssemblySize = (item: AssemblySizeType) => {
  if (assemblySize.value === item) return;
  globalStore.setGlobalState("assemblySize", item);
};
</script>
