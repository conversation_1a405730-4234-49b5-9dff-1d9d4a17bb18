<template>
  <div class="item-main">
    <el-tooltip :content="t('layout.header.theme.title')" placement="right" effect="dark">
      <el-dropdown trigger="click" @visible-change="handleVisibleChange" placement="right-start" :popper-options="{ strategy: 'fixed' }">
        <div class="theme-trigger">
          <i class="iconfont toolBar-icon" style="display: flex; flex-direction: column">
            <svg-icon icon="eva:color-palette-fill"></svg-icon>
            <span v-if="globalStore.checkColumnLayout()" style="margin-top: 6px; font-size: 12px">{{ t("layout.header.theme.title") }}</span>
          </i>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="theme-dropdown">
            <el-dropdown-item @click="openQuickTheme" class="theme-item" ref="quickThemeBtn">
              <svg-icon icon="eva:color-palette-outline" class="menu-icon" />
              <span class="theme-text">{{ t("layout.theme.quickTheme.title") }}</span>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </el-dropdown-item>
            <el-dropdown-item @click="openLayoutSettings" class="theme-item" ref="layoutSettingsBtn">
              <svg-icon icon="eva:layout-fill" class="menu-icon" />
              <span class="theme-text">{{ t("layout.theme.layoutSettings.title") }}</span>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
  </div>

  <!-- 快捷主题设置弹窗 -->
  <el-popover
    v-model:visible="quickThemeVisible"
    :width="320"
    trigger="click"
    placement="right"
    :offset="8"
    popper-class="quick-theme-popover"
    :popper-options="{
      strategy: 'fixed',
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            boundary: 'viewport',
            padding: 10
          }
        },
        {
          name: 'flip',
          options: {
            fallbackPlacements: ['left', 'right-start', 'left-start']
          }
        }
      ]
    }"
  >
    <template #reference>
      <div ref="popoverTrigger" style="position: absolute; bottom: 50px; left: 60px; width: 1px; height: 1px; pointer-events: none; opacity: 0"></div>
    </template>
    <div class="theme-quick-switch">
      <div class="popover-header">
        <h4>{{ t("layout.theme.quickTheme.title") }}</h4>
        <el-button link @click="closeQuickTheme">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <div class="theme-section">
        <h4>{{ t("layout.theme.global.dark") }}</h4>
        <div class="theme-options">
          <div :class="['theme-option', { active: !globalStore.isDark }]" @click="switchTheme('light')">
            <div class="theme-preview light-theme">
              <div class="preview-header"></div>
              <div class="preview-sidebar"></div>
              <div class="preview-content"></div>
            </div>
            <span>{{ t("layout.theme.mode.light") }}</span>
          </div>
          <div :class="['theme-option', { active: globalStore.isDark }]" @click="switchTheme('dark')">
            <div class="theme-preview dark-theme">
              <div class="preview-header"></div>
              <div class="preview-sidebar"></div>
              <div class="preview-content"></div>
            </div>
            <span>{{ t("layout.theme.mode.dark") }}</span>
          </div>
        </div>
      </div>

      <el-divider />

      <div class="theme-section">
        <h4>{{ t("layout.theme.presetThemes.title") }}</h4>
        <div class="preset-themes">
          <div v-for="theme in presetThemes" :key="theme.name" class="preset-theme" @click="applyPresetTheme(theme)">
            <div class="preset-preview" :style="{ backgroundColor: theme.config.primary }"></div>
            <div class="preset-info">
              <span class="preset-name">{{ theme.name }}</span>
              <span class="preset-description">{{ theme.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <el-divider />

      <div class="theme-section">
        <h4>{{ t("layout.theme.global.special") }}</h4>
        <div class="special-options">
          <div class="special-option">
            <span>{{ t("layout.theme.global.grey") }}</span>
            <el-switch v-model="globalStore.isGrey" @change="changeSpecialMode('grey', !!$event)" />
          </div>
          <div class="special-option">
            <span>{{ t("layout.theme.global.weak") }}</span>
            <el-switch v-model="globalStore.isWeak" @change="changeSpecialMode('weak', !!$event)" />
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, computed } from "vue";
import { useTheme } from "@/hooks/useTheme";
import { useGlobalStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import { ArrowRight, Close } from "@element-plus/icons-vue";
import mittBus from "@/utils/mittBus";

const { t } = useI18n();
const globalStore = useGlobalStore();
const { changePrimary, changeGreyOrWeak, switchDark } = useTheme();
const quickThemeBtn = ref();
const layoutSettingsBtn = ref();
const popoverTrigger = ref();
const quickThemeVisible = ref(false);

defineProps({
  id: String,
  modelValue: [String, Number, Boolean, Object, Array],
  onUpdateModelValue: Function
});

// 预设主题配置（响应式）
const presetThemes = computed(() => [
  {
    name: t("layout.theme.presetThemes.default.name"),
    description: t("layout.theme.presetThemes.default.description"),
    config: { primary: "#124198", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.dark.name"),
    description: t("layout.theme.presetThemes.dark.description"),
    config: { primary: "#124198", isDark: true, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.techBlue.name"),
    description: t("layout.theme.presetThemes.techBlue.description"),
    config: { primary: "#1890ff", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.deepBlue.name"),
    description: t("layout.theme.presetThemes.deepBlue.description"),
    config: { primary: "#0050b3", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.nature.name"),
    description: t("layout.theme.presetThemes.nature.description"),
    config: { primary: "#52c41a", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.forestGreen.name"),
    description: t("layout.theme.presetThemes.forestGreen.description"),
    config: { primary: "#389e0d", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.warm.name"),
    description: t("layout.theme.presetThemes.warm.description"),
    config: { primary: "#fa8c16", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.sunsetOrange.name"),
    description: t("layout.theme.presetThemes.sunsetOrange.description"),
    config: { primary: "#ff7a45", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.elegant.name"),
    description: t("layout.theme.presetThemes.elegant.description"),
    config: { primary: "#722ed1", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.lavender.name"),
    description: t("layout.theme.presetThemes.lavender.description"),
    config: { primary: "#9254de", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.sakura.name"),
    description: t("layout.theme.presetThemes.sakura.description"),
    config: { primary: "#eb2f96", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.rose.name"),
    description: t("layout.theme.presetThemes.rose.description"),
    config: { primary: "#f5222d", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.lime.name"),
    description: t("layout.theme.presetThemes.lime.description"),
    config: { primary: "#a0d911", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.skyBlue.name"),
    description: t("layout.theme.presetThemes.skyBlue.description"),
    config: { primary: "#13c2c2", isDark: false, isGrey: false, isWeak: false }
  },
  {
    name: t("layout.theme.presetThemes.eyeCare.name"),
    description: t("layout.theme.presetThemes.eyeCare.description"),
    config: { primary: "#124198", isDark: false, isGrey: true, isWeak: false }
  }
]);

// 点击外部关闭弹窗
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const popover = document.querySelector(".quick-theme-popover");
  const dropdown = document.querySelector(".theme-dropdown");

  if (quickThemeVisible.value && popover && !popover.contains(target) && dropdown && !dropdown.contains(target)) {
    closeQuickTheme();
  }
};

// 处理下拉菜单可见性变化
const handleVisibleChange = (visible: boolean) => {
  console.log("Theme dropdown visible changed:", visible);
  // 当下拉菜单关闭时，确保弹窗也关闭
  if (!visible) {
    quickThemeVisible.value = false;
  }
};

// 打开快捷主题设置
const openQuickTheme = async () => {
  // 等待DOM更新后再显示弹窗
  await nextTick();

  // 延迟一点时间确保dropdown完全显示
  setTimeout(() => {
    // 定位触发元素到dropdown位置
    if (popoverTrigger.value) {
      // 获取dropdown元素的位置
      const dropdownEl = document.querySelector(".theme-dropdown") as HTMLElement;

      if (dropdownEl) {
        const rect = dropdownEl.getBoundingClientRect();
        const trigger = popoverTrigger.value;
        const popoverWidth = 320; // 弹窗宽度
        const offset = 8; // 间距

        trigger.style.position = "fixed";
        trigger.style.top = `${rect.top}px`;
        trigger.style.height = `${rect.height}px`;
        trigger.style.zIndex = "3000";

        // 检查右侧空间是否足够
        const viewportWidth = window.innerWidth;
        const rightSpace = viewportWidth - rect.right;

        if (rightSpace >= popoverWidth + offset) {
          // 右侧空间足够，显示在右边
          trigger.style.left = `${rect.right + offset}px`;
          trigger.style.width = "1px";
        } else {
          // 右侧空间不足，显示在左边
          trigger.style.left = `${rect.left - popoverWidth - offset}px`;
          trigger.style.width = "1px";
        }

        // 显示弹窗
        quickThemeVisible.value = true;
      }
    }
  }, 100); // 增加延迟时间确保dropdown完全显示
};

// 关闭快捷主题设置
const closeQuickTheme = () => {
  quickThemeVisible.value = false;
};

// 打开布局设置
const openLayoutSettings = () => {
  mittBus.emit("openThemeDrawer", { triggerEl: layoutSettingsBtn.value });
};

// 切换主题模式
const switchTheme = (mode: "light" | "dark") => {
  globalStore.setGlobalState("isDark", mode === "dark");
  // 触发主题切换
  switchDark();
};

// 应用预设主题
const applyPresetTheme = (theme: (typeof presetThemes.value)[0]) => {
  // 应用主题颜色
  changePrimary(theme.config.primary);

  // 应用深色模式
  if (theme.config.isDark !== globalStore.isDark) {
    globalStore.setGlobalState("isDark", theme.config.isDark);
    switchDark();
  }

  // 应用灰色模式
  if (theme.config.isGrey !== globalStore.isGrey) {
    globalStore.setGlobalState("isGrey", theme.config.isGrey);
    changeGreyOrWeak("grey", theme.config.isGrey);
  }

  // 应用弱色模式
  if (theme.config.isWeak !== globalStore.isWeak) {
    globalStore.setGlobalState("isWeak", theme.config.isWeak);
    changeGreyOrWeak("weak", theme.config.isWeak);
  }
};

// 切换特殊模式
const changeSpecialMode = (type: "grey" | "weak", value: boolean) => {
  changeGreyOrWeak(type, value);
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<style scoped lang="scss">
.item-main {
  position: relative;
  z-index: 3002;
  display: flex;
  flex-flow: column wrap;
  place-content: space-around center;
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
}
.theme-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
  .toolBar-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    .svg-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
:deep(.theme-dropdown) {
  min-width: 120px;
  padding: 5px 0;
  margin-left: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  .language-item {
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.3s ease;
    &:hover {
      background-color: var(--bl-hover-bg-color);
    }
    .language-text {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
:deep(.el-popper) {
  z-index: 3002 !important;
}
:deep(.el-dropdown-menu__item) {
  &:hover {
    background-color: var(--bl-hover-bg-color);
  }
  &.is-active {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}
.theme-quick-switch {
  padding: 8px 0;
  .popover-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    margin-bottom: 16px;
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  .theme-section {
    margin-bottom: 16px;
    h4 {
      margin: 0 0 12px;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .theme-options {
      display: flex;
      gap: 12px;
      .theme-option {
        flex: 1;
        padding: 8px;
        text-align: center;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.2s;
        &:hover {
          background-color: var(--bl-hover-bg-color);
        }
        &.active {
          background-color: var(--el-color-primary-light-9);
          border: 1px solid var(--el-color-primary);
        }
        .theme-preview {
          position: relative;
          width: 100%;
          height: 40px;
          margin-bottom: 6px;
          overflow: hidden;
          border-radius: 4px;
          .preview-header {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            height: 8px;
            background-color: var(--el-color-primary);
          }
          .preview-sidebar {
            position: absolute;
            top: 8px;
            left: 0;
            width: 25%;
            height: 32px;
            background-color: var(--el-menu-bg-color);
            border-right: 1px solid var(--el-border-color-light);
          }
          .preview-content {
            position: absolute;
            top: 8px;
            right: 0;
            left: 25%;
            height: 32px;
            background-color: var(--el-bg-color);
          }
        }
        span {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }

    .preset-themes {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
      .preset-theme {
        display: flex;
        align-items: center;
        padding: 8px;
        cursor: pointer;
        background-color: var(--el-bg-color);
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        transition: all 0.2s;
        &:hover {
          background-color: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
          transform: translateY(-1px);
        }
        .preset-preview {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          border: 1px solid var(--el-border-color-light);
          border-radius: 4px;
        }
        .preset-info {
          display: flex;
          flex: 1;
          flex-direction: column;
          .preset-name {
            font-size: 12px;
            font-weight: 500;
            line-height: 1.2;
            color: var(--el-text-color-primary);
          }
          .preset-description {
            margin-top: 2px;
            font-size: 10px;
            line-height: 1.2;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
    .special-options {
      .special-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        span {
          font-size: 13px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

// 主题预览样式
.light-theme {
  .preview-header {
    background-color: #124198 !important;
  }
  .preview-sidebar {
    background-color: #ffffff !important;
    border-right-color: #e4e7ed !important;
  }
  .preview-content {
    background-color: #ffffff !important;
  }
}
.dark-theme {
  .preview-header {
    background-color: #124198 !important;
  }
  .preview-sidebar {
    background-color: #141414 !important;
    border-right-color: #414243 !important;
  }
  .preview-content {
    background-color: #141414 !important;
  }
}

// 快捷主题弹窗特定样式
:deep(.quick-theme-popover) {
  z-index: 3004 !important;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
  .el-popover__title {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

// 确保弹窗内容正确显示
:deep(.quick-theme-popover .el-popover__body) {
  padding: 0;
}
</style>
