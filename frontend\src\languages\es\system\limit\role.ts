export default {
  limit: {
    role: {
      title: "Gestión de Roles",
      form: {
        add: "Agregar Rol",
        edit: "Editar Rol",
        view: "Ver Rol",
        name: "Nombre del Rol",
        code: "Código del Rol",
        category: "Categoría del Rol",
        org: "Organización",
        status: "Estado",
        sort: "Índice",
        description: "Descripción",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          name: "Por favor ingrese el nombre del rol",
          code: "Por favor ingrese el código del rol",
          category: "Por favor seleccione la categoría del rol",
          org: "Por favor seleccione la organización",
          status: "Por favor seleccione el estado"
        }
      },
      columns: {
        name: "Nombre del Rol",
        code: "Código del Rol",
        category: "Categoría del Rol",
        org: "Organización",
        status: "Estado",
        sort: "Índice",
        operation: "Operación"
      },
      category: {
        system: "Rol del Sistema",
        org: "Rol de la Organización"
      },
      status: {
        enable: "Habilitado",
        disable: "Deshabilitado"
      },
      grantResource: {
        title: "Recurso de Autorización",
        warning: "Por favor seleccione el recurso a autorizar",
        firstLevel: "Menú de Primer Nivel",
        menu: "Menú",
        buttonAuth: "Permiso de Botón",
        cancel: "Cancelar",
        confirm: "Confirmar",
        selectDataScope: "Seleccionar Ámbito de Datos",
        api: "API",
        dataScope: "Ámbito de Datos"
      },
      grantPermission: {
        title: "Permiso de Autorización",
        warning: "Por favor seleccione el permiso a autorizar",
        api: "API",
        apiPlaceholder: "Por favor ingrese el nombre de la API",
        dataScope: "Ámbito de Datos",
        cancel: "Cancelar",
        confirm: "Confirmar"
      },
      dataScope: {
        selectOrg: "Seleccionar Organización",
        orgList: "Lista de Organizaciones",
        cancel: "Cancelar",
        confirm: "Confirmar"
      }
    }
  }
};
