import { moduleRequest } from "@/api/request";
import { ReqId, Spa, Menu } from "@/api/interface";
const http = moduleRequest("/sys/limit/menu/");

const menuApi = {
  /** 获取菜单树 */
  tree(params: Menu.Tree) {
    return http.get<Menu.MenuInfo[]>("tree", params);
  },
  /** 获取菜单树选择器 */
  menuTreeSelector(params: Menu.MenuTreeSelectorReq) {
    return http.get<Menu.MenuInfo[]>("menuTreeSelector", params);
  },
  /** 获取菜单详情 */
  detail(params: ReqId) {
    return http.get<Spa.SpaInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除菜单 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 修改模块菜单 */
  changeModule(params: { id: number | string; module: number | string }) {
    return http.post("changeModule", params);
  }
};

export { menuApi };
