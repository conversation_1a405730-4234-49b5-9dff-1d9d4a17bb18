<template>
  <svg
    t="1743043648873"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="13670"
    :width="props.width"
    :height="props.height"
  >
    <path
      d="M896 256a21.333333 21.333333 0 0 1 21.333333 21.333333v42.666667a21.333333 21.333333 0 0 1-21.333333 21.333333h-85.333333v469.333334c0 47.530667-40.96 83.114667-89.322667 85.226666L716.8 896H307.2c-48.768 0-91.221333-34.133333-93.738667-80.917333L213.333333 810.666667V341.333333H128a21.333333 21.333333 0 0 1-21.333333-21.333333v-42.666667a21.333333 21.333333 0 0 1 21.333333-21.333333h768z m-170.666667 85.333333H298.666667v466.965334l0.853333 0.597333a14.592 14.592 0 0 0 5.418667 1.642667L307.2 810.666667h409.6a15.893333 15.893333 0 0 0 7.68-1.770667l0.853333-0.618667V341.333333z m-277.333333 106.666667a21.333333 21.333333 0 0 1 21.333333 21.333333v213.333334a21.333333 21.333333 0 0 1-21.333333 21.333333h-42.666667a21.333333 21.333333 0 0 1-21.333333-21.333333V469.333333a21.333333 21.333333 0 0 1 21.333333-21.333333h42.666667z m170.666667 0a21.333333 21.333333 0 0 1 21.333333 21.333333v213.333334a21.333333 21.333333 0 0 1-21.333333 21.333333h-42.666667a21.333333 21.333333 0 0 1-21.333333-21.333333V469.333333a21.333333 21.333333 0 0 1 21.333333-21.333333h42.666667z m0-320a21.333333 21.333333 0 0 1 21.333333 21.333333v42.666667a21.333333 21.333333 0 0 1-21.333333 21.333333H405.333333a21.333333 21.333333 0 0 1-21.333333-21.333333V149.333333a21.333333 21.333333 0 0 1 21.333333-21.333333h213.333334z"
      :fill="props.color"
      p-id="13671"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";
const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
