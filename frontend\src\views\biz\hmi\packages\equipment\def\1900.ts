import { calculateArc } from "../../graph/GraphUtil";

const e = {
  shape: "1900",
  markup: [
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(0, 5.17, 16, 16, 270, 180)
      }
    },
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(0, 5.17, 16, 16, 170, 180)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8,
        y1: 12.84,
        x2: 0,
        y2: 12.84
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8,
        y1: 12.84,
        x2: 8,
        y2: 0
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8,
        y1: 27.5,
        x2: 8,
        y2: 21
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    arc: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
