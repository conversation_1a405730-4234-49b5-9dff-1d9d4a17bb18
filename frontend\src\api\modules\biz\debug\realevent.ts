import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/realevent/");

const realEventApi = {
  // 指定 deviceId 的订阅
  subRealEventByDevice(deviceId: string, types: string[]) {
    return ipc.iecInvokeWithDevice<{}>("subRealEvent", types, deviceId);
  },
  // 取消订阅（指定 deviceId）
  unSubRealEventByDevice(deviceId: string, param: { all?: string; type?: string[] }) {
    return ipc.iecInvokeWithDevice<{}>("unSubRealEvent", param, deviceId);
  }
};

export { realEventApi };
