import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "1300",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8,
        y1: 40,
        x2: 8,
        y2: 28.83
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 16.67, 17)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 12, 16.67, 17)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
