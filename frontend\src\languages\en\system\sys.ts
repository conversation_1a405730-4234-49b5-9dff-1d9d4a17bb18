export default {
  config: {
    title: "System Configuration",
    paramTitle: "Parameter Configuration",
    systemName: "System Name",
    systemVersion: "System Version",
    waveToolPath: "Third-party Waveform Tool Path",
    waveToolPathPlaceholder: "Please select third-party waveform tool path",
    openDirectory: "Open File Directory",
    save: "Save",
    reset: "Reset",
    saveSuccess: "Save successful",
    selectWaveTool: "Select Waveform Analysis Tool",
    // 参数配置
    paramRefreshTime: "Setting Refresh Interval (ms)",
    reportRefreshTime: "Report Refresh Interval (ms)",
    stateRefreshTime: "Status Refresh Interval (ms)",
    variRefreshTime: "Variable Refresh Interval (ms)",
    // 其他配置
    configTitle: "Configuration",
    configKey: "Config Key",
    configValue: "Config Value",
    remark: "Remark",
    sortCode: "Sort",
    operation: "Operation",
    deleteConfirm: "Delete configuration [{key}]"
  }
};
