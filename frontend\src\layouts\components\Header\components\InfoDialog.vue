<template>
  <el-dialog v-model="dialogVisible" :title="t('layout.header.userInfo.title')" width="500px" draggable>
    <span>This is userInfo</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t("layout.header.userInfo.cancel") }}</el-button>
        <el-button type="primary" @click="dialogVisible = false">{{ t("layout.header.userInfo.confirm") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>
