import { Graph, View, Node, Cell, Rectangle } from "@antv/x6";
import GraphGroup from "./GraphGroup";
import GraphOperator from "./GraphOperator";
import i18n from "@/languages";

const t: any = i18n.global.t;

export const GraphConstants = {
  COMMON_PLUGIN_NAME: "common",
  DATA_PLUGIN_NAME: "data"
};
export enum TriggerEventType {
  UNSELECT = "unselect",
  SELECT = "select"
}
export interface GraphJsonData {
  data: {
    cells: Cell.Properties[];
  };
  graph: {
    background: string;
    grid: {
      show: boolean;
      size: number;
    };
  };
}
export interface CbrDis {
  open: string;
  close: string;
}
export interface GraphViewData {
  /** 画图数据 */
  graphData: GraphJsonData;
  /** 自定义图符 */
  equipmentDatas: EquipmentData[];
  /** 开关刀闸对应装置，默认open=1,close=0 */
  cbrDis: CbrDis;
}

export interface CommonPluginDataInfo {
  graphArea?: Rectangle;
  resizeRefCount?: number;
  /** 标记无效事件计数器 */
  invalidTriggerRef?: Map<TriggerEventType, number>;
}

export interface DataInfo {
  operator: GraphOperator;
  group: GraphGroup;
}

export interface RenderData {
  /** key=短地址，value=短地址关联的Cell */
  yxMap: Map<string, RenderItem[]>;
}

export interface RenderItem {
  /** 如果是组合，则是父元素 */
  cell: Cell;
  equipmentConfig: EquipmentConfig;
  /** 显示配置 */
  show?: RenderItemShow[];
}

export interface RenderItemShow {
  value: string;
  /** 是否是刀闸 */
  isCbrDis?: boolean;
  /** 如果是组合，则是父元素 */
  showValue: string | Cell;
}

/**
 * 设备关联配置
 */
export interface EquipmentConfig {
  /** 遥信/遥测短地址 */
  saddr: string;
  /** 格式化 */
  format: string;
  /** 系数 */
  factor: number;
  /** 遥控短地址 */
  controlSAddr: string;
  controlType: SelectControlType;
  controlValue: SelectControlValue;
  /** 遥设短地址 */
  setSAddr: string;
  setType: SelectControlType;
  /** 显示配置 */
  showCfg: EquipmentShowConfig[];
}
export interface EquipmentShowConfig {
  /** 原始值 */
  value: string;
  type: EquipmentShowType;
  /** 显示值 */
  showValue: string | EquipmentData;
  edit: "type" | "value" | "showValue" | "none";
}
export enum EquipmentShowType {
  TEXT = "text",
  EQUIPMENT = "equipment"
}
export enum ContextMenuItemType {
  GROUP = "group",
  UNGROUP = "ungroup",
  CLONE = "clone",
  DELETE = "delete",
  EQUIPMENT_STATUS = "equipmentStatus",
  EQUIPMENT_SADDR = "equipmentSAddr"
}
export interface ContextMenuItem {
  title: string;
  type: ContextMenuItemType;
  enable: boolean;
  data?: unknown;
}

export interface ContextMenu {
  create: (graph: Graph) => ContextMenuItem[];
  trigger: (item: ContextMenuItem, graph: Graph, graphGroup: GraphGroup) => void;
}
export class GraphResult<T> {
  code: number;
  msg: string;
  data!: T;
  constructor(code?: number) {
    this.code = code == undefined ? 1 : code;
    this.msg = "";
  }
  isSuccess() {
    return this.code === 1;
  }
  setSuccess() {
    this.code = 1;
  }
}
export interface Select<T> {
  label: string;
  value: T;
}
export enum EquipmentStatus {
  OPEN = "open",
  CLOSE = "close",
  NONE = ""
}
export enum EquipmentType {
  CBR = "CBR",
  DIS = "DIS",
  GDIS = "GDIS",
  PTR2 = "PTR2",
  PTR3 = "PTR3",
  VTR = "VTR",
  CTR = "CTR",
  EFN = "EFN",
  IFL = "IFL",
  EnergyConsumer = "EnergyConsumer",
  GND = "GND",
  Arrester = "Arrester",
  Capacitor_P = "Capacitor_P",
  Capacitor_S = "Capacitor_S",
  Reactor_P = "Reactor_P",
  Reactor_S = "Reactor_S",
  Ascoil = "Ascoil",
  Fuse = "Fuse",
  BAT = "BAT",
  BSH = "BSH",
  CAB = "CAB",
  LIN = "LIN",
  GEN = "GEN",
  GIL = "GIL",
  RRC = "RRC",
  TCF = "TCF",
  TCR = "TCR",
  LTC = "LTC",
  IND = "IND"
}
export const EquipmentTypeMap = new Map<EquipmentType, string>([
  [EquipmentType.CBR, t("hmi.graph.equipmentType.CBR")],
  [EquipmentType.DIS, t("hmi.graph.equipmentType.DIS")],
  [EquipmentType.GDIS, t("hmi.graph.equipmentType.GDIS")],
  [EquipmentType.PTR2, t("hmi.graph.equipmentType.PTR2")],
  [EquipmentType.PTR3, t("hmi.graph.equipmentType.PTR3")],
  [EquipmentType.VTR, t("hmi.graph.equipmentType.VTR")],
  [EquipmentType.CTR, t("hmi.graph.equipmentType.CTR")],
  [EquipmentType.EFN, t("hmi.graph.equipmentType.EFN")],
  [EquipmentType.IFL, t("hmi.graph.equipmentType.IFL")],
  [EquipmentType.EnergyConsumer, t("hmi.graph.equipmentType.EnergyConsumer")],
  [EquipmentType.GND, t("hmi.graph.equipmentType.GND")],
  [EquipmentType.Arrester, t("hmi.graph.equipmentType.Arrester")],
  [EquipmentType.Capacitor_P, t("hmi.graph.equipmentType.Capacitor_P")],
  [EquipmentType.Capacitor_S, t("hmi.graph.equipmentType.Capacitor_S")],
  [EquipmentType.Reactor_P, t("hmi.graph.equipmentType.Reactor_P")],
  [EquipmentType.Reactor_S, t("hmi.graph.equipmentType.Reactor_S")],
  [EquipmentType.Ascoil, t("hmi.graph.equipmentType.Ascoil")],
  [EquipmentType.Fuse, t("hmi.graph.equipmentType.Fuse")],
  [EquipmentType.BAT, t("hmi.graph.equipmentType.BAT")],
  [EquipmentType.BSH, t("hmi.graph.equipmentType.BSH")],
  [EquipmentType.CAB, t("hmi.graph.equipmentType.CAB")],
  [EquipmentType.LIN, t("hmi.graph.equipmentType.LIN")],
  [EquipmentType.GEN, t("hmi.graph.equipmentType.GEN")],
  [EquipmentType.GIL, t("hmi.graph.equipmentType.GIL")],
  [EquipmentType.RRC, t("hmi.graph.equipmentType.RRC")],
  [EquipmentType.TCF, t("hmi.graph.equipmentType.TCF")],
  [EquipmentType.TCR, t("hmi.graph.equipmentType.TCR")],
  [EquipmentType.LTC, t("hmi.graph.equipmentType.LTC")],
  [EquipmentType.IND, t("hmi.graph.equipmentType.IND")]
]);

export interface ComponentInfo {
  title: string;
  url: string;
  data: Node.Metadata;
  /** 自定义电气符号(不含默认电气符号) */
  equipmentData?: EquipmentData;
  cellEquipmentData?: CellEquipmentData;
}

const defaultZIndex = 0xffff;
export const EventTypeParamsName = "EventTypeParamsName";
export const EquipmentManagerName = "EquipmentManager";
export interface NodeAttrValue {
  bgColor: string;
  borderColor: string;
  borderWidth: number;
  borderDasharray: string;
  width: number;
  height: number;
  x: number;
  y: number;
  rx: number;
  ry: number;
  angle: number; //旋转角度
  zIndex: number;
  fontValue: FontAttrValue;
  lineHeight: number;
  lineColor: string;
}
export interface FontAttrValue {
  text: string;
  fontSize: number;
  fontFamily: string;
  fontColor: string;
}
export enum Attrs {
  BACKGROUND_COLOR = "background-color",
  BORDER_COLOR = "border-color",
  BORDER_WIDTH = "border-width",
  BORDER_DASHARRAY = "border-dasharray",
  WIDTH = "width",
  HEIGHT = "height",
  X = "x",
  Y = "y",
  RX = "rx",
  RY = "ry",
  ANGLE = "angle",
  ZINDEX = "z-index",
  FONT_FAMILY = "font-family",
  FONT_SIZE = "font-size",
  FONT_COLOR = "font-color",
  TEXT = "text",
  POINTS = "points"
}
export enum NodeAttrs {
  BACKGROUND_COLOR = "fill",
  BORDER_COLOR = "stroke",
  BORDER_WIDTH = "strokeWidth",
  BORDER_DASHARRAY = "stroke-dasharray"
}
export interface GraphData {
  type: string;
  children: string[];
  groupStyle: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  angle: number;
}
export default {
  ContextMenuZIndex: defaultZIndex - 1,
  GroupZIndex: defaultZIndex - 2
};

export enum EventType {
  NONE = "none",
  BLANK_CLICK = "blank:click",
  NODE_CLICK = "node:click"
}
export type EventParams = {
  e: Event;
  x: number;
  y: number;
  node: Node;
  view: View;
  graph: Graph;
};

export type EventTypeParams = {
  type: EventType;
  eventParam: Partial<EventParams>;
};

/**
 * 定义组件项
 */
export interface EquipmentItem {
  data: CellData;
  img: string;
  equipmentStatus: EquipmentStatus;
}

/**
 * 定义组件信息
 */
export interface EquipmentInfo {
  name: string;
  type: string;
  components: EquipmentItem[];
}

export interface EquipmentData extends EquipmentInfo {
  id: string;
}

export interface CellData {
  /** ancestor cell */
  value: Cell;
  /** all children cells */
  descendantCells: Cell[];
}

export interface CellEquipmentData {
  equipmentId: string;
  equipmentIndex: number;
  equipmentType: string;
  equipmentStatus: EquipmentStatus;
  /** 如何加载此图符 */
  loadType?: "create" | "parse";
}

export enum SelectControlType {
  SELECT = "select",
  DIRECT = "direct",
  NONE = "none"
}

export enum SelectControlValue {
  SWITCH_CLOSE = 2,
  SWITCH_OPEN = 1,
  NONE = 0
}

export interface SetSaddrValue {
  saddr: string;
  value: string;
  error: string;
}
