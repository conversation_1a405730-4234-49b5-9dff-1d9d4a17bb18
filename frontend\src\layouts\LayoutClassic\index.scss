.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-header) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
    -webkit-app-region: drag; /* 使整个头部可拖拽 */
    .header-center {
      flex: 1;
    }
    .header-lf {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 300px; // 由 210px 改为 auto，自适应内容宽度
      min-width: 120px; // 可选，防止过窄
      padding: 0;
      background-color: var(--el-header-bg-color);
      -webkit-app-region: no-drag; /* 确保左侧区域不可拖拽，可交互 */
      .logo {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: auto;
        height: 55px;
        margin-right: 3px;
        margin-left: 10px;
        font-size: 20px;
        .logo-img {
          width: 32px;
          object-fit: contain;
        }
        .company-name-en {
          font-family: Tahoma;
          font-size: 18px;
          font-weight: 600;
          color: #124198;
          user-select: none;
        }
        .company-name-cn {
          font-family: "黑体";
          font-size: 16px;
          font-weight: 600;
          color: #124198;
          user-select: none;
        }
        .logo-mark {
          top: 1px;
          right: 1px;
          margin: 0 4px;
          font-family: Tahoma;
          font-size: 14px;
          font-weight: 600;
          color: #124198;
          user-select: none;
        }
      }
    }
    .header-ri {
      display: flex;
      align-items: center;
      justify-content: flex-end; // 由 center 改为 flex-end，靠右显示
      padding-right: 24px; // 适当留白
      -webkit-app-region: no-drag; /* 确保右侧区域不可拖拽，可交互 */
    }
  }
  .classic-content {
    display: flex;
    height: calc(100% - 55px);
    :deep(.el-aside) {
      width: auto;
      background-color: var(--el-menu-bg-color);
      border-right: 1px solid var(--el-aside-border-color);
      .aside-box {
        display: flex;
        flex-direction: column;
        height: 100%;
        transition: width 0.3s ease;
        .el-menu {
          width: 100%;
          overflow-x: hidden;
          border-right: none;
          .el-menu-item {
            .el-menu-tooltip__trigger {
              .el-icon {
                font-size: 23px;
              }
            }
          }
        }
        .other {
          flex-direction: column;
          gap: 8px;
          height: 350px;
          margin-bottom: 0;
          .iconfont {
            color: var(--el-menu-text-color);
          }
        }
      }
    }
    .classic-main {
      display: flex;
      flex-direction: column;
    }
  }
}
