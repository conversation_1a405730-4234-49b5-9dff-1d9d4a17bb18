import { ElMessage } from "element-plus";

export class Message {
  public static info = (message: string, duration: number = 2000): void => {
    ElMessage.info({
      message: message,
      duration: duration
    });
  };

  public static success = (message: string, duration: number = 2000): void => {
    ElMessage.success({
      message: message,
      duration: duration
    });
  };

  public static warning = (message: string, duration: number = 2000): void => {
    ElMessage.warning({
      message: message,
      duration: duration
    });
  };

  public static error = (message: string, duration: number = 2000): void => {
    ElMessage.error({
      message: message,
      duration: duration
    });
  };
}

export default Message;
