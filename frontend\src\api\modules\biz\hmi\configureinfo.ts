import { moduleIpcRequest } from "@/api/request";
import { Configure } from "src/api/interface/biz/hmi";
const ipc = moduleIpcRequest("controller/hmi/configureinfo/");

const configureInfoApi = {
  // 获取组态文件
  getConfigureList() {
    return ipc.invoke<any>("getConfigureList");
  },
  // 添加组态
  addConfigure(param: Configure.ConfigureInfo) {
    return ipc.invoke<any>("addConfigure", param);
  },
  // 重命名组态
  renameConfigure(param: Configure.ConfigureInfo) {
    return ipc.invoke<any>("renameConfigure", param);
  },
  // 删除组态
  removeConfigure(param: Configure.ConfigureInfo) {
    return ipc.invoke<any>("removeConfigure", param);
  },
  // 保存组态
  saveConfigure(param: Configure.SaveConfigureInfo) {
    return ipc.invoke<any>("saveConfigure", param);
  },
  // 加载组态
  loadConfigure(param: Configure.LoadConfigureInfo) {
    return ipc.invoke<any>("loadConfigure", param);
  },
  // 打开组态文件夹
  openConfigureDir(param: Configure.openConfigureInfo) {
    return ipc.invoke<any>("openConfigureDir", param);
  }
};

export { configureInfoApi };
