import { ReqPage, VisLog } from "@/api";

export namespace OpLog {
  /** 操作日志分页查询 */
  export interface Page extends ReqPage {
    /** 操作日志分类 */
    category: string;
  }

  /** 操作日志信息 */
  export interface OpLogInfo extends VisLog.VisLogInfo {
    /** 具体消息 */
    exeMessage: null;
    /** 类名称 */
    className: string;
    /** 方法名称 */
    methodName: string;
    /** 请求方式 */
    reqMethod: string;
    /** 请求地址 */
    reqUrl: string;
    /** 请求参数 */
    paramJson: string | null;
    /** 返回结果 */
    resultJson: string | null;
  }

  /** 折线图 */
  export interface ColumnChart {
    /** 日期 */
    date: string;
    /** 日志类型 */
    name: string;
    /** 数量 */
    count: number;
  }

  /** 饼图 */
  export interface PineChart {
    /** 类型 */
    type: string;
    /** 数量 */
    value: number;
  }
}
