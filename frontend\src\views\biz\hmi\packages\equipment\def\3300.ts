import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "3300",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 7,
        y1: 4.83,
        x2: 0,
        y2: 4.83
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 36,
        y1: 4.5,
        x2: 13,
        y2: 4.67
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(6.49, 0, 21.17, 9.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 12.33,
        y1: 4.67,
        x2: 18.33,
        y2: 1.67
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 12.33,
        y1: 4.67,
        x2: 18.33,
        y2: 7.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
