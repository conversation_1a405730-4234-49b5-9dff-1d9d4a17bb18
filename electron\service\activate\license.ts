"use strict";

const { logger } = require("ee-core/log");

import { ErrorCode, myAuthMgr } from "license";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { t } from "../../data/i18n/i18n";

/**
 * LicenseCheck SERVICE
 * <AUTHOR>
 * @class
 */
class LicenseService {
  private authCache: { result: boolean; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  // 返回机器码
  async getMachineCode(): Promise<string> {
    logger.info(`[LicenseService] ${t("services.license.getMachineCode")}`);

    const startTime = Date.now();
    try {
      var machineCode = await myAuthMgr.getMachineCode();
      const duration = Date.now() - startTime;

      logger.info(`[LicenseService] getMachineCode - 获取机器码成功: ${machineCode}, 耗时: ${duration}ms`);
      return machineCode;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`[LicenseService] getMachineCode - 获取机器码失败，耗时: ${duration}ms`, error);
      throw error;
    }
  }

  // 检查校验码
  async checkAuth(): Promise<boolean> {
    logger.info(`[LicenseService] ${t("services.license.checkAuth")}`);

    // 检查缓存
    if (
      this.authCache &&
      Date.now() - this.authCache.timestamp < this.CACHE_DURATION
    ) {
      logger.info(
        `[LicenseService] checkAuth - ${t("services.license.cacheResult")}`
      );
      return this.authCache.result;
    }

    const startTime = Date.now();
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME; // 软件名称

    try {
      var checkResult = await myAuthMgr.checkAuth(SOFTWARE_NAME);
      const duration = Date.now() - startTime;
      logger.info(
        `[LicenseService] checkAuth - ${t("services.license.verificationTime")}: ${duration}ms`
      );

      logger.info(
        `[LicenseService] checkAuth - 授权验证结果: errCode=${checkResult.errCode}, errInfo=${checkResult.errInfo}`
      );

      if (checkResult.errCode === ErrorCode.SUCCESS) {
        // 缓存成功结果
        this.authCache = { result: true, timestamp: Date.now() };
        logger.info(`[LicenseService] checkAuth - 授权验证成功，结果已缓存`);
        return true;
      } else {
        logger.warn(
          `[LicenseService] checkAuth - 授权验证失败: errCode=${checkResult.errCode}, errInfo=${checkResult.errInfo}`
        );
        // 获取业务错误信息
        throw new Error(String(checkResult?.errInfo));
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(
        `[LicenseService] checkAuth - ${t("services.license.verificationFailed")}: ${duration}ms`,
        error
      );
      throw error;
    }
  }

  // 检查校验码
  async activate(activaeCode: string): Promise<boolean> {
    logger.info(`[LicenseService] ${t("services.license.activate")} - 激活码: ${activaeCode}`);
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME;

    const startTime = Date.now();
    try {
      var checkResult = await myAuthMgr.activate(SOFTWARE_NAME, activaeCode);
      const duration = Date.now() - startTime;

      logger.info(
        `[LicenseService] activate - 激活结果: errCode=${checkResult.errCode}, errInfo=${checkResult.errInfo}, 耗时: ${duration}ms`
      );

      if (checkResult.errCode === ErrorCode.SUCCESS) {
        logger.info(`[LicenseService] activate - 激活成功`);
        return true;
      } else {
        logger.warn(
          `[LicenseService] activate - 激活失败: errCode=${checkResult.errCode}, errInfo=${checkResult.errInfo}`
        );
        // 获取业务错误信息
        throw new Error(String(checkResult?.errInfo));
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`[LicenseService] activate - 激活异常，耗时: ${duration}ms`, error);
      throw error;
    }
  }
}

LicenseService.toString = () => "[class LicenseService]";
const licenseService = new LicenseService();

export { LicenseService, licenseService };
