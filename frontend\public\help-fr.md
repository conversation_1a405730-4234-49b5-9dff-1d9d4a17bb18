# Manuel d'Utilisation

## 1. Aperçu des Fonctions

**VisualDebug** est un outil de débogage spécialement conçu pour les dispositifs de plateforme de visualisation Sieyuan (R&D et débogage d'ingénierie et gestion), intégrant des fonctions riches de débogage, configuration, gestion et auxiliaires pour aider les ingénieurs à accomplir efficacement diverses tâches de débogage.

![Interface Générale de l'Outil](./help-fr/工具整体界面.png)

Les fonctions principales incluent :

- Débogage et gestion des dispositifs
- Outils de configuration
- Outils IT
- Paramètres d'outils, configuration de thème, changement de langue, plus de fonctions

Grâce aux fonctions ci-dessus, VisualDebug peut répondre aux besoins de R&D de processus complet et de débogage d'ingénierie, conception de configuration et gestion quotidienne des dispositifs de la plateforme de visualisation Sieyuan.

### 1.1 Débogage et Gestion des Dispositifs

- Configuration des informations de connexion du dispositif
- Visualisation des informations de base du dispositif
- Valeurs de réglage, valeurs analogiques, valeurs d'état, signalisation à distance, mesure à distance, contrôle à distance, ajustement à distance, transmission de sortie, rapports
- Synchronisation temporelle du dispositif, débogage de variables, importation et exportation de valeurs de réglage
- Téléchargement de fichiers, téléchargement de fichiers

![Débogage et Gestion des Dispositifs](./help-fr/装置调试与管理.png)

### 1.2 Outils de Configuration

- Dessin de graphiques de configuration (aperçu, ajouter, éditer, symboles personnalisés)
- Association des informations du dispositif avec les éléments graphiques

![Outils de Configuration](./help-fr/组态工具.png)

### 1.3 Outils IT

- Téléchargement par lots, importation par lots de valeurs de réglage
- Empaquetage de programmes
- Formatage XML/JSON
- Conversion de base, conversion de température
- Chiffrement et déchiffrement de texte

![Outils IT](./help-fr/IT小工具.png)

### 1.4 Configuration d'Ingénierie et Paramètres Système

- Importation et exportation de configuration d'ingénierie
- Configuration système, configuration de paramètres
- Personnalisation de thème, changement multilingue

![Configuration d'Ingénierie et Paramètres Système](./help-fr/工程配置导入和导出.png)

## 2. Modules Principaux

- **Barre de Menu** : La barre de menu gauche par défaut affiche centralement toutes les entrées de fonctions, incluant débogage, configuration, outils, paramètres, thèmes, langues, plus, etc.
- **Débogage de Dispositifs** : Prend en charge la connexion multi-dispositifs, connexion en temps réel pour voir l'état du dispositif, paramètres, variables, synchronisation temporelle du dispositif, téléchargement de fichiers, téléchargement de fichiers, etc.
- **Fonction de Configuration** : Dessin de graphiques de configuration (aperçu, ajouter, éditer, symboles personnalisés), affichage en temps réel des informations d'interface de configuration du dispositif.
- **Console** : Affichage en temps réel des journaux système, informations de débogage, alertes d'erreur, pratique pour la localisation de problèmes.
- **Support Multilingue** : Peut basculer entre chinois simplifié, anglais et autres langues dans les paramètres.
- **Changement de Thème** : Peut basculer librement entre thèmes clairs, sombres et autres pour améliorer l'expérience visuelle.
- **Gestion de Configuration** : Prend en charge l'importation, exportation, sauvegarde et récupération de fichiers de configuration d'ingénierie, pratique pour la migration de projets et la collaboration.

![Modules Généraux de l'Outil](./help-fr/工具整体模块.png)

## 3. Exigences d'Environnement Opérationnel

Pour s'assurer que le logiciel VisualDebug puisse fonctionner de manière stable et efficace, il est recommandé d'utiliser l'environnement matériel et logiciel suivant :

- **Système d'Exploitation** : Windows 10 et supérieur (64 bits recommandé), prend en charge certaines distributions Linux (comme Debian12+).
- **Processeur** : CPU d'architecture x86 mainstream double cœur et supérieur.
- **Mémoire** : 4GB et supérieur recommandé, 8GB et supérieur recommandé pour une meilleure expérience.
- **Espace de Stockage** : Au moins 500MB d'espace disque disponible requis pour la décompression et l'opération.
- **Résolution d'Écran** : 1366×768 et supérieur recommandé, résolution Full HD 1920×1080 recommandée.
- **Environnement Réseau** : Lors du débogage de dispositifs, assurez-vous d'être sur le même LAN que le dispositif cible.

- **Note :**
  - Il est recommandé d'exécuter le logiciel avec des privilèges administrateur pour éviter les problèmes de configuration causés par des permissions insuffisantes.
  - Cet outil est un logiciel vert, aucune installation complexe requise, peut être exécuté directement après décompression, pas besoin de modifier le registre ou les variables d'environnement du système. Toutes les données utilisateur et fichiers de configuration sont sauvegardés dans le répertoire du logiciel, pratique pour la sauvegarde et la migration.

## 4. Activation du Logiciel

Le logiciel VisualDebug adopte un mécanisme d'autorisation d'activation local, et l'opération d'activation doit être complétée lors de la première utilisation. Le processus d'activation est le suivant :

![Activation d'Autorisation](./help-fr/授权激活.png)

1. **Obtenir le Code Machine**

   - Après avoir démarré le logiciel, s'il n'est pas activé, la fenêtre d'activation apparaîtra automatiquement, affichant le code machine unique de cette machine.
   - Vous pouvez également voir le code machine dans la page "Plus" > "À propos" de la barre de menu.

2. **Demander le Code d'Activation**

   - Envoyez le code machine à l'administrateur du logiciel ou au personnel de support technique pour demander le code d'activation.

3. **Entrer le Code d'Activation**

   - Entrez le code d'activation reçu dans la fenêtre d'activation et cliquez sur le bouton "Activer".
   - Après une activation réussie, le logiciel entrera en état d'utilisation normale

4. **Notes**
   - Le code d'activation correspond au code machine un à un et ne peut être utilisé que sur le dispositif demandé. Par défaut, l'activation n'est requise que pour la première fois.
   - Si vous changez d'ordinateur ou réinstallez le système, vous devez redemander le code d'activation.
   - Les informations d'activation sont sauvegardées dans le gestionnaire d'informations d'identification du système d'exploitation - informations d'identification Windows - informations d'identification générales, veuillez ne pas les supprimer arbitrairement.

![Gestionnaire d'Informations d'Identification](./help-fr/凭据管理器.png)

> Si vous rencontrez des échecs d'activation ou des problèmes liés à l'autorisation, veuillez contacter le support technique pour assistance.

## 5. Fonctions de Débogage

### 5.1 Instructions de Configuration du Dispositif

La fonction de configuration du dispositif est utilisée pour gérer et maintenir les informations des dispositifs qui doivent être débogués, facilitant la connexion ultérieure et la gestion des paramètres. Les opérations principales sont les suivantes :

1. **Entrer dans l'Interface de Configuration du Dispositif**

   - Cliquez sur "Déboguer" > "Liste des Dispositifs" dans la barre de menu pour entrer dans la page de gestion des dispositifs.

2. **Ajouter un Nouveau Dispositif**

   - Cliquez sur le bouton "Ajouter un Dispositif", remplissez le nom du dispositif, l'adresse IP, le port (par défaut 58000), si chiffrer et autres informations.
   - Cliquez sur "Développer les Options Avancées" pour configurer le délai de connexion (par défaut 5000 millisecondes), délai de demande global (par défaut 30000 millisecondes), délai de modification de réglage (par défaut 30000 millisecondes)

3. **Éditer/Supprimer le Dispositif**

   - Dans la liste des dispositifs, sélectionnez le dispositif correspondant et cliquez droit sur le bouton "Éditer" dans le menu contextuel pour modifier les informations du dispositif.
   - Cliquez sur le bouton "Supprimer" pour retirer les dispositifs inutiles. L'opération de suppression doit être faite avec précaution.

4. **État du Dispositif et Connexion**

   - La liste des dispositifs affichera l'état en ligne/hors ligne des dispositifs en temps réel.
   - Prend en charge la connexion/déconnexion des dispositifs en un clic, l'état de connexion aura une identification évidente.

5. **Recherche et Filtre de Dispositifs**

   La liste des dispositifs prend en charge la fonction de **filtre de recherche**, pratique pour localiser rapidement les dispositifs cibles :

   - Entrez n'importe quelle partie du nom du dispositif, adresse IP ou numéro de port dans la boîte d'entrée dans le coin supérieur droit de la page de gestion des dispositifs, et la liste filtrera automatiquement les dispositifs correspondants.
   - Prend en charge la recherche floue, pas besoin d'entrer des informations complètes pour localiser.
   - Après avoir effacé la boîte de recherche, restaure l'affichage de tous les dispositifs.

6. **Fonction d'Expansion/Réduction du Dispositif**

   - Il y a des boutons d'expansion/réduction dans la position médiane de la page de liste de configuration du dispositif, prenant en charge **expansion du dispositif** et **réduction du dispositif**, pratique pour que les utilisateurs naviguent rapidement ou réduisent les informations du dispositif.

![Configuration du Dispositif](./help-fr/装置配置.png)

> **Conseil :**
>
> - Les données de configuration du dispositif seront automatiquement sauvegardées dans les fichiers de configuration locaux et automatiquement chargées après redémarrage du logiciel.
> - La configuration du dispositif peut être exportée régulièrement pour prévenir la perte de données.

### 5.2 Gestion des Informations de Groupes

Après s'être connecté avec succès au dispositif, le logiciel affichera automatiquement les **informations de groupes** du dispositif, facilitant aux utilisateurs la gestion de diverses fonctions et données sous le dispositif. L'interface d'informations de groupes est montrée dans la figure :

![Exemple d'Interface d'Informations de Groupes](./help-fr/分组信息界面示例.png)

1. **Affichage des Informations de Groupes**

   - La zone gauche listera automatiquement tous les groupes sous le dispositif actuel (comme valeurs de réglage, valeurs d'état, mesure à distance, signalisation à distance, contrôle à distance, ajustement à distance, transmission de sortie, rapports, etc.), chaque groupe contient des éléments de fonction correspondants ou des points de données.
   - La structure de groupes prend en charge l'imbrication multiniveau, pratique pour la gestion hiérarchique.

2. **Recherche de Groupes**

   - Une **boîte de recherche** est fournie au-dessus de la liste des groupes, où vous pouvez entrer des noms de groupes ou des mots-clés pour localiser rapidement les groupes cibles ou les éléments de fonction.
   - Prend en charge la recherche floue, pas besoin d'entrer des noms complets pour filtrer.

3. **Créer un Menu Personnalisé**
   - Les utilisateurs peuvent créer des menus personnalisés selon les besoins réels en cliquant sur le bouton "Nouveau Menu", ajoutant des groupes ou éléments de fonction couramment utilisés aux menus personnalisés pour améliorer l'efficacité opérationnelle.
   - Lors de l'ajout d'un nouveau menu personnalisé, vous pouvez configurer le nom et la description du menu. Après création réussie, la description du menu est affichée sur le menu de groupes.

![Exemple d'Interface de Création de Menu Personnalisé](./help-fr/创建自定义菜单界面示例.png)

4. **Créer un Rapport Personnalisé**
   - Prend en charge la création de rapports personnalisés sous les menus personnalisés, classifiant et gérant les éléments de fonction liés.
   - Lors de la création d'un nouveau rapport personnalisé, vous pouvez personnaliser le nom du rapport, description, mots-clés, hériter des rapports, et filtrer les données de rapports hérités selon les mots-clés

![Exemple d'Interface de Création de Rapport Personnalisé](./help-fr/创建自定义报告界面示例.png)

5. **Opérations de Menu Personnalisé**
   - Clic droit sur le menu personnalisé pour effectuer des opérations comme "Éditer le Menu", "Supprimer le Menu", etc.

![Exemple d'Interface d'Opération de Menu Personnalisé](./help-fr/自定义菜单操作界面示例.png)

> **Conseil :**
>
> - Les menus personnalisés et informations de groupes seront automatiquement actualisés avec la connexion du dispositif pour s'assurer que la structure de groupes la plus récente est affichée. Par défaut, les informations de groupes créées ne prennent effet que pour ce dispositif.
> - Les menus personnalisés et groupes ne sont sauvegardés que localement et restent effectifs après redémarrage du logiciel.

## 6. Fonctions de Configuration

### 6.1 Objectif

Ce chapitre décrit les instructions d'opération de l'outil de configuration, décrivant les étapes du processus opérationnel pour les utilisateurs, facilitant aux utilisateurs de comprendre et utiliser rapidement l'outil de configuration.

### 6.2 Emplacement

Ouvrir l'outil, barre de navigation gauche - [Configuration]

![Interface d'Emplacement de Fonction de Configuration](./help-fr/组态功能位置界面.png)

### 6.3 Disposition d'Interface

- Supérieur gauche : Afficher liste de dispositifs et liste de débogage commune, les listes de dispositifs des deux côtés réutilisent une donnée.
- Inférieur gauche : Liste de configuration, clic droit prend en charge les opérations comme ajouter, éditer, supprimer, renommer. Clic gauche affiche interface de configuration.
- Côté droit : Afficher interface de configuration

### 6.4 Liste de Dispositifs

La configuration de surveillance des données du dispositif nécessite de connecter des dispositifs, supérieur gauche affiche liste de dispositifs.

#### 6.4.1 Ajouter

Le [+] dans le coin supérieur droit peut ajouter des informations de dispositif.

![Interface d'Ajout de Dispositif](./help-fr/新增装置界面.png)

#### 6.4.2 Opérations

Sélectionner un dispositif dans la liste de dispositifs, clic droit pour sélectionner les opérations correspondantes. Les opérations ici partagent des données avec le débogage, la suppression ajustera également la liste de dispositifs du côté débogage.

![Interface d'Opération de Liste de Dispositifs](./help-fr/装置列表操作界面.png)

### 6.5 Liste de Configuration

L'interface inférieure gauche affiche les informations de configuration sous forme d'arbre, nœud racine surveillance de configuration. Les configurations sont classées par projet, donc vous devez ajouter des projets d'abord, puis ajouter des configurations sous les projets.

#### 6.5.1 Ajouter Projet

Clic droit sur nœud [Surveillance de Dispositifs], sélectionner ajouter projet.
![Interface d'Ajout de Projet](./help-fr/新增项目界面.png)

Entrer nom du projet, cliquer sur [OK].
![Interface d'Ajout de Projet 2](./help-fr/新增项目界面2.png)

Un nouveau nœud de projet apparaît sous le nœud de surveillance de dispositifs.
![Interface d'Ajout de Projet 3](./help-fr/新增项目界面3.png)

#### 6.5.2 Ajouter Configuration

Les configurations appartiennent à différents projets, besoin de sélectionner nœud de projet, clic droit sélectionner ajouter.
![Interface d'Ajout de Configuration 1](./help-fr/新增组态界面1.png)

Entrer nom de configuration, cliquer sur [OK], nœud de nom de configuration apparaît sous nœud de projet.
![Interface d'Ajout de Configuration 2](./help-fr/新增组态界面2.png)

#### 6.5.3 Éditer Configuration

Après ajout de configuration, c'est une interface vierge, besoin d'ajouter des symboles pour devenir interface de configuration. Clic droit sur [Surveillance de Données de Dispositif], sélectionner [Éditer Configuration].
![Interface d'Édition de Configuration 1](./help-fr/编辑组态界面1.png)

Les opérations spécifiques se réfèrent au chapitre [Dessin de Configuration].
![Interface d'Édition de Configuration 2](./help-fr/编辑组态界面2.png)

#### 6.5.4 Supprimer Configuration

Sélectionner nœud de configuration sous projet, clic droit sélectionner [Supprimer].

## 7. Outils IT

Ce chapitre présente 6 outils IT intégrés dans le logiciel, aidant les utilisateurs à accomplir efficacement les conversions de données communes et opérations auxiliaires dans les scénarios de débogage, traitement de données, gestion d'ingénierie.

### 7.1 Téléchargement par Lots

**Introduction de Fonction :**
L'outil de téléchargement par lots intègre trois modules de fonction principaux : configuration de liste de dispositifs, configuration de fichiers de téléchargement, configuration de paramètres de réglage, prend en charge le téléchargement par lots et l'importation de fichiers et paramètres de réglage de multiples dispositifs, améliorant grandement l'efficacité de gestion et d'archivage de fichiers d'ingénierie.

**Structure de Fonction et Instructions d'Opération Principales :**

1. **Configuration de Liste de Dispositifs**

   - Affiche tous les dispositifs opérables, prend en charge la sélection par lots, ajout, suppression, tri et autres opérations.
   - Peut configurer les informations de connexion, port, si chiffrer, si participer au téléchargement de fichiers et importation de réglage et autres paramètres pour chaque dispositif.
   - Prend en charge sélectionner tout/désélectionner tout en un clic, importation et exportation par lots d'informations de dispositifs.

2. **Configuration de Fichiers de Téléchargement**

   - Affiche les répertoires de fichiers des dispositifs sélectionnés, prend en charge la sélection par lots de multiples fichiers, importation, exportation, suppression, tri et autres opérations.
   - Affiche des informations détaillées comme nom de fichier, taille, chemin, dernière heure de modification.
   - Prend en charge la configuration du chemin de sauvegarde local, tous les fichiers téléchargés seront automatiquement sauvegardés dans le répertoire spécifié.
   - Prend en charge l'importation/exportation de liste de fichiers en un clic, pratique pour la gestion par lots de tâches et la récupération.

3. **Configuration de Paramètres de Réglage**
   - Affiche tous les paramètres de réglage des dispositifs, prend en charge le filtrage de groupes, recherche par mots-clés, sélection par lots, importation, exportation et autres opérations.
   - Affiche des informations détaillées comme nom de paramètre, description, valeur minimale, valeur maximale, pas, unité, valeur actuelle.
   - Prend en charge l'exportation par lots de paramètres de réglage, pratique pour l'archivage et la configuration par lots ultérieure.

**Scénarios d'Application Typiques :**

- Archivage par lots de fichiers de configuration et paramètres de réglage de multiples dispositifs après débogage d'ingénierie.
- Exportation unifiée de sauvegarde de paramètres de réglage de multiples dispositifs, pratique pour la gestion de projets et l'analyse de données.
- Récupération rapide ou migration de configurations de dispositifs, améliorant l'efficacité d'ingénierie.

### 7.2 Fonction d'Empaquetage de Programmes

**Introduction de Fonction :**
L'outil d'empaquetage de programmes est utilisé pour compresser par lots multiples fichiers locaux en fichiers zip, pratique pour le téléchargement unifié ultérieur vers des répertoires spécifiés du dispositif, réalisant le déploiement par lots, mise à niveau ou migration. Prend en charge la sélection flexible de fichiers et dossiers, opération pratique et efficace.

![Interface de Fonction d'Empaquetage de Programmes](./help-fr/程序打包界面示例.png)

**Instructions d'Opération Principales :**

- Cliquer sur le bouton "Sélectionner Fichiers", fenêtre de sélection de fichiers apparaît, prend en charge la sélection multiple, ajouter fichiers ou dossiers sélectionnés à la liste d'empaquetage.
- La liste de fichiers peut voir des informations détaillées comme nom de fichier, taille, chemin, dernière heure de modification.
- Prend en charge la suppression de fichier unique, effacer tout, ajuster flexiblement le contenu d'empaquetage.
- Cliquer sur le bouton "Empaqueter", compresser tous les fichiers dans la liste actuelle en un fichier zip, nommé automatiquement.

**Scénarios d'Application Typiques :**

- Déploiement par lots sur site d'ingénierie, mise à niveau de multiples fichiers de programme.
- Empaquetage unifié de multiples fichiers puis téléchargement vers dispositif, améliorant l'efficacité opérationnelle.
- Quand besoin de migration générale, archivage ou sauvegarde de multiples fichiers liés.

### 7.3 Formatage XML

**Introduction de Fonction :**
L'outil de formatage XML peut rapidement embellir les chaînes XML originales en format clair et facile à lire, prend en charge l'indentation personnalisée et l'aperçu de formatage, pratique pour la visualisation et l'édition de données.

![Exemple de Fonction de Formatage XML](./help-fr/XML格式化功能示例.png)

**Instructions d'Opération Principales :**

- Coller ou entrer le contenu XML original dans la boîte d'entrée "XML à formater".
- Peut choisir si réduire l'affichage via l'interrupteur "Réduire Contenu", ajuster "Taille d'Indentation" pour personnaliser les espaces pour chaque niveau d'indentation.
- Après avoir cliqué sur le bouton de formatage, la zone "XML Formaté" en dessous affiche automatiquement le contenu XML embelli.
- Prend en charge la copie en un clic des résultats de formatage, pratique pour l'utilisation ultérieure.

**Scénarios d'Application Typiques :**

- Voir, éditer et embellir les fichiers XML comme configuration de dispositifs, journaux de débogage.
- Traiter les données XML exportées de systèmes tiers, améliorer la lisibilité des données et l'efficacité de résolution de problèmes.

### 7.4 Formatage JSON

**Introduction de Fonction :**
L'outil de formatage JSON peut rapidement embellir les chaînes JSON originales en format clair et facile à lire, prend en charge le tri de dictionnaire, l'indentation personnalisée et l'aperçu de formatage, pratique pour la visualisation, le débogage et l'édition de données.

![Exemple de Fonction de Formatage JSON](./help-fr/JSON格式化功能示例.png)

**Instructions d'Opération Principales :**

- Coller ou entrer le contenu JSON original dans la boîte d'entrée "JSON à formater".
- Peut choisir si trier les noms de clés JSON via l'interrupteur "Tri de Dictionnaire", ajuster "Taille d'Indentation" pour personnaliser les espaces pour chaque niveau d'indentation.
- La zone "JSON Formaté" en dessous affiche automatiquement le contenu JSON embelli.
- Prend en charge la copie en un clic des résultats de formatage, pratique pour l'utilisation ultérieure.

**Scénarios d'Application Typiques :**

- Voir, éditer et embellir les données JSON comme retours d'interface, fichiers de configuration.
- Traiter le contenu JSON exporté de systèmes tiers, améliorer la lisibilité des données et l'efficacité de résolution de problèmes.

### 7.5 Conversion de Base

**Introduction de Fonction :**
L'outil de conversion de base prend en charge la conversion rapide de valeurs entre différentes bases (comme binaire, octal, décimal, hexadécimal, Base64 et bases personnalisées), adapté à divers scénarios comme débogage de protocoles, analyse de données.

![Exemple de Fonction de Conversion de Base](./help-fr/进制转换功能示例.png)

**Instructions d'Opération Principales :**

- Entrer des valeurs de n'importe quelle base (comme décimal, hexadécimal, etc.) dans la boîte d'entrée "Nombre à convertir".
- L'outil convertira automatiquement les valeurs entrées en temps réel vers des bases communes comme binaire, octal, décimal, hexadécimal, Base64, et affichera les résultats dans les champs correspondants.
- Prend en charge la conversion de base personnalisée, peut configurer la base cible via la boîte d'entrée de base personnalisée en dessous, afficher automatiquement les résultats de conversion.
- Tous les résultats peuvent être directement copiés, pratique pour l'utilisation ultérieure.

**Scénarios d'Application Typiques :**

- Débogage de protocoles, analyse d'adresses de registres, conversion de base de contenu de messages.
- Conversion multiple de base et vérification de paramètres de dispositifs, données d'ingénierie.

### 7.6 Conversion de Température

**Introduction de Fonction :**
L'outil de conversion de température prend en charge la conversion rapide entre multiples échelles de température communes et professionnelles (comme Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur, Rømer), répondant aux besoins d'ingénierie, recherche scientifique et autres scénarios.

![Exemple de Fonction de Conversion de Température](./help-fr/温度转换功能示例.png)

**Instructions d'Opération Principales :**

- Entrer des valeurs de température dans n'importe quelle boîte d'entrée d'échelle de température, l'outil calculera automatiquement et affichera synchroniquement les résultats de conversion de toutes les autres échelles de température.
- Prend en charge la conversion entre échelles de température communes (K, ℃, ℉, °R) et échelles de température professionnelles (°De, °N, °Ré, °Rø).
- Tous les résultats peuvent être directement copiés, pratique pour l'utilisation ultérieure.

**Scénarios d'Application Typiques :**

- Débogage et vérification de paramètres de température de dispositifs.
- Conversion d'unités d'échelles de température multiples sur sites d'ingénierie, expériences scientifiques.

### 7.7 Chiffrement et Déchiffrement de Texte

**Introduction de Fonction :**
L'outil de chiffrement et déchiffrement de texte prend en charge multiples algorithmes de chiffrement principaux (comme AES, TripleDES, Rabbit, RC4, etc.), peut rapidement chiffrer et déchiffrer les données texte, adapté à la protection d'informations sensibles, support technique et autres scénarios.

**Algorithmes Pris en Charge :**
Actuellement l'outil prend en charge les algorithmes de chiffrement principaux suivants, les utilisateurs peuvent choisir selon les besoins réels :

- **AES** (Standard de Chiffrement Avancé)
- **TripleDES** (Standard de Chiffrement de Données Triple)
- **Rabbit** (Algorithme de Chiffrement de Flux)
- **RC4** (Algorithme de Chiffrement de Flux)

![Exemple de Fonction de Chiffrement et Déchiffrement de Texte](./help-fr/文本加解密功能示例.png)

**Instructions d'Opération Principales :**

- Chiffrement : Entrer le contenu de texte en clair dans la boîte d'entrée "Texte à chiffrer", remplir la clé, sélectionner l'algorithme de chiffrement, le système génère automatiquement le texte chiffré crypté.
- Déchiffrement : Entrer le contenu de texte chiffré dans la boîte d'entrée "Texte à déchiffrer", remplir la clé, sélectionner l'algorithme de déchiffrement, le système restaure automatiquement le contenu de texte en clair.
- Prend en charge la sélection de multiples algorithmes de chiffrement et déchiffrement, la zone de résultats peut être directement copiée, pratique pour l'utilisation ultérieure.

**Scénarios d'Application Typiques :**

- Stockage et transmission chiffrés de paramètres sensibles, fichiers de configuration.
- Déchiffrement rapide et vérification d'informations chiffrées pendant le support technique, résolution de problèmes.

## 8. Plus de Fonctions

Cliquer sur le bouton "Plus" en bas de la barre de menu gauche pour accéder rapidement aux fonctions auxiliaires suivantes, améliorant la facilité d'utilisation et l'extensibilité du logiciel :

![Description d'Exemple de Plus de Fonctions](./help-fr/更多功能示例说明.png)

### 8.1 Importer Configuration d'Ingénierie

Prend en charge l'importation de fichiers de configuration d'outils exportés dans l'environnement logiciel d'outils actuel. Peut sélectionner le type de configuration (comme tout, partiel), et spécifier le chemin de fichier d'importation via le sélecteur de fichiers. Après importation, les configurations liées seront automatiquement appliquées au projet actuel, pratique pour la migration de projets et la configuration par lots.

**Étapes d'Opération :**

1. Cliquer sur l'option "Importer Configuration d'Ingénierie" sous le menu "Plus"
2. Sélectionner le fichier de configuration à importer dans le sélecteur de fichiers pop-up
3. Sélectionner le type d'importation (toute la configuration ou configuration partielle)
4. Confirmer l'importation, le système appliquera automatiquement la configuration à l'environnement actuel

**Notes :**

- Il est recommandé de sauvegarder la configuration actuelle avant importation pour éviter la perte de données
- Veuillez ne pas fermer le logiciel ou effectuer d'autres opérations pendant l'importation
- Si l'importation échoue, veuillez vérifier le format du fichier et l'intégrité du contenu

![Exemple de Fonction d'Importation de Configuration d'Ingénierie](./help-fr/导入工程配置功能示例.png)

### 8.2 Exporter Configuration d'Ingénierie

Prend en charge l'exportation de toute ou partie de la configuration d'ingénierie actuelle comme fichiers, pratique pour la sauvegarde, l'archivage ou la migration vers d'autres environnements. Peut sélectionner le répertoire d'exportation, les fichiers de configuration générés après exportation peuvent être utilisés pour les opérations d'importation ultérieures.

**Étapes d'Opération :**

1. Cliquer sur l'option "Exporter Configuration d'Ingénierie" sous le menu "Plus"
2. Sélectionner le type d'exportation (toute la configuration ou configuration partielle)
3. Sélectionner le répertoire d'exportation
4. Confirmer l'exportation, le système générera des fichiers de configuration

**Contenu d'Exportation :**

- Tout
- Liste de Dispositifs
- Liste de Configuration

**Scénarios d'Utilisation :**

- Sauvegarde et archivage de projets
- Migration de configuration vers d'autres environnements
- Partage de configuration de collaboration d'équipe
- Résolution de problèmes de support technique

![Exemple de Fonction d'Exportation de Configuration d'Ingénierie](./help-fr/导出工程配置功能示例.png)

### 8.3 Fonction de Capture d'Écran

Capture en un clic de l'interface logicielle actuelle, pratique pour sauvegarder le processus de débogage, retour de problèmes ou support technique. Les fichiers de capture d'écran peuvent être copiés dans le presse-papiers ou sauvegardés dans un répertoire spécifié, prend en charge la visualisation et le partage ultérieurs.

**Caractéristiques de Fonction :**

- Prend en charge la capture d'écran plein écran et la capture de zone
- Prend en charge la copie dans le presse-papiers ou la sauvegarde dans un répertoire spécifié

**Étapes d'Opération :**

1. Cliquer sur l'option "Capture d'Écran" sous le menu "Plus"
2. Sélectionner le mode de capture d'écran (plein écran ou zone)
3. Si la capture de zone est sélectionnée, glisser pour sélectionner la zone de capture d'écran

**Scénarios d'Application :**

- Enregistrement de processus de débogage
- Retour de problèmes et support technique
- Documentation d'étapes d'opération
- Référence de conception d'interface

**Notes :**

- Veuillez vous assurer que l'affichage de l'interface est complet avant la capture d'écran
- Il est recommandé de masquer les informations sensibles lors de la prise de captures d'écran
- Les fichiers de capture d'écran occuperont de l'espace disque, un nettoyage régulier est recommandé

### 8.4 Recherche de Menu

Prend en charge la recherche rapide par nom de menu ou chemin, pratique pour localiser rapidement les entrées de fonction requises quand il y a beaucoup de fonctions. Après avoir saisi des mots-clés, le système filtre automatiquement les éléments de menu correspondants, améliorant l'efficacité opérationnelle.

**Fonctions de Recherche :**

- Prend en charge la recherche floue et la recherche exacte
- Affichage en temps réel des résultats de recherche
- Affichage en surbrillance des résultats de recherche

**Étapes d'Opération :**

1. Cliquer sur l'option "Recherche de Menu" sous le menu "Plus"
2. Entrer des mots-clés dans la boîte de recherche
3. Le système affiche automatiquement les éléments de menu correspondants
4. Cliquer sur les résultats de recherche pour sauter directement à la fonction correspondante

![Exemple de Fonction de Recherche de Menu](./help-fr/菜单搜索功能示例.png)

### 8.5 Documentation d'Aide

Ouvrir cette documentation d'aide, voir les descriptions de fonctions du logiciel, guides d'opération, questions fréquemment posées et autre contenu, pratique pour que les débutants démarrent rapidement et trouvent des solutions d'auto-assistance quand ils rencontrent des problèmes.

**Contenu de Documentation :**

- Introduction et aperçu des fonctions du logiciel
- Instructions détaillées d'étapes d'opération
- Questions Fréquemment Posées (FAQ)
- Guide de résolution de problèmes
- Recommandations de meilleures pratiques

**Méthodes d'Utilisation :**

- Visualisation en ligne : Cliquer sur l'option "Aide" sous le menu "Plus"
- Visualisation hors ligne : La documentation d'aide est intégrée dans le logiciel
- Fonction de recherche : Prend en charge la recherche par mots-clés pour localisation rapide
- Navigation de répertoire : Saut rapide vers des chapitres spécifiés via le répertoire

**Utilisateurs Applicables :**

- Démarrage rapide de nouveaux utilisateurs
- Référence d'utilisation quotidienne
- Résolution et résolution de problèmes
- Apprentissage et recherche approfondis de fonctions

![Exemple de Fonction d'Aide](./help-fr/帮助功能示例.png)

### 8.6 Informations À Propos

Voir les informations de base du logiciel, incluant nom d'outil, numéro de version, code machine, etc. Peut être utilisé pour l'enregistrement du logiciel, support technique et gestion de versions.

**Informations Affichées :**

- Nom du logiciel et numéro de version
- Informations de droits d'auteur et informations de l'entreprise
- Code machine (utilisé pour l'activation du logiciel)
- Temps de construction et identification de version

**Utilisations Principales :**

- Confirmation de version du logiciel
- Demande de code d'activation (nécessite code machine)

**Instructions d'Opération :**

1. Cliquer sur l'option "À Propos" sous le menu "Plus"
2. Voir les informations détaillées du logiciel
3. Copier le code machine pour demande d'activation

**Notes :**

- Le code machine est l'identifiant unique pour l'activation du logiciel
- Veuillez conserver correctement les informations du code machine
- Les informations de version aident à la résolution de problèmes

![Exemple de Fonction À Propos](./help-fr/关于功能示例.png)

> **Conseil :**
>
> - Les fonctions sous le menu "Plus" sont des outils auxiliaires, visant à améliorer la flexibilité du logiciel et l'expérience utilisateur.
> - Les opérations d'importation et exportation sont recommandées à effectuer régulièrement pour prévenir la perte de données.
> - Si des anomalies de fonction ou questions d'opération se produisent, veuillez consulter la documentation d'aide ou contacter le support technique.
> - La sauvegarde régulière de configurations importantes et fichiers de capture d'écran est recommandée.
> - L'utilisation raisonnable de la fonction de recherche peut améliorer significativement l'efficacité opérationnelle.

## 9. Questions Fréquemment Posées (FAQ)

- **Q : L'affichage du menu de groupes est incorrect après connexion du dispositif ?**
  - Vérifier si urpc dans le répertoire shr du dispositif est mis à jour vers la version compatible avec l'outil.
  - Lire debug_info.xml dans le répertoire shr et télécharger localement, contacter le support technique pour localisation de problèmes.
- **Q : Impossible de connecter le dispositif ?**
  - Vérifier l'alimentation du dispositif et la connexion réseau, s'assurer d'être sur le même LAN que l'ordinateur.
  - Vérifier les paramètres de pare-feu, s'assurer que le logiciel a la permission d'accès réseau.
- **Q : Échec d'importation de configuration ?**
  - Veuillez confirmer que le format du fichier de configuration importé est correct et pris en charge par **VisualDebug**.
- **Q : Les fichiers Excel chiffrés ne peuvent pas être importés ?**
  - En raison de l'impact des politiques IT, Excel chiffré ne peut pas être reconnu. Si besoin d'importer des fichiers Excel chiffrés, veuillez déchiffrer d'abord.
- **Q : Affichage anormal de l'interface du logiciel ?**
  - Essayer de changer de thèmes ou redémarrer le logiciel, si le problème persiste veuillez contacter le support technique.
- **Q : Les valeurs de réglage avec différences ne s'affichent pas après importation de réglage ?**
  - Veuillez confirmer si les noms de groupes de réglage dans le fichier Excel/xml/csv de réglage importé sont complètement cohérents avec les noms de groupes sur le menu de groupes de l'outil. L'incohérence ne peut pas comparer les différences.
- **Q : Dans quel répertoire du dispositif les fichiers empaquetés de la fonction d'empaquetage de programmes doivent-ils être téléchargés ?**
  - Le zip empaqueté est en format chiffré, doit être téléchargé dans le répertoire /dwld du dispositif, redémarrage requis après achèvement du téléchargement pour prendre effet.
- **Q : Pourquoi le redémarrage ne prend pas effet quand redémarrage à l'achèvement du téléchargement est coché dans l'interface de téléchargement de fichiers ?**
  ![Échec de Redémarrage](./help-fr/重启失败.png)
  - Le redémarrage a des exigences pour le firmware de la carte CPU. Si ls -l /sbin/reboot montre un lien symbolique comme dans l'image ci-dessus, reboot n'est pas pris en charge, mise à niveau du firmware requise.

## 10. Support Technique

Si vous rencontrez des problèmes qui ne peuvent pas être résolus, veuillez nous contacter par les méthodes suivantes :

- **Entreprise** : Sieyuan Electric Co., Ltd.
- **Département** : Institut de Recherche Central - Département de Développement d'Applications Embarquées
- **Groupe Professionnel** : Groupe de Développement de Logiciels d'Outils

Merci d'utiliser ce logiciel, nous vous souhaitons un débogage réussi !
