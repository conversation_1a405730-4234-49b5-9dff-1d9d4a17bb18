import {
  DebugInfoFileManager,
  DebugMenu,
  DebugReport,
} from "../../utils/customInfoFileManager";
import { Parser } from "xml2js";
import { readFileSync } from "fs";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { debugInfoMenuService } from "./debuginfomenu";

function getDebugInfoPath(deviceId: string) {
  var debugInfoPath = "/shr/debug_info.xml";
  return IECCONSTANTS.PATH_CONFIG + "/" + deviceId + debugInfoPath;
}

export class CustomInfoService {
  private getFileManager(deviceId: string) {
    return new DebugInfoFileManager(deviceId);
  }

  async getAllGroups(deviceId: string) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.getAllGroupsEntry")}: ${deviceId}`
    );
    return this.getFileManager(deviceId).getAll();
  }

  async validateMenuName(deviceId: string, name: string) {
    logger.info(`[CustomInfoService] 验证菜单名称: ${deviceId}, name: ${name}`);
    return this.getFileManager(deviceId).validateMenuName(name);
  }

  async addMenu(deviceId: string, group: DebugMenu) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.addMenuEntry")}: ${deviceId}, ${t("logs.customInfoService.groupName")}: ${group?.name}`
    );
    try {
      return await this.getFileManager(deviceId).addMenu(group);
    } catch (error) {
      logger.error(`[CustomInfoService] 添加菜单失败: ${error}`);
      throw error; // 重新抛出错误，让上层处理
    }
  }

  async editMenu(deviceId: string, uuid: string, newGroup: DebugMenu) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.editMenuEntry")}: ${deviceId}, uuid: ${uuid}, ${t("logs.customInfoService.newGroupKeyword")}: ${newGroup?.keyword}`
    );
    try {
      return await this.getFileManager(deviceId).editMenu(uuid, newGroup);
    } catch (error) {
      logger.error(`[CustomInfoService] 编辑菜单失败: ${error}`);
      throw error; // 重新抛出错误，让上层处理
    }
  }

  async deleteMenu(deviceId: string, uuid: string) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.deleteMenuEntry")}: ${deviceId}, uuid: ${uuid}`
    );
    return this.getFileManager(deviceId).deleteMenu(uuid);
  }

  async addReport(
    deviceId: string,
    groupUuid: string,
    report: DebugReport & { newname?: string }
  ) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.addReportEntry")}: ${deviceId}, groupUuid: ${groupUuid}, ${t("logs.customInfoService.reportName")}: ${report?.name}, ${t("logs.customInfoService.newName")}: ${report?.newname}`
    );
    return this.getFileManager(deviceId).addReport(groupUuid, report);
  }

  async editReport(
    deviceId: string,
    groupUuid: string,
    reportUuid: string,
    newReport: DebugReport & { newname?: string }
  ) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.editReportEntry")}: ${deviceId}, groupUuid: ${groupUuid}, reportUuid: ${reportUuid}, ${t("logs.customInfoService.newReportName")}: ${newReport?.name}, ${t("logs.customInfoService.newName")}: ${newReport?.newname}`
    );
    return this.getFileManager(deviceId).editReport(
      groupUuid,
      reportUuid,
      newReport
    );
  }

  async deleteReport(deviceId: string, groupUuid: string, reportUuid: string) {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.deleteReportEntry")}: ${deviceId}, groupUuid: ${groupUuid}, reportUuid: ${reportUuid}`
    );
    return this.getFileManager(deviceId).deleteReport(groupUuid, reportUuid);
  }

  /** 获取debug_info.xml中所有唯一的 fc 列表 */
  async getFcList(deviceId: string): Promise<string[]> {
    logger.info(`[CustomInfoService] getFcList: ${deviceId}`);
    const debugInfoPath = getDebugInfoPath(deviceId);
    const xmlData = readFileSync(debugInfoPath, { encoding: "utf-8" });
    const result = await new Parser({
      explicitArray: false,
    }).parseStringPromise(xmlData);
    const root = result.DebugInfo || {};
    const set = new Set<string>();
    function collectFc(menu: any) {
      if (!menu) return;
      if (Array.isArray(menu)) {
        menu.forEach(collectFc);
        return;
      }
      if (menu.$ && menu.$.fc) {
        set.add(menu.$.fc);
      }
      if (menu.Menu) collectFc(menu.Menu);
    }
    if (root.Menu) collectFc(root.Menu);
    const list = Array.from(set);
    logger.info(`[CustomInfoService] getFcList done, count: ${list.length}`);
    return list;
  }

  /** 根据 fc 获取对应 TAB 菜单及其点（items） */
  async getMenusByFc(deviceId: string, fc: string) {
    logger.info(`[CustomInfoService] getMenusByFc: ${deviceId}, fc: ${fc}`);
    // 直接复用已解析缓存
    const menus = debugInfoMenuService.getTreeItemByFc([fc], deviceId);
    return menus || [];
  }

  /** 获取debug_info.xml中所有fc=LG的报告，返回name、fc、method */
  async getLGReports(
    deviceId: string
  ): Promise<{ name: string; desc: string; fc: string; method: string }[]> {
    logger.info(
      `[CustomInfoService] ${t("logs.customInfoService.getLGReportsEntry")}: ${deviceId}`
    );
    try {
      const debugInfoPath = getDebugInfoPath(deviceId);
      const xmlData = readFileSync(debugInfoPath, { encoding: "utf-8" });
      const result = await new Parser({
        explicitArray: false,
      }).parseStringPromise(xmlData);
      const root = result.DebugInfo || {};
      let reports: {
        name: string;
        desc: string;
        fc: string;
        method: string;
      }[] = [];
      // 递归查找所有Menu
      function findLGMenus(menu: any) {
        if (!menu) return;
        if (Array.isArray(menu)) {
          menu.forEach(findLGMenus);
          return;
        }
        if (menu.$ && menu.$.fc === "LG") {
          reports.push({
            name: menu.$.name,
            fc: menu.$.fc,
            desc: menu.$.desc,
            method: menu.$.method || "",
          });
        }
        if (menu.Menu) {
          findLGMenus(menu.Menu);
        }
      }
      if (root.Menu) {
        findLGMenus(root.Menu);
      }
      logger.info(
        `[CustomInfoService] ${t("logs.customInfoService.getLGReportsSuccess")}: ${deviceId}, ${t("logs.customInfoService.lgReportsCount")}: ${reports.length}`
      );
      return reports;
    } catch (err) {
      logger.error(
        `[CustomInfoService] ${t("logs.customInfoService.getLGReportsError")}: ${deviceId}, ${t("errors.errorDetail")}: ${err}`
      );
      throw err;
    }
  }
}
