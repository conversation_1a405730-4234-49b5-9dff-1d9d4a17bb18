export default {
  equipmentList: {
    sequence: "Index",
    name: "Nom",
    type: "Type",
    operation: "Opération",
    preview: "Aperçu",
    copy: "Copier",
    delete: "Supprimer",
    confirmDelete: "Êtes-vous sûr de vouloir supprimer ?",
    tip: "Message d'information",
    error: "Erreur"
  },
  graphComponent: {
    deviceType: "Type d'appareil",
    deviceName: "Nom de l'appareil",
    save: "Enregistrer"
  },
  contextMenu: {
    group: "Grouper",
    ungroup: "Dégrouper",
    setStatus: "Définir le statut",
    copy: "Copier",
    delete: "Supprimer",
    rename: "Renommer"
  },
  graphCreate: {
    needTwoDevices: "L'interrupteur ou le sectionneur nécessite la sélection de deux symboles d'appareil",
    needCorrectStatus: "Veuillez définir les propriétés de statut correctes pour l'interrupteur ou le sectionneur",
    needOneDevice: "Veuillez sélectionner un symbole d'appareil"
  },
  graphDefine: {
    waitCanvasInit: "Veuillez attendre l'initialisation du canevas",
    selectOneGraph: "Veuillez sélectionner un symbole",
    tip: "Conseil"
  },
  setStatus: {
    open: "Ouvert",
    close: "Fermé",
    none: "Aucun"
  },
  graphTools: {
    undo: "Annuler",
    redo: "Rétablir",
    front: "Mettre au premier plan",
    back: "Mettre à l'arrière-plan",
    delete: "Supprimer",
    save: "Enregistrer",
    equipmentList: "Liste d'Équipements"
  },
  graphEditor: {
    dataConfig: "Configuration des données",
    loadEquipmentFailed: "Échec du chargement du symbole"
  }
};
