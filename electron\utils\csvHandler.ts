// csv-handler.ts
import { createReadStream, createWriteStream } from "fs";
import { parse, write } from "fast-csv";
import { Column } from "../interface/debug/exportTypes";
import { ImportData, ImportResult, SheetData } from "../interface/debug/sheetDara";

export class CsvHandler {
  static async export(
    data: any[],
    columns: Column[],
    filePath: string,
    grpName?: string
  ): Promise<void> {
    const csvData = data.map((item) => {
      return columns.reduce(
        (acc, col) => {
          acc[col.header] = item[col.key];
          return acc;
        },
        {} as Record<string, any>
      );
    });

    return new Promise((resolve, reject) => {
      const writer = createWriteStream(filePath);
      // 写入表头
      writer.write(`\ufeff${columns.map((c) => c.header).join(",")}\n`);
      // 写入分组名注释行（如有）
      if (grpName) {
        writer.write(`# ${grpName}\n`);
      }
      // 写入数据
      csvData.forEach((row) => {
        const dataRow = columns
          .map((col) => `"${row[col.header] ?? ""}"`)
          .join(",");
        writer.write(`${dataRow}\n`);
      });
      writer.end();
      writer.on("finish", resolve);
      writer.on("error", reject);
    });
  }

  static async exportMuti(
    groups: SheetData[], // 新增分组参数
    columns: Column[],
    filePath: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const writer = createWriteStream(filePath);

      // 统一写入CSV头（仅一次）
      const headerRow = columns.map((c) => c.header).join(",");
      writer.write(`\ufeff${headerRow}\n`); // BOM头解决中文乱码

      let isFirstGroup = true;

      groups.forEach((group) => {
        // 添加分组注释行（从第二组开始换行）
        if (!isFirstGroup) {
          writer.write(`\n# ${group.sheetName}\n`);
        } else {
          writer.write(`# ${group.sheetName}\n`);
          isFirstGroup = false;
        }

        // 生成数据行
        group.data.forEach((item) => {
          const dataRow = columns
            .map((col) => `"${item[col.key] ?? ""}"`) // 处理特殊字符
            .join(",");
          writer.write(`${dataRow}\n`);
        });
      });

      writer.end();
      writer.on("finish", resolve);
      writer.on("error", reject);
    });
  }

  static async import<T>(
    filePath: string,
    columns: Column[],
    grpName?: string
  ): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const results: T[] = [];
      let currentGroup = "default";
      let groupMatched = !grpName; // 如果没传分组名，默认全部匹配

      createReadStream(filePath)
        .pipe(parse({ headers: true }))
        .on("data", (row) => {
          const firstColumn = row[Object.keys(row)[0]];
          // 检查分组行
          if (
            firstColumn &&
            typeof firstColumn === "string" &&
            firstColumn.trim().startsWith("#")
          ) {
            currentGroup = firstColumn.replace("#", "").trim();
            groupMatched = !grpName || currentGroup === grpName;
            return;
          }
          if (groupMatched) {
            const item = columns.reduce((acc, col) => {
              acc[col.key] = row[col.header];
              return acc;
            }, {} as any);
            results.push(item);
          }
        })
        .on("end", () => resolve(results))
        .on("error", reject);
    });
  }

  /**
   * 异步函数，用于处理文件导入并分组数据
   * @param filePath 文件路径
   * @param groupHeaderPrefix 分组前缀
   * @param logger 日志记录器
   * @returns 返回分组后的数据
   */
  static async importMuti(
    filePath: string,
    columns: Column[],
    groupHeaderPrefix: string = "#"
  ): Promise<ImportResult> {
    return new Promise((resolve, reject) => {
      const results: ImportResult = [];
      let currentGroup: ImportData = { grpname: "default", data: [] };

      createReadStream(filePath)
        .pipe(parse({ headers: true })) // 使用 fast-csv 解析 CSV 文件
        .on("data", (row) => {
          //分组检测逻辑
          const firstColumn = row[Object.keys(row)[0]];
          // logger.info("row", firstColumn);
          if (firstColumn.includes(groupHeaderPrefix)) {
            // logger.info(row); // 记录日志
            if (currentGroup.data.length > 0) {
              results.push(currentGroup); // 将当前分组添加到结果中
            }
            currentGroup = {
              grpname: String(firstColumn).replace("#", "").trim(), // 更新分组名称
              data: [], // 初始化新分组的数据
            };
            return;
          }
          const item = columns.reduce((acc, col) => {
            acc[col.key] = row[col.header];
            return acc;
          }, {} as any);
          currentGroup.data.push(item); // 将数据添加到当前分组
        })
        .on("end", () => {
          if (currentGroup.data.length > 0) {
            results.push(currentGroup); // 添加最后一个分组
          }

          // logger.info("results:", results);
          resolve(results); // 返回分组后的数据
        })
        .on("error", (err) => {
          reject(err); // 处理错误
        });
    });
  }
}
