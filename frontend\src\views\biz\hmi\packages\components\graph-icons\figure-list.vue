<template>
  <svg
    t="1742957580763"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="54683"
    :width="props.width"
    :height="props.height"
  >
    <path
      :fill="props.color"
      d="M810.666667 469.333333H213.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V213.333333a85.333333 85.333333 0 0 1 85.333333-85.333333h597.333334a85.333333 85.333333 0 0 1 85.333333 85.333333v170.666667a85.333333 85.333333 0 0 1-85.333333 85.333333zM213.333333 213.333333v170.666667h597.333334V213.333333H213.333333z m341.333334 42.666667h170.666666v85.333333h-170.666666V256zM213.333333 896h597.333334a85.333333 85.333333 0 0 0 85.333333-85.333333v-170.666667a85.333333 85.333333 0 0 0-85.333333-85.333333H213.333333a85.333333 85.333333 0 0 0-85.333333 85.333333v170.666667a85.333333 85.333333 0 0 0 85.333333 85.333333z m0-85.333333v-170.666667h597.333334v170.666667H213.333333z m341.333334-128h170.666666v85.333333h-170.666666v-85.333333z"
      p-id="54684"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";

const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
