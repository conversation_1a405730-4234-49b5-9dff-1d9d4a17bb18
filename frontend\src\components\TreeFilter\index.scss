.filter {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  .title {
    padding: 16px;
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    border-bottom: 1px solid var(--el-border-color-light);
  }
  .search {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    .el-input {
      flex: 1;
    }
  }
  :deep(.el-tree) {
    height: 100%;
    padding: 8px;
    overflow: auto;
    .el-tree-node__content {
      height: 32px;
    }
    .el-tree-node__label {
      font-size: 14px;
    }
  }
}

// 树节点样式
:deep(.el-tree-node) {
  &.is-current > .el-tree-node__content {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
  &.is-checked > .el-tree-node__content {
    background-color: var(--el-color-primary-light-9);
  }
}

// 滚动条样式
:deep(.el-scrollbar__bar) {
  &.is-horizontal {
    display: none;
  }
}
