/**
 * @description 用户选择器接口
 * @license Apache License Version 2.0
 */
import UserSelector from "./index.vue";

/** 用户选择器属性 */
export interface UserSelectProps {
  /** 组织树api */
  orgTreeApi: (data?: any) => Promise<any>;
  /** 职位选择api */
  positionTreeApi?: (data?: any) => Promise<any>;
  /** 角色选择api */
  roleTreeApi?: (data?: any) => Promise<any>;
  /** 用户选择api */
  userSelectorApi: (data?: any) => Promise<any>;
  /** 是否多选 */
  multiple?: boolean;
  /** 最大用户数 */
  maxCount?: number;
  /** 是否是业务 */
  biz?: boolean;
}

/** 用户选择器表格初始化参数 */
export interface UserSelectTableInitParams {
  /** 组织ID */
  orgId?: number | string | null;
  /** 职位ID */
  positionId?: number | string | null;
  /** 角色ID */
  roleId?: number | string | null;
}

/**
 * @description 用户选择器实例类型
 */
export type UserSelectorInstance = Omit<InstanceType<typeof UserSelector>, keyof ComponentPublicInstance | keyof UserSelectProps>;
