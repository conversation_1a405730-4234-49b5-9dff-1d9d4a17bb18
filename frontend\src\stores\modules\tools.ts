import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { ToolInfo } from "@/stores/interface";

const name = "simple-tools"; // 定义模块名称
/**  ToolsState */
export interface ToolConfig {
  /** 系统基本信息 */
  toolInfo: ToolInfo;
}

/** 配置模块 */
export const useToolsStore = defineStore({
  id: name,
  state: (): ToolConfig => {
    return {
      toolInfo: { asideIndex: "1", tabIndex: "1" }
    };
  },
  persist: piniaPersistConfig(name)
});
