import {logger} from 'ee-core/log';
import {getConfig} from 'ee-core/config';
import {getMainWindow} from 'ee-core/electron';
import {app} from 'electron';
import { getExecDir } from "ee-core/ps";

class Lifecycle {
  /**
   * Core app has been loaded
   */
  async ready(): Promise<void> {
    app.setPath("appData", getExecDir());
    logger.info("[lifecycle] ready");
  }

  /**
   * Electron app is ready
   */
  async electronAppReady(): Promise<void> {
    logger.info("[lifecycle] electron-app-ready");
  }

  /**
   * Main window has been loaded
   */
  async windowReady(): Promise<void> {
    logger.info("[lifecycle] window-ready");

    // 优化窗口显示逻辑，等待内容加载完成后再显示，避免黑屏
    const config = getConfig();
    const { windowsOption } = config;
    if (windowsOption?.show === false) {
      const win = getMainWindow();

      // 监听页面加载完成事件
      const showWindow = () => {
        if (!win.isDestroyed() && !win.isVisible()) {
          win.show();
          win.focus();
          logger.info("[lifecycle] 窗口已显示，内容加载完成");
        }
      };

      // 多种事件监听，确保窗口能够显示
      win.webContents.once("did-finish-load", showWindow);
      win.webContents.once("dom-ready", showWindow);
      win.once("ready-to-show", showWindow);

      // 添加超时保护，防止窗口永远不显示
      setTimeout(() => {
        if (!win.isDestroyed() && !win.isVisible()) {
          logger.warn("[lifecycle] 窗口显示超时，强制显示");
          win.show();
          win.focus();
        }
      }, 3000); // 3秒超时
    }
  }

  /**
   * Before app close
   */
  async beforeClose(): Promise<void> {
    logger.info("[lifecycle] before-close");
  }
}

Lifecycle.toString = () => '[class Lifecycle]';

export {Lifecycle};