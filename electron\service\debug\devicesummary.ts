import { IECReq } from "../../interface/debug/request";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { MENU_ITEM } from "../../data/debug/menuItem";
import { ParamSummaryInfo, SummaryInfo } from "../../interface/debug/devicesummary";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { DebugInfoMenu } from "../../interface/debug/debuginfo";
import { debugInfoMenuService } from "./debuginfomenu";

/**
 * 装置汇总信息Service
 * 负责装置各类信息的统计、汇总等业务逻辑
 * <AUTHOR>
 * @class
 */
class DeviceSummaryService {
  // 缓存每个设备的参数定值汇总，使用 debugInfo.header.md5 作为失效依据
  private paramSummaryCache: Map<string, { md5?: string; data: ParamSummaryInfo[] }> = new Map();
  // 缓存每个设备的汇总信息，使用 debugInfo.header.md5 作为失效依据
  private summaryInfoCache: Map<string, { md5?: string; data: SummaryInfo }> = new Map();

  // 获取SG数量
  async getSGCount(req: IECReq<any>): Promise<number> {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountEntry")}:`,
      JSON.stringify(req)
    );
    try {
      // 复用 getSummaryInfo 的缓存和优化逻辑
      const summaryInfo = await this.getSummaryInfo(req);
      // SG 数量可以从 settingNum 中分离出来，但为了简化，我们直接计算
      const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(req.head.id);
      if (device && device.debugInfo) {
        const cacheKey = req.head.id;
        const currentMd5 = device.debugInfo.header?.md5;
        const cached = this.summaryInfoCache.get(cacheKey);
        if (cached && cached.md5 === currentMd5) {
          // 从缓存的汇总信息中快速计算 SG 数量
          let sgCount = 0;
          const dfs = (menus: DebugInfoMenu[]) => {
            for (const menu of menus || []) {
              if (menu.fc === MENU_ITEM.SG) {
                sgCount += menu.items?.length || 0;
              }
              if (menu.menus && menu.menus.length) dfs(menu.menus);
            }
          };
          dfs(device.debugInfo.menus || []);

          logger.info(
            `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountReturn")}:`,
            sgCount
          );
          return sgCount;
        }
      }

      // 降级到原始方法
      const sgCount = debugInfoMenuService.getTreeItemByFc(
        [MENU_ITEM.SG],
        req.head.id,
        false
      ).length;
      logger.info(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountReturn")}:`,
        sgCount
      );
      return sgCount;
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSGCountError")}:`,
        error
      );
      throw error;
    }
  }

  // 获取汇总信息
  async getSummaryInfo(req: IECReq<any>) {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoEntry")}:`,
      JSON.stringify(req)
    );
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    try {
      if (device && device.debugInfo) {
        // 命中缓存直接返回
        const cacheKey = req.head.id;
        const currentMd5 = device.debugInfo.header?.md5;
        const cached = this.summaryInfoCache.get(cacheKey);
        if (cached && cached.md5 === currentMd5) {
          logger.info(`[DeviceSummaryService] getSummaryInfo - 使用缓存`);
          return cached.data;
        }

        // 单次遍历统计所有类型，避免6次重复遍历
        const counts = new Map<string, number>();
        const targetFcs = [MENU_ITEM.SG, MENU_ITEM.SP, MENU_ITEM.ST, MENU_ITEM.MX, MENU_ITEM.CO, MENU_ITEM.BO];

        const dfs = (menus: DebugInfoMenu[]) => {
          for (const menu of menus || []) {
            const fc = menu.fc || "";
            if (targetFcs.includes(fc)) {
              const itemCount = menu.items?.length || 0;
              counts.set(fc, (counts.get(fc) || 0) + itemCount);
            }
            if (menu.menus && menu.menus.length) dfs(menu.menus);
          }
        };
        dfs(device.debugInfo.menus || []);

        const summaryinfo: SummaryInfo = {
          settingNum: (counts.get(MENU_ITEM.SG) || 0) + (counts.get(MENU_ITEM.SP) || 0),
          ykNum: counts.get(MENU_ITEM.CO) || 0,
          driveNum: counts.get(MENU_ITEM.BO) || 0,
          ycNum: counts.get(MENU_ITEM.MX) || 0,
          yxNum: counts.get(MENU_ITEM.ST) || 0,
        };

        // 写入缓存
        this.summaryInfoCache.set(cacheKey, { md5: currentMd5, data: summaryinfo });

        logger.info(
          `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoReturn")}:`,
          summaryinfo
        );
        return summaryinfo;
      }
      throw new Error(t("errors.noDataRetrieved"));
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getSummaryInfoError")}:`,
        error
      );
      throw error;
    }
  }

  // 获取定值汇总信息
  async getParamSummary(req: IECReq<any>) {
    logger.info(
      `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryEntry")}:`,
      JSON.stringify(req)
    );
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    try {
      if (device && device.debugInfo) {
        // 命中缓存直接返回
        const cacheKey = req.head.id;
        const currentMd5 = device.debugInfo.header?.md5;
        const cached = this.paramSummaryCache.get(cacheKey);
        if (cached && cached.md5 === currentMd5) {
          logger.info(`[DeviceSummaryService] getParamSummary - 使用缓存`);
          return cached.data;
        }

        // 单次遍历聚合统计（Map 聚合，避免 push + 递归数组扩张开销）
        const agg = new Map<string, number>();
        const dfs = (menus: DebugInfoMenu[]) => {
          for (const item of menus || []) {
            const fc = item.fc || "";
            if (fc === MENU_ITEM.SG || fc === MENU_ITEM.SP) {
              const name = item.desc || item.name || "";
              const count = item.items?.length || 0;
              if (count > 0) agg.set(name, (agg.get(name) || 0) + count);
            }
            if (item.menus && item.menus.length) dfs(item.menus);
          }
        };
        dfs(device.debugInfo.menus || []);

        const summaryinfo: ParamSummaryInfo[] = Array.from(agg.entries()).map(
          ([name, value]) => ({ name, value })
        );

        // 写入缓存
        this.paramSummaryCache.set(cacheKey, { md5: currentMd5, data: summaryinfo });

        logger.info(
          `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryReturn")}:`,
          "success"
        );
        return summaryinfo;
      }
      throw new Error(t("errors.noDataRetrieved"));
    } catch (error) {
      logger.error(
        `[DeviceSummaryService] ${t("logs.deviceSummaryService.getParamSummaryError")}:`,
        error
      );
      throw error;
    }
  }
}

DeviceSummaryService.toString = () => "[class DeviceSummaryService]";
const deviceSummaryService = new DeviceSummaryService();

export { DeviceSummaryService, deviceSummaryService };
