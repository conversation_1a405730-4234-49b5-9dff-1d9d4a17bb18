<template>
  <el-dialog v-model="dialogVisible" :title="t('layout.header.password.title')" width="500px" draggable>
    <span>This is Password</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t("layout.header.password.cancel") }}</el-button>
        <el-button type="primary" @click="dialogVisible = false">{{ t("layout.header.password.confirm") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>
