<template>
  <el-dialog class="about-main" v-model="dialogVisible" :title="''" width="900px" draggable>
    <div class="about-header">
      <img class="about-logo" :src="logoSrc" alt="logo" />
      <div class="about-title-group">
        <span class="about-title">{{ packageJson.name }}</span>
        <span class="about-subtitle">{{ t("layout.about.title") }}</span>
      </div>
    </div>
    <el-divider />
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="hover" class="about-card">
          <h4 class="title">{{ t("layout.about.introduction") }}</h4>
          <span class="text">{{ t("layout.about.description") }}</span>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <el-card shadow="hover" class="about-card">
          <h4 class="title">{{ t("layout.about.versionInfo") }}</h4>
          <el-descriptions :column="1" border label-width="250px">
            <el-descriptions-item :label="t('layout.about.toolName')" label-align="left">
              <el-tag type="success">{{ packageJson.name }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="t('layout.about.version')" label-align="left">
              <el-tag type="info">{{ packageJson.version }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="t('layout.about.machineCode')" label-align="left">
              <div class="machine-code-container">
                <el-tooltip :content="machineCode || t('layout.about.loading')" placement="top" effect="dark">
                  <el-tag type="warning" class="machine-code-tag">{{ machineCode || t("layout.about.loading") }}</el-tag>
                </el-tooltip>
                <el-button v-if="machineCode" type="primary" size="small" @click="copyMachineCode" class="copy-btn">
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
    <el-card shadow="hover" class="about-card feature-card">
      <h4 class="title">{{ t("layout.about.versionFeatures") }}</h4>
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="12" v-for="(feature, idx) in features" :key="idx">
          <div class="feature-item">
            <el-icon class="feature-icon"><CircleCheckFilled /></el-icon>
            <span>{{ t(feature) }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import packageJson from "./../../../../../../package.json";
import logo from "@/assets/images/logo.png";
import { CircleCheckFilled, CopyDocument } from "@element-plus/icons-vue";
import { licenseApi } from "@/api/modules/sys/activate/license";

const { t } = useI18n();
const dialogVisible = ref(false);
const machineCode = ref<string>("");
// 用于国际化的特性key数组
const features = ["layout.about.features.visualTool", "layout.about.features.configTool", "layout.about.features.themeTool"];
const logoSrc = logo;

const openDialog = () => {
  dialogVisible.value = true;
  loadMachineCode();
};

const loadMachineCode = async () => {
  try {
    const response = await licenseApi.getMachineCode();
    // 适配 ResultData<string> 结构
    if (response && typeof response.data === "string") {
      machineCode.value = response.data;
    } else {
      machineCode.value = t("layout.about.machineCodeError");
    }
  } catch (error) {
    console.error("获取机器码失败:", error);
    machineCode.value = t("layout.about.machineCodeError");
  }
};

const copyMachineCode = async () => {
  try {
    await navigator.clipboard.writeText(machineCode.value);
    ElMessage.success(t("layout.about.copySuccess"));
  } catch (error) {
    console.error("复制失败:", error);
    ElMessage.error(t("layout.about.copyError"));
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss">
.about-main {
  .el-dialog__header {
    padding: 0;
    border-bottom: none;
  }
  .el-dialog__body {
    padding: 24px 32px 32px;
    background: #fafbfc;
    [class="dark"] & {
      background: #23272e;
    }
  }
  .about-header {
    display: flex;
    gap: 18px;
    align-items: center;
    margin-bottom: 8px;
    .about-logo {
      width: 48px;
      height: 48px;
      object-fit: contain;
      background: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
      [class="dark"] & {
        background: #23272e;
        box-shadow: 0 2px 8px rgb(0 0 0 / 40%);
      }
    }
    .about-title-group {
      display: flex;
      flex-direction: column;
      .about-title {
        font-size: 24px;
        font-weight: bold;
        color: var(--el-color-primary);
        letter-spacing: 1px;
        [class="dark"] & {
          color: #8cd2ff;
        }
      }
      .about-subtitle {
        margin-top: 2px;
        font-size: 15px;
        color: var(--el-text-color-secondary);
        [class="dark"] & {
          color: #b0b8c7;
        }
      }
    }
  }
  .about-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 180px;
    margin-bottom: 18px;
    border-radius: 12px;
    .title {
      padding-left: 10px;
      margin: 0 0 12px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      border-left: 4px solid var(--el-color-primary);
      [class="dark"] & {
        color: #8cd2ff;
        border-left: 4px solid #8cd2ff;
      }
    }
    .text {
      font-size: 15px;
      line-height: 1.6;
      color: var(--el-text-color-regular);
      [class="dark"] & {
        color: #b0b8c7;
      }
    }
  }
  .feature-card {
    margin-top: 8px;
    .feature-item {
      display: flex;
      gap: 8px;
      align-items: center;
      padding: 8px 0;
      font-size: 15px;
      color: var(--el-text-color-regular);
      border-bottom: 1px dashed var(--el-border-color-lighter);
      transition: background 0.2s;
      &:last-child {
        border-bottom: none;
      }
      &:hover {
        background: #f0f6ff;
        [class="dark"] & {
          background: #23272e;
        }
      }
      .feature-icon {
        font-size: 18px;
        color: var(--el-color-primary);
        [class="dark"] & {
          color: #8cd2ff;
        }
      }
    }
  }
  .machine-code-container {
    display: flex;
    gap: 8px;
    align-items: center;
    .machine-code-tag {
      max-width: 180px;
      overflow: hidden;
      font-family: "Courier New", monospace;
      font-size: 12px;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      [class="dark"] & {
        color: #b0b8c7;
        background: #23272e;
      }
    }
    .copy-btn {
      flex-shrink: 0;
    }
  }

  // 强制设置 el-descriptions label 宽度
  ::v-deep .el-descriptions__label,
  ::v-deep .el-descriptions-item__label {
    display: inline-block;
    width: 250px !important;
    word-break: break-all;
    white-space: pre-line;
    [class="dark"] & {
      color: #b0b8c7;
    }
  }
}
</style>
