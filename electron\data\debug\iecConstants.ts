import path from "node:path";
import Ps from "ee-core/ps";
import UtilsIs from "ee-core/utils/is";
import { logger } from "ee-core/log";
import { isProd } from "ee-core/ps";

const getBasePath = (): string => {
  const execDir = Ps.getExecDir();
  // 资源路径不同
  let dir = "";
  // 打包后  execDir为 应用程序 exe\dmg\dep软件所在目录；打包前该值是项目根目录
  // windows和MacOs不一样
  dir = execDir;
  if (UtilsIs.macOS()) {
    dir = path.join(execDir, "..");
  }
  return dir;
};
const BASE_PATH = getBasePath();
let BUILD_PATH = "build";
// logger.info("isProd : " + isProd());
if (isProd()) {
  BUILD_PATH = "resources"; // 打包后路径
}
const PATH_MAIN = path.join(BASE_PATH, BUILD_PATH, "/extraResources/.config");
const PATH_CONFIG = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/.config/device"
);
const PATH_CONFIGURE = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/.config/hmi"
);
const PATH_CFG = path.join(BASE_PATH, BUILD_PATH, "/extraResources/cfg/device");
const PATH_THIRDLIB = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/thirdlib"
);
const PATH_WORKSPACE = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/workspace"
);
const PATH_DLL = path.join(BASE_PATH, BUILD_PATH, "/extraResources/dll");

const PATH_TEMPLATE = path.join(BASE_PATH, BUILD_PATH, "/extraResources/template");
const PATH_ZIP = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/zip_with_random_key"
);

const PATH_ZIP_ENCRYPT = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/zip_with_random_key/encrypt.exe"
);
const PATH_ZIP_DECRYPT = path.join(
  BASE_PATH,
  BUILD_PATH,
  "/extraResources/zip_with_random_key/decrypt.exe"
);

const IECCONSTANTS = {
  CODE_SUCCESS: 1,
  CODE_AUTH: 2,
  PRODUCTNAME: "VisualDebug",
  CODE_ERROR: 99,
  PATH_CONFIG: PATH_CONFIG,
  PATH_CONFIGURE: PATH_CONFIGURE,
  PATH_CFG: PATH_CFG,
  PATH_MAIN: PATH_MAIN,
  PATH_DLL: PATH_DLL,
  PATH_THIRDLIB,
  PATH_TEMPLATE,
  PATH_WORKSPACE,
  PATH_ZIP_ENCRYPT,
  PATH_ZIP_DECRYPT,
  PATH_ZIP,
  TREE_CONFIG_TYPE_SET: 3,
  TREE_CONFIG_TYPE_REPORT: 6,
  TP215_FAN_ACT_VAL_EX: 215,
  PATH_DEVICE_CFG: PATH_CONFIG + "/deviceCfg.json",
  PATH_CONFIGURE_CFG: PATH_CONFIGURE + "/configureCfg.json",
  PATH_CONFIGURE_EQUIPMENT: PATH_CONFIGURE + "/equipment.json",
  BATCH_COUNT: 500,
  WRITE_SETTING_TIMEOUT: 30000,
};
export default IECCONSTANTS;
