import { ResPage, VisLog } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/audit/logVisit/");

const visLogApi = {
  /** 获取访问日志分页 */
  page(params: VisLog.Page) {
    return http.get<ResPage<VisLog.VisLogInfo>>("page", params);
  },
  /** 获取访问日志折线图数据 */
  lineChart() {
    return http.get<VisLog.LineChart[]>("lineChartData", {}, { loading: false });
  },
  /** 获取访问日志饼状图数据 */
  pieChart() {
    return http.get<VisLog.PineChart[]>("pieChartData", {}, { loading: false });
  },
  /** 清空访问日志 */
  delete(category: string) {
    return http.post("delete", { category });
  }
};

export { visLogApi };
