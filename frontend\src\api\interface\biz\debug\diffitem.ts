interface DiffItem {
  /* 根据实际数据结构补充字段 */
  name: string;
  description: string;
  minValue: number;
  maxValue: number;
  step: number;
  unit: string;
  oldValue: number;
  newValue: number;
  inf: number;
  grp: string;
  type: string;
  grpName: string;
}
interface GroupInfo {
  id: string;
  grpname: string;
}
interface DiffTabData {
  grpname: string;
  data: DiffItem[];
}
interface ParamItem {
  data: any;
  index: number;
  paramName: string;
  paramDesc: string;
  inf: string;
  value: string;
  minValue: string;
  maxValue: string;
  step: string;
  unit: string;
  fc: string;
  grp: string;
  type: string;
  grpName?: string;
}

interface SheetData<T = any> {
  data: T[];
  sheetName?: string;
  headers: string[];
}
// 定义 diffData 的接口
type DiffData = DiffTabData[];
export type { DiffItem, DiffData, GroupInfo, ParamItem, SheetData };
