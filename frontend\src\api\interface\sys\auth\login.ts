export namespace Login {
  /** 模块信息 */
  export type ModuleInfo = {
    id: number | string;
    title: string;
    code: string;
    icon: string;
    description: string;
  };

  /**
   * 验证码
   */
  interface ValidCode {
    /**  验证码 */
    validCode: string;
    /** 验证码请求号 */
    validCodeReqNo: string;
  }

  /**
   * 账号密码登录表单
   */
  export interface LoginForm extends ValidCode {
    account: string;
    password: string;
    tenantId?: number | string;
  }

  /**
   * 手机号登录表单
   */
  export interface PhoneLoginForm extends ValidCode {
    /** 手机号 */
    phone: string;
    /** 租户Id */
    tenantId?: number | string;
  }

  /** 获取手机验证码请求 */
  export interface ReqPhoneValidCode extends ValidCode {
    /** 手机号 */
    phone: string;
  }

  /**
   * 注销表单
   */
  export interface Logout {
    token: string;
  }

  // 登录返回
  export interface Login {
    /** token */
    token: string;
    /** 默认模块 */
    defaultModule: string;
    /** 模块列表 */
    moduleList: ModuleInfo[];
  }

  /**
   * 验证码返回
   */
  export interface ReqValidCode {
    /** 验证码 */
    validCodeBase64: string;
    /** 验证码请求号 */
    validCodeReqNo: string;
  }

  /** 用户信息 */
  export interface LoginUserInfo {
    /** 用户id */
    id: string | number;
    /** 用户名 */
    account: string;
    /** 用户姓名 */
    name: string;
    /** 用户昵称 */
    nickname: string;
    /** 用户头像 */
    avatar: string;
    /** 用户性别 */
    gender: string;
    /** 民族 */
    nation: string;
    /** 出生日期 */
    birthday: string;
    /** 家庭住址 */
    homeAddress: string;
    /** 电话号码 */
    phone: string;
    /** 邮箱 */
    email: string;
    /** 用户签名 */
    signature: string;
    /** 默认模块 */
    defaultModule: number | string;
    /** 模块列表 */
    moduleList: ModuleInfo[];
    /** 组织全程 */
    orgNames: string;
    /** 职位名称 */
    positionName: string;
    /** 按钮码集合 */
    buttonCodeList: string[];
    /** 权限码集合 */
    permissionCodeList: string[];
    /** 角色码集合 */
    roleCodeList: string[];
  }
}
