export default {
  downList: {
    title: "Download List",
    deviceDirectory: "Device Directory",
    addFile: "Add File",
    addFolder: "Add Folder",
    defaultExportFileName: "Download File List.xlsx",
    exportTitle: "Export Download File List",
    importTitle: "Import Download File List",
    exportSuccess: "Download file list exported successfully, {path}",
    exportFailed: "Failed to export download file list",
    importSuccess: "Download file list imported successfully",
    importFailed: "Failed to import download file list: {msg}",
    fileExists: "File {path} already exists, failed to add!",
    fileDeleted: "File {path} deleted",
    filesDeleted: "Files deleted in batch",
    buttons: {
      addFile: "Add File",
      addFolder: "Add Folder",
      import: "Import",
      export: "Export",
      delete: "Delete",
      clear: "Clear",
      moveUp: "Move Up",
      moveDown: "Move Down"
    },
    columns: {
      index: "Index",
      fileName: "File Name",
      fileSize: "File Size",
      filePath: "File Path",
      lastModified: "Last Modified",
      operation: "Operation"
    },
    export: {
      defaultPath: "Download List",
      title: "Export Download List"
    },
    import: {
      title: "Import Download List"
    },
    dialog: {
      title: "Prompt",
      confirm: "Confirm"
    },
    messages: {
      filesDeleted: "Files deleted",
      exportSuccess: "Export successful",
      exportFailed: "Export failed",
      importSuccess: "Import successful",
      importFailed: "Import failed"
    }
  }
};
