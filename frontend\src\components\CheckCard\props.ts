/**
 * @description  props
 * @license Apache License Version 2.0
 */
// 从Vue库中导入PropType类型
import type { PropType as VuePropType } from "vue";
// 从本地types文件中导入OptionsConfig类型

// 定义一个新类型PropType，它是VuePropType的别名
declare type PropType<T> = VuePropType<T>;

// 定义一个包含CheckModule组件props的对象
export const basicProps = {
  value: {
    type: Array as PropType<number[] | string[]> // value prop的类型是数字或字符串的数组
  },
  width: {
    type: [Number, String], // width prop的类型是数字或字符串
    default: 320 // width prop的默认值是320
  },
  multiple: {
    type: Boolean, // multiple prop的类型是布尔值
    default: false // multiple prop的默认值是false
  },
  hoverable: {
    type: Boolean, // hoverable prop的类型是布尔值
    default: false // hoverable prop的默认值是false
  },
  bordered: {
    type: Boolean, // bordered prop的类型是布尔值
    default: true // bordered prop的默认值是true
  },
  options: {
    type: Array as PropType<CheckCard.OptionsConfig[]>, // options prop的类型是OptionsConfig对象的数组
    default: () => [] // options prop的默认值是一个空数组
  }
};
