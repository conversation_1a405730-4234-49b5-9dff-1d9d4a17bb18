import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/param/");

const paramSettingApi = {
  /** 获取参数（指定 deviceId） */
  getParamByDevice(deviceId: string, param: any) {
    return ipc.iecInvokeWithDevice<{}>("getParam", param, deviceId);
  },

  getAllParamByDevice(deviceId: string, param: any) {
    return ipc.iecInvokeWithDevice<{}>("getAllParam", param, deviceId);
  },

  confirmParamByDevice(deviceId: string, param: any[]) {
    return ipc.iecInvokeWithDevice<{}>("confirmParam", param, deviceId);
  },

  importParamByDevice(deviceId: string, param: any[]) {
    return ipc.iecInvokeWithDevice<{}>("importParam", param, deviceId);
  },

  getDiffParamByDevice(deviceId: string, param: { path: string; grpName: string }) {
    return ipc.iecInvokeWithDevice<{}>("getDiffParam", param, deviceId);
  },

  getAllDiffParamByDevice(deviceId: string, param: { path: string; grpName: string }) {
    return ipc.iecInvokeWithDevice<{}>("getAllDiffParam", param, deviceId);
  },

  exportParamByDevice(deviceId: string, param: { path: string; grpName: string }) {
    return ipc.iecInvokeWithDevice<{}>("exportParam", param, deviceId);
  },

  exportAllParamByDevice(deviceId: string, param: { path: string; grpName: string }) {
    return ipc.iecInvokeWithDevice<{}>("exportAllParam", param, deviceId);
  },

  /** 获取当前运行区（指定 deviceId） */
  getCurrentRunAreaByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("getCurrentRunArea", undefined, deviceId);
  },

  /** 选择定值区（指定 deviceId） */
  selectRunAreaByDevice(deviceId: string, param: { runArea: number }) {
    return ipc.iecInvokeWithDevice<{}>("selectRunArea", param, deviceId);
  }
};

export { paramSettingApi };
