import { ResPage, OpLog, ReqId } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/audit/logOperate/");

const opLogApi = {
  /** 获取操作日志分页 */
  page(params: OpLog.Page) {
    return http.get<ResPage<OpLog.OpLogInfo>>("page", params);
  },
  /** 获取操作日志柱状图数据 */
  columnChart() {
    return http.get<OpLog.ColumnChart[]>("columnChartData", {}, { loading: false });
  },
  /** 获取操作日志饼状图数据 */
  pieChart() {
    return http.get<OpLog.PineChart[]>("pieChartData", {}, { loading: false });
  },
  /** 获取操作日志详情 */
  detail(params: ReqId) {
    return http.get<OpLog.OpLogInfo>("detail", params, { loading: false });
  },
  /** 清空操作日志 */
  delete(category: string) {
    return http.post("delete", { category });
  }
};

export { opLogApi };
