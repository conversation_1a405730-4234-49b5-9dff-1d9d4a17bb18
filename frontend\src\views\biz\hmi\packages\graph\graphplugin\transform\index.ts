import { CssLoader, Graph, Node } from "@antv/x6";
import { Transform as T } from "@antv/x6-plugin-transform";
import { content } from "./style/raw";
import { TransformImpl } from "./Transform";
import { isLine } from "../../GraphUtil";
/**
 * <AUTHOR>
 * @version 1.0 2025-03-24
 */
export class Transform extends T {
  protected currGraph!: Graph;
  constructor(options: T.Options = {}) {
    super(options);
    CssLoader.ensure(`${this.name}-line`, content);
  }
  init(graph: Graph) {
    super.init(graph);
    this.currGraph = graph;
  }
  protected createTransform(node: Node) {
    const options = this.getTransformOptions(node);
    if (options.resizable || options.rotatable) {
      // 创建自定义的 TransformImpl 实例
      const transformImpl = new TransformImpl(options, node, this.currGraph);

      // 如果是线，添加特殊样式
      if (transformImpl.view) {
        const cellNode = transformImpl.view.cell as Node;
        if (cellNode && isLine(cellNode)) {
          transformImpl.renderHandles();
        }
      }

      return transformImpl;
    }
    return null;
  }
}
