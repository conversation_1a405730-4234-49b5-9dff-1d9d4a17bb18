import { Disposable, IDisablable, Graph } from "@antv/x6";
import { DataInfo } from "../../../graph/Graph";
/**
 * 数据插件，不同层次传递数据使用
 * <AUTHOR>
 * @version 1.0 2025-03-11
 */
export class DataImpl extends Disposable implements IDisablable {
  data: DataInfo;
  private get graph() {
    return this.options.graph;
  }

  constructor(private readonly options: DataImpl.Options & { graph: Graph }) {
    super();
    this.data = options.data;
  }

  get disabled() {
    return this.options.enabled !== true;
  }

  enable() {
    if (this.disabled) {
      this.options.enabled = true;
    }
  }

  disable() {
    if (!this.disabled) {
      this.options.enabled = false;
    }
  }

  //@Disposable.dispose()
  dispose() {
    console.log("dispose");
  }
}

export namespace DataImpl {
  export type Action = "keypress" | "keydown" | "keyup";
  export type Handler = (e: KeyboardEvent) => void;

  export interface Options {
    enabled?: boolean;
    data: DataInfo;
  }
}
