import { Cell, Graph } from "@antv/x6";
import GraphGroup from "./GraphGroup";
import { isGroup } from "./GraphUtil";

class GraphOperator {
  toFront(graph: Graph) {
    const cells = graph.getSelectedCells();
    if (!cells || cells.length == 0) {
      return;
    }
    let childs: Cell[] | null;
    for (const cell of cells) {
      childs = cell.getChildren();
      if (childs != null) {
        // 所有的子元素也需要置前
        // 所有的子元素需要按z-index排序，保证原来的层次
        const arrs: Cell[] = [];
        for (const child of childs) {
          arrs.push(child);
        }
        arrs.sort((a, b) => {
          let x = a.zIndex ? a.zIndex : 0;
          let y = b.zIndex ? b.zIndex : 0;
          if (x > y) {
            return 1;
          } else if (x < y) {
            return -1;
          }
          return 0;
        });
        arrs.forEach(item => {
          item.toFront();
        });
      }
      cell.toFront();
    }
  }
  toBack(graph: Graph) {
    const cells = graph.getSelectedCells();
    if (!cells || cells.length == 0) {
      return;
    }
    let childs: Cell[] | null;
    for (const cell of cells) {
      cell.toBack();
      childs = cell.getChildren();
      if (childs != null) {
        // 所有的子元素需要按z-index排序，保证原来的层次
        const arrs: Cell[] = [];
        for (const child of childs) {
          arrs.push(child);
        }
        arrs.sort((a, b) => {
          let x = a.zIndex ? a.zIndex : 0;
          let y = b.zIndex ? b.zIndex : 0;
          if (x > y) {
            return -1;
          } else if (x < y) {
            return 1;
          }
          return 0;
        });
        arrs.forEach(item => {
          item.toBack();
        });
      }
    }
  }
  resize(cell: Cell, graph: Graph) {
    if (isGroup(cell)) {
      graph.trigger("node:resizing", { node: cell });
    }
  }
  rotate(cell: Cell, graphGroup: GraphGroup) {
    if (isGroup(cell)) {
      graphGroup.rotate(cell);
    }
  }
  rotating(cell: Cell, graphGroup: GraphGroup) {
    if (isGroup(cell)) {
      graphGroup.rotating(cell);
    }
  }
  deleteSelectCells(graph) {
    const cells = graph.getSelectedCells();
    if (cells.length > 0) {
      graph.removeCells(cells);
    }
  }
}
export default GraphOperator;
