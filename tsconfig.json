{
  "compilerOptions": {
    "target": "es6",
    "module": "commonjs",
    "useDefineForClassFields": true,
    "skipLibCheck": true,
    "types": ["node"],
    "esModuleInterop": true,

    /* Bundler mode */
    "moduleResolution": "node", // node
    "resolveJsonModule": true,
    "isolatedModules": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitAny": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "forceConsistentCasingInFileNames": true,
  },
  "include": [
    "./electron/**/*"
  ]
}
  