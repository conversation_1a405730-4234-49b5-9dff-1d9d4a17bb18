export default {
  limit: {
    user: {
      title: "User Management",
      form: {
        add: "Add User",
        edit: "Edit User",
        view: "View User",
        username: "<PERSON>rna<PERSON>",
        password: "Password",
        confirmPassword: "Confirm Password",
        realName: "Real Name",
        phone: "Phone Number",
        email: "Email",
        org: "Organization",
        role: "Role",
        status: "Status",
        description: "Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          username: "Please enter username",
          password: "Please enter password",
          confirmPassword: "Please confirm password",
          realName: "Please enter real name",
          phone: "Please enter phone number",
          email: "Please enter email",
          org: "Please select organization",
          role: "Please select role",
          status: "Please select status",
          passwordNotMatch: "The two passwords do not match"
        }
      },
      columns: {
        username: "Username",
        realName: "Real Name",
        phone: "Phone Number",
        email: "Email",
        org: "Organization",
        role: "Role",
        status: "Status",
        operation: "Operation"
      },
      status: {
        enable: "Enable",
        disable: "Disable"
      },
      resetPassword: {
        title: "Reset Password",
        password: "New Password",
        confirmPassword: "Confirm Password",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          password: "Please enter new password",
          confirmPassword: "Please confirm password",
          passwordNotMatch: "The two passwords do not match"
        }
      },
      assignRole: {
        title: "Assign Role",
        warning: "Please select the role to assign",
        cancel: "Cancel",
        confirm: "Confirm"
      }
    }
  }
};
