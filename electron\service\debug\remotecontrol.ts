import { CtlResult, SelectRequestData } from "iec-upadrpc/dist/src/data";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { IECReq } from "../../interface/debug/request";
import { IECResult } from "iec-common/dist/data/iecdata";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";

class RemoteControlService {
  constructor() {}

  // 遥控带值选择
  async ykSelectWithValue(
    req: IECReq<SelectRequestData>
  ): Promise<IECResult<CtlResult>> {
    // 遥控带值选择方法入口日志
    logger.info(
      "[RemoteControlService] ykSelectWithValue 入参:",
      JSON.stringify(req)
    );
    try {
      const requestData = req.data;
      const id = req.head.id;
      const singleGlobalDeviceInfo =
        GlobalDeviceData.getInstance().deviceInfoMap.get(id);
      const client = singleGlobalDeviceInfo?.deviceClient;
      if (client && client.isConnected()) {
        logger.info(
          "[RemoteControlService]   ykSelectWithValue requestData",
          JSON.stringify(requestData)
        );
        const res: IECResult<CtlResult> = await client.select(
          requestData,
          false
        );
        logger.info("[RemoteControlService]   ykSelectWithValue 返回:", res);
        return res;
      }
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.connectionDisconnected"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    } catch (error) {
      logger.error("[RemoteControlService]   ykSelectWithValue 异常:", error);
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.interfaceCallException"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    }
  }

  /**
   * 遥控执行
   * @param req 请求数据
   * @returns
   */
  async ykSelectValueConfirm(
    req: IECReq<SelectRequestData>
  ): Promise<IECResult<CtlResult>> {
    // 遥控执行确认方法入口日志
    logger.info(
      "[RemoteControlService] ykSelectValueConfirm 入参:",
      JSON.stringify(req)
    );
    try {
      const requestData = req.data;
      const id = req.head.id;
      const singleGlobalDeviceInfo =
        GlobalDeviceData.getInstance().deviceInfoMap.get(id);
      const client = singleGlobalDeviceInfo?.deviceClient;
      if (client && client.isConnected()) {
        const res: IECResult<CtlResult> =
          await client.selectConfirm(requestData);
        logger.info("[RemoteControlService] ykSelectValueConfirm 返回:", res);
        return res;
      }
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.connectionDisconnected"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    } catch (error) {
      logger.error("[RemoteControlService] ykSelectValueConfirm 异常:", error);
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.interfaceCallException"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    }
  }

  /**
   * 遥控执行
   * @param req 请求数据
   * @returns
   */
  async ykSelectValueCancel(
    req: IECReq<SelectRequestData>
  ): Promise<IECResult<CtlResult>> {
    // 遥控执行取消方法入口日志
    logger.info(
      "[RemoteControlService] ykSelectValueCancel 入参:",
      JSON.stringify(req)
    );
    try {
      const requestData = req.data;
      const id = req.head.id;
      const singleGlobalDeviceInfo =
        GlobalDeviceData.getInstance().deviceInfoMap.get(id);
      const client = singleGlobalDeviceInfo?.deviceClient;
      if (client && client.isConnected()) {
        const res: IECResult<CtlResult> =
          await client.selectCancel(requestData);
        logger.info("[RemoteControlService] ykSelectValueCancel 返回:", res);
        return res;
      }
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.connectionDisconnected"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    } catch (error) {
      logger.error("[RemoteControlService] ykSelectValueCancel 异常:", error);
      const data: CtlResult = {
        success: false,
        addCauseDesc: t("remoteControl.interfaceCallException"),
        ServiceError: 0,
        addCause: "",
      };
      const iecRes: IECResult<CtlResult> = new IECResult<CtlResult>();
      iecRes.code = 1;
      iecRes.data = data;
      return iecRes;
    }
  }
}

RemoteControlService.toString = () => "[class RemoteControlService]";
const remoteControlService = new RemoteControlService();

export { RemoteControlService, remoteControlService };
