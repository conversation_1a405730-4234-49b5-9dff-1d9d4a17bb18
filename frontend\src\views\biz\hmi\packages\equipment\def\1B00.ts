import { calculateArc } from "../../graph/GraphUtil";

const e = {
  shape: "1B00",
  markup: [
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(0, 0, 9, 9, 0, 180, 0)
      }
    },
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(9, 0, 9, 9, 0, 180, 0)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 4,
        x2: 0,
        y2: 26
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 36,
        y1: 3,
        x2: 36,
        y2: 25
      }
    },
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(18, 0, 9, 9, 0, 180, 0)
      }
    },
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(27, 0, 9, 9, 0, 180, 0)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    arc: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
