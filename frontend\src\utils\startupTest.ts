/**
 * 启动性能测试工具
 */

interface StartupTestResult {
  testName: string;
  duration: number;
  success: boolean;
  details?: any;
  timestamp: number;
}

interface StartupTestSuite {
  suiteName: string;
  results: StartupTestResult[];
  totalDuration: number;
  successRate: number;
}

class StartupPerformanceTester {
  private testResults: StartupTestResult[] = [];
  private suiteStartTime: number = 0;

  /**
   * 开始测试套件
   */
  startTestSuite(suiteName: string): void {
    console.group(`🧪 启动性能测试: ${suiteName}`);
    this.suiteStartTime = performance.now();
    this.testResults = [];
  }

  /**
   * 执行单个测试
   */
  async runTest(testName: string, testFn: () => Promise<any> | any): Promise<StartupTestResult> {
    const startTime = performance.now();
    let success = false;
    let details: any = null;

    try {
      console.time(`⏱️ ${testName}`);
      const result = await testFn();
      console.timeEnd(`⏱️ ${testName}`);

      success = true;
      details = result;
      console.log(`✅ ${testName} - 通过`);
    } catch (error) {
      console.error(`❌ ${testName} - 失败:`, error);
      details = error;
    }

    const duration = performance.now() - startTime;
    const testResult: StartupTestResult = {
      testName,
      duration,
      success,
      details,
      timestamp: Date.now()
    };

    this.testResults.push(testResult);
    return testResult;
  }

  /**
   * 结束测试套件并生成报告
   */
  endTestSuite(suiteName: string): StartupTestSuite {
    const totalDuration = performance.now() - this.suiteStartTime;
    const successCount = this.testResults.filter(r => r.success).length;
    const successRate = (successCount / this.testResults.length) * 100;

    const suite: StartupTestSuite = {
      suiteName,
      results: [...this.testResults],
      totalDuration,
      successRate
    };

    this.generateReport(suite);
    console.groupEnd();

    return suite;
  }

  /**
   * 生成测试报告
   */
  private generateReport(suite: StartupTestSuite): void {
    console.group("📊 测试报告");

    console.log(`测试套件: ${suite.suiteName}`);
    console.log(`总耗时: ${suite.totalDuration.toFixed(2)}ms`);
    console.log(`成功率: ${suite.successRate.toFixed(1)}%`);
    console.log(`测试数量: ${suite.results.length}`);

    // 按耗时排序显示最慢的测试
    const sortedResults = [...suite.results].sort((a, b) => b.duration - a.duration);

    console.group("⏱️ 耗时排行");
    sortedResults.slice(0, 5).forEach((result, index) => {
      const status = result.success ? "✅" : "❌";
      console.log(`${index + 1}. ${status} ${result.testName}: ${result.duration.toFixed(2)}ms`);
    });
    console.groupEnd();

    // 显示失败的测试
    const failedTests = suite.results.filter(r => !r.success);
    if (failedTests.length > 0) {
      console.group("❌ 失败的测试");
      failedTests.forEach(result => {
        console.error(`${result.testName}:`, result.details);
      });
      console.groupEnd();
    }

    console.groupEnd();
  }

  /**
   * 测试缓存性能
   */
  async testCachePerformance(): Promise<void> {
    await this.runTest("缓存写入性能", async () => {
      const { setStartupCache } = await import("@/utils/startupCache");
      const testData = { test: "data", timestamp: Date.now() };

      const startTime = performance.now();
      setStartupCache("menuList", testData);
      const duration = performance.now() - startTime;

      return { duration, success: duration < 10 }; // 期望小于10ms
    });

    await this.runTest("缓存读取性能", async () => {
      const { getStartupCache } = await import("@/utils/startupCache");

      const startTime = performance.now();
      const data = getStartupCache("menuList");
      const duration = performance.now() - startTime;

      return { duration, data, success: duration < 5 }; // 期望小于5ms
    });
  }

  /**
   * 测试组件加载性能
   */
  async testComponentLoadingPerformance(): Promise<void> {
    await this.runTest("Element Plus 组件加载", async () => {
      const startTime = performance.now();
      await import("element-plus");
      const duration = performance.now() - startTime;

      return { duration, success: duration < 100 }; // 期望小于100ms
    });

    await this.runTest("Vue Router 加载", async () => {
      const startTime = performance.now();
      await import("vue-router");
      const duration = performance.now() - startTime;

      return { duration, success: duration < 50 }; // 期望小于50ms
    });
  }

  /**
   * 测试图标加载性能
   */
  async testIconLoadingPerformance(): Promise<void> {
    await this.runTest("图标集合加载", async () => {
      const startTime = performance.now();

      try {
        const { downloadAndInstall } = await import("@/utils/iconify");
        await downloadAndInstall();
        const duration = performance.now() - startTime;

        return { duration, success: duration < 500 }; // 期望小于500ms
      } catch (error) {
        return { duration: performance.now() - startTime, success: false, error };
      }
    });
  }

  /**
   * 测试路由性能
   */
  async testRouterPerformance(): Promise<void> {
    await this.runTest("动态路由初始化", async () => {
      const startTime = performance.now();

      try {
        const { initDynamicRouter } = await import("@/routers/modules/dynamicRouter");
        await initDynamicRouter();
        const duration = performance.now() - startTime;

        return { duration, success: duration < 200 }; // 期望小于200ms
      } catch (error) {
        return { duration: performance.now() - startTime, success: false, error };
      }
    });
  }

  /**
   * 运行完整的启动性能测试
   */
  async runFullStartupTest(): Promise<StartupTestSuite> {
    this.startTestSuite("完整启动性能测试");

    // 测试各个组件的加载性能
    await this.testCachePerformance();
    await this.testComponentLoadingPerformance();
    await this.testIconLoadingPerformance();
    await this.testRouterPerformance();

    // 测试内存使用情况
    await this.runTest("内存使用检查", () => {
      if ("memory" in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;

        return {
          usedMB: usedMB.toFixed(2),
          totalMB: totalMB.toFixed(2),
          success: usedMB < 50 // 期望小于50MB
        };
      }

      return { success: true, message: "浏览器不支持内存监控" };
    });

    // 测试DOM节点数量
    await this.runTest("DOM节点数量检查", () => {
      const nodeCount = document.querySelectorAll("*").length;
      return {
        nodeCount,
        success: nodeCount < 1000 // 期望小于1000个节点
      };
    });

    return this.endTestSuite("完整启动性能测试");
  }
}

// 创建全局测试实例
export const startupTester = new StartupPerformanceTester();

// 便捷方法
export const runStartupPerformanceTest = () => startupTester.runFullStartupTest();

// 在开发模式下自动运行测试
if (import.meta.env.DEV) {
  // 延迟运行测试，确保应用完全启动
  setTimeout(() => {
    console.log("🚀 自动运行启动性能测试...");
    runStartupPerformanceTest().then(result => {
      console.log("📈 启动性能测试完成，总耗时:", result.totalDuration.toFixed(2) + "ms");
    });
  }, 2000);
}
