"use strict";

import { DebugInfo, DebugInfoItem } from "../../interface/debug/debuginfo";
import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import { DebugInfoMenu } from "../../interface/debug/debuginfo";
import { XmlFileManager } from "../../utils/xmlFileManager";
import { UpadRpcFileItem } from "iec-upadrpc/dist/src/data";
import { t } from "../i18n/i18n";


/**
 * 用来存在装置连接信息，变量信息，装置读取的文件信息，装置信息等内容
 * wzl
 *
 */
export class SingleGlobalDeviceInfo {
  private deviceId: string;
  private static SERVICE_ERROR = "ServiceError";
  debugInfo?: DebugInfo; // 读取的debug_xml文件数据
  deviceClient: UpadRcpClient;
  deviceInfoMenus: DebugInfoMenu[]; // 装置返回给前端的菜单
  variableItems: string[];
  // debug_info.xml文件中的ServiceError码提供对应的描述。
  enumTypes: Map<string, Map<string, string>> = new Map();
  debugInfoFile: string = "";
  debugItemMap: Map<string, string> = new Map(); // debug_info 里面item的name 和desc
  debugItemObjMap: Map<string, DebugInfoItem> = new Map();
  compareMd5Result: boolean = false;
  abortControllerMap: Map<string, AbortController> = new Map();
  abortTaskMap: Map<string, UpadRpcFileItem[]> = new Map();
  constructor(deviceId: string) {
    this.deviceId = deviceId;
    this.deviceClient = new UpadRcpClient();
    this.deviceInfoMenus = new Array();
    this.variableItems = new Array();
  }
  // 复位变量数据
  resetData() {
    this.variableItems = new Array();
  }
  getDeviceId() {
    return this.deviceId;
  }
  async initEnumTypeAndFile() {
    const xmlFileManager = new XmlFileManager();
    await xmlFileManager.putFirstTimeDataTypeTemplates(this);
    this.debugInfoFile = xmlFileManager.getFilePath(this.deviceId);
  }

  // 提供一个可以获取错误码的方法
  public getServiceErrMsgByCode(code: string) {
    const enumType = this.enumTypes.get(SingleGlobalDeviceInfo.SERVICE_ERROR);
    const errorMsg = enumType?.get(code);
    return errorMsg ? errorMsg : t("errors.getServiceErrorInfo");
  }

  public setVaribaleItem(variableItems: string[]) {
    this.variableItems = variableItems;
  }
}
