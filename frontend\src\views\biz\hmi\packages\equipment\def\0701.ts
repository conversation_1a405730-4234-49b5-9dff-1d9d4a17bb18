import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0701",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0.67, 4, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 2.33,
        y1: 0,
        x2: 2.33,
        y2: 4
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0.67, 28, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 2.33,
        y1: 35.67,
        x2: 2.33,
        y2: 31.67
      }
    },

    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 15.67,
        x2: 5,
        y2: 21.67
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.83,
        y1: 16.5,
        x2: 0.5,
        y2: 21.83
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
