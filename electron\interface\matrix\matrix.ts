import { SgValueWriteRequestData, UpadRpcFileDownloadItem } from "iec-upadrpc/dist/src/data";

// 设备操作进度类型
export interface DeviceProgress {
  deviceId: string;
  currentStep: "connect" | "download" | "import" | "disconnect" | "complete";
  fileProgress?: number; // 单个文件下载进度 (0~1)
  totalProgress: number; // 总进度 (0~1)
  status: "processing" | "success" | "error";
  message?: string;
}

export interface DeviceResult {
  deviceId: string;
  status: "success" | "error";
  totalProgress: number; // 总进度 (0~1)
  message?: string;
}
// 线程消息协议
export type WorkerMessage = { type: "progress"; data: DeviceProgress } | { type: "result"; data: DeviceResult };

export interface DeviceItem {
  index?: number;
  id: string;
  ip: string;
  name: string;
  port: string;
  encrypted: boolean;
  prjType: number;
  deviceType: number;
  isConnect: boolean;
  isActive: boolean;
  connectTimeout?: number;
  paramTimeout?: number;
  readTimeout?: number;
  connectTime: string;
  status: string; // 文件状态
  percentType: string; // 进度条
  percent: number; // 文件上传或者下载进度
  downFile: boolean;
  importParam: boolean;
}

export interface ParamItem {
  data: any;
  index: number;
  paramName: string;
  paramDesc: string;
  inf: string;
  value: string;
  minValue: string;
  maxValue: string;
  step: string;
  unit: string;
  fc: string;
  grp: string;
  type: string;
  grpName?: string;
}

export interface FileItem {
  // 文件相关信息字段
  checked: boolean;
  index?: number;
  id: number;
  fileName: string; // 文件名
  fileSize: number; // 文件大小
  fileSizeAs: string; // 文件大小
  type: string; // 文件类型
  path: string; // 文件下载地址
  lastModified: string; // 文件更新时间
  status: string; // 文件状态
  percentType: string; // 进度条
  percent: number; // 文件上传或者下载进度
  checkSum: number;
  hasTask: boolean;
  taskid: string; // 任务号
  fileParentPath: string;
}

export interface MatrixTaskItem {
  device: DeviceItem;
  downlist: UpadRpcFileDownloadItem[];
  paramList: SgValueWriteRequestData[];
  isReboot: boolean;
}
