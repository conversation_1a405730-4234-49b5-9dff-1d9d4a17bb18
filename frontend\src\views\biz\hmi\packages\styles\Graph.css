.toolbar {
  display: flex;
  flex-direction: row;
  height: 40px;
  margin: 0;
  padding: 0;
  justify-content: left;
  align-items: center;
}

.toolbar .toolbar-item {
  height: 100%;
  cursor: pointer;
  display: flex;
  justify-content: left;
  align-items: center;
  padding: 0 5px;
}

.toolbar .toolbar-item:hover {
  background-color: rgb(221.7, 222.6, 224.4);
}

.graph-contextmenu-container {
  position: absolute;
  background-color: #ffffff;
  min-width: 160px;
  z-index: 99999999;
  display: flex;
  flex-direction: column;
  padding: 5px 5px;
  border: 1px solid rgb(232.8, 233.4, 234.6, 0.8);
  border-radius: 3px;
  box-shadow: 3px 3px 5px 0 rgba(0, 0, 0, 0.3);
}

.graph-contextmenu-container > .graph-contextmenu > .graph-contextmenu-item {
  padding: 3px 16px;
  line-height: 30px;
  border-bottom: solid 1px #eee;
  cursor: pointer;
  text-align: left;
  color: #303133;
}

.graph-contextmenu-container > .graph-contextmenu > .graph-contextmenu-item:hover {
  background-color: rgb(232.8, 233.4, 234.6);
}

.graph-contextmenu-container > .graph-contextmenu > .graph-contextmenu-item-disabled {
  color: #c1c5cc;
}

.graph-contextmenu-container > .graph-contextmenu > .graph-contextmenu-item-disabled:hover {
  background-color: transparent;
}

.graph-contextmenu-container-hide {
  display: none;
}

.equipment-icon {
  cursor: pointer;
}

.equipment-icon:hover {
  color: #409eff;
}

.graph-text-block {
  word-wrap: break-word;
  word-break: break-all;
}

.x6-node-tool-editor {
  z-index: 99999999;
}

.graph-basic-item {
  cursor: pointer;
}

.graph-basic-item:hover {
  cursor: pointer;
  background-color: antiquewhite;
}

.graph-basic-item > .graph-equipment-img {
  max-width: 32px;
  max-height: 32px;
}

.graph-electric-item {
  cursor: pointer;
}

.graph-electric-item:hover {
  cursor: pointer;
  background-color: antiquewhite;
}

.graph-electric-item > .graph-equipment-img {
  max-width: 32px;
  max-height: 32px;
}

.graph-equipment-item {
  cursor: pointer;
}

.graph-equipment-item:hover {
  cursor: pointer;
  background-color: antiquewhite;
}

.graph-equipment-img {
  width: 100%;
  height: 100%;
}
