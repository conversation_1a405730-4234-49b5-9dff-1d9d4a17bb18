.table-main {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    .header-button-lf {
      display: flex;
      gap: 8px;
      align-items: center;
    }
    .header-button-ri {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  .table-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 0;
    color: var(--el-text-color-secondary);
    img {
      width: 120px;
      height: 120px;
      margin-bottom: 16px;
    }
  }
}

// 表格内容区域
:deep(.el-table) {
  flex: 1;
  overflow: hidden;
  .el-table__inner-wrapper {
    height: 100%;
  }
  .el-table__body-wrapper {
    height: calc(100% - 40px);
    overflow-y: auto;
  }
}

// 分页区域
:deep(.el-pagination) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid var(--el-border-color-light);
}

// 搜索表单区域
.search-form {
  padding: 16px;
  margin-bottom: 16px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

// 列设置抽屉
:deep(.el-drawer__body) {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
  overflow: hidden;
  .table-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    .el-table {
      flex: 1;
      overflow: hidden;
    }
  }
}

// 拖拽排序
.cursor-move {
  cursor: move;
}

// 表格工具栏
.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .toolbar-left {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  .toolbar-right {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
