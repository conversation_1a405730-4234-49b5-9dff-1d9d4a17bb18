// 请求响应参数（不包含data）
export interface Result {
  code: number;
  msg: string;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  [x: string]: any;
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  /** 页码 */
  pageNum: number;
  /** 数量 */
  pageSize: number;
  /** 排序字段 */
  sortField?: string;
  /** 排序方式 */
  sortOrder?: string;
  /** 关键字 */
  searchKey?: string;
}

/** id请求参数 */
export interface ReqId {
  /** id */
  id: number | string;
}

/** iec-rpc请求参数 */
export interface IECRpcReq<T> {
  /** head */
  head?: { id: string };
  data: T;
}

export interface IECNotify {
  type:
    | "connect"
    | "disconnect"
    | "error"
    | "readCommonReport"
    | "readAuditLogReport"
    | "readOperateReport"
    | "readTripReport"
    | "readGroupReport"
    | "fileUpload"
    | "matrixDownload"
    | "fileDownload"
    | "fileUploadError"
    | "fileUploadSuccess"
    | "fileDecrypt"
    | "upadRpcMessage";
  data: unknown;
  /** 后端附带的装置ID，用于前端多装置过滤 */
  deviceId?: string;
  /** 标识是否为部分数据（用于分批回调） */
  isPartial?: boolean;
}

export interface RealEventState {
  subscribe: boolean;
  type: string;
}

export * from "./sys";
export * from "./biz";
