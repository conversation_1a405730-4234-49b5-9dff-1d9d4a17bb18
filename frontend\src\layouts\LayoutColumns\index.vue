<!-- 分栏布局 -->
<template>
  <el-container class="layout">
    <div class="aside-split">
      <div class="logo flx-center">
        <img class="logo-img" :src="sysInfo.SYS_LOGO" alt="logo" />
      </div>
      <el-scrollbar>
        <el-menu mode="vertical" :collapse="isCollapse" :router="false" :default-active="activeMenu">
          <!-- 不能直接使用 SubMenu 组件，无法触发 el-menu 隐藏省略功能 -->
          <template v-for="subItem in menuList" :key="subItem.path">
            <el-sub-menu v-if="subItem.children?.length" :key="subItem.path" :index="subItem.path + 'el-sub-menu'">
              <template #title>
                <div style="display: flex; flex-direction: column; align-items: center; line-height: normal">
                  <svg-icon :icon="subItem.meta.icon" class="el-icon" />
                  <span style="margin-top: 3px; font-size: 12px">{{ $t(subItem.meta.title) }}</span>
                </div>
              </template>
              <SubMenu :menu-list="subItem.children" />
            </el-sub-menu>
            <el-menu-item v-else :key="subItem.path + 'el-menu-item'" :index="subItem.path" @click="changeSubMenu(subItem)">
              <div style="display: flex; flex-direction: column; align-items: center; line-height: normal">
                <svg-icon :icon="subItem.meta.icon" class="el-icon" />
                <span style="margin-top: 3px; font-size: 12px">{{ $t(subItem.meta.title) }}</span>
              </div>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
      <div class="other flx-justify-between">
        <GlobalSetting id="globalSetting" v-model="place" />
        <ThemeQuickSwitch id="themeQuickSwitch" v-model="place" />
        <Language id="language" v-model="place" />
        <MoreInfo id="moreInfo" v-model="place" />
      </div>
    </div>
    <el-aside :class="{ 'not-aside': !subMenuList.length }" :style="{ width: subMenuList.length ? '210px' : '0px' }">
      <el-scrollbar>
        <el-menu :router="false" :default-active="activeMenu" :collapse="!subMenuList.length" :unique-opened="accordion" :collapse-transition="false">
          <SubMenu :menu-list="subMenuList" />
        </el-menu>
      </el-scrollbar>
    </el-aside>
    <el-container>
      <el-header>
        <ToolBarLeft />
        <ToolBarDrag />
        <ToolBarRight />
      </el-header>
      <Main />
    </el-container>
  </el-container>
</template>

<script setup lang="ts" name="layoutColumns">
import { ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAuthStore, useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";

import Main from "@/layouts/components/Main/index.vue";
import ToolBarLeft from "@/layouts/components/Header/ToolBarLeft.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import GlobalSetting from "@/layouts/components/Header/components/GlobalSetting.vue";
import ThemeQuickSwitch from "@/layouts/components/Header/components/ThemeQuickSwitch.vue";
import Language from "@/layouts/components/Header/components/Language.vue";
import MoreInfo from "@/layouts/components/Header/components/MoreInfo.vue";
import ToolBarDrag from "@/layouts/components/Header/ToolBarDrag.vue";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const configStore = useConfigStore();
useI18n();
const accordion = true;
const isCollapse = true;
const place: string = "right";
const menuList = computed(() => authStore.showMenuListGet);
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const sysInfo = computed(() => configStore.sysBaseInfoGet);
const subMenuList = ref<Menu.MenuOptions[]>([]);
const splitActive = ref("");
watch(
  () => [menuList, route],
  () => {
    // 当前菜单没有数据直接 return
    if (!menuList.value.length) return;
    splitActive.value = route.path;
    const menuItem = menuList.value.filter((item: Menu.MenuOptions) => {
      const parts = route.path.split("/"); // 将路由按照 '/' 分割成数组
      return route.path === item.path || parts.slice(0, parts.length - 1).join("/") == item.path || `/${parts[1]}` === item.path;
    });
    if (menuItem.length > 0 && menuItem[0].children?.length) return (subMenuList.value = menuItem[0].children);
    subMenuList.value = [];
  },
  {
    deep: true,
    immediate: true
  }
);

// change SubMenu
const changeSubMenu = (item: Menu.MenuOptions) => {
  splitActive.value = item.path;
  if (item.children?.length) return (subMenuList.value = item.children);
  subMenuList.value = [];
  router.push(item.path);
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
