import { IECResult } from "iec-common/dist/data/iecdata";
import IECCONSTANTS from "../data/debug/iecConstants";
import { createWriteStream } from "node:fs";
import { v4 as uuid } from "uuid";
import { IpUtils } from "./ipUtils";
import { logger } from "ee-core/log";
import fs from "fs";
import nodePath from "node:path";
import path from "path";
import Decimal from "decimal.js";
import { t } from "../data/i18n/i18n";

export const getNoConnectResult = (): IECResult<unknown> => {
  const result = new IECResult<unknown>();
  result.code = IECCONSTANTS.CODE_ERROR;
  result.msg = t("errors.deviceNotConnectedOrDisconnected");
  return result;
};

export const writeDataToFile = (
  path: string,
  data: string | Buffer | DataView
): Promise<boolean> => {
  const promise = new Promise<boolean>((resolve) => {
    const stream = createWriteStream(path);
    if (!stream.write(data)) {
      stream.once("drain", () => {
        resolve(true);
      });
    } else {
      resolve(true);
    }
  });

  return promise;
};

export const tabIndent = (str: string, level: number): string => {
  const tab = "  ";
  return tab.repeat(level) + str;
};

export const undefinedToEmpty = (data: unknown) => {
  if (data == undefined) {
    return "";
  }
  if (data instanceof Object) {
    return data.toString();
  }
  return data + "";
};

export const getUUID = () => {
  return uuid();
};

// 去掉最后一个回车
export const removeLastCarriage = (str: string): string => {
  logger.info("removeLastCarriage：", str);
  if (str === "") {
    return str;
  }
  if (str.endsWith("\n")) {
    return str.slice(0, -1);
  }
  return str;
};

// 按照指定步长格式化数字
export const formatByStep = (value, step) => {
  // 处理空值情况
  if (
    value === null ||
    value === undefined ||
    value === "" ||
    step === null ||
    step === undefined ||
    step === ""
  ) {
    return value;
  }

  // 尝试转换为数字
  const numValue = Number(value);
  const numStep = Number(step);

  // 检查是否为有效数字
  if (isNaN(numValue) || isNaN(numStep)) {
    return value;
  }

  // 获取步长的小数位数
  const decimalPlaces = (step.toString().split(".")[1] || "").length;

  // 格式化数字
  return numValue.toFixed(decimalPlaces);
};

// 判断是否相等 10.0 == 10.0000
export const isEquals = (a: any, b: any) => {
  const numA = Number(a);
  const numB = Number(b);
  if (Number.isNaN(numA) || Number.isNaN(numB)) {
    return a == b;
  }
  const tolerance = 1e-10;
  return Math.abs(numA - numB) < tolerance;
};

// 判断是否合法
export const isValid = (
  num: any,
  dataType: string,
  min: any,
  max: any,
  step: number
) => {
  if (dataType === "s") {
    return true;
  }
  let realValue = num.trim();
  if (realValue.length == 0) {
    return false;
  }
  if (dataType === "net") {
    if (!IpUtils.validateIp(realValue)) {
      return false;
    }
    realValue = IpUtils.ipToNumber(realValue);
    console.log(IpUtils.ipToNumber(max));
    min = IpUtils.ipToNumber(min);
    max = IpUtils.ipToNumber(max);
  }
  min = Number(min);
  max = Number(max);
  const value = Number(realValue);
  // 检查是否为有效数值
  if (typeof value !== "number" || isNaN(value)) {
    return false;
  }
  // 处理步长为0的情况
  if (step === 0) {
    return false;
  }
  // 检查数值范围
  if (value < min || value > max) {
    return false;
  }

  const diff = new Decimal(value).minus(min);
  const n = diff.dividedBy(step);
  return n.isInteger() && value >= min && value <= max;
};

export const copyFolder = (sourceDir, targetDir) => {
  // 检查源路径是否存在
  if (!fs.existsSync(sourceDir)) {
    throw new Error(t("fileOperations.sourceFolderNotExists"));
  }

  // 确保目标路径存在
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }

  // 读取源文件夹中的所有文件和子文件夹
  const files = fs.readdirSync(sourceDir);

  files.forEach((file) => {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);

    if (fs.statSync(sourcePath).isFile()) {
      // 复制文件
      fs.copyFileSync(sourcePath, targetPath);
    } else if (fs.statSync(sourcePath).isDirectory()) {
      // 递归复制子文件夹
      copyFolder(sourcePath, targetPath);
    }
  });
};

export const deleteFolderRecursive = (folderPath) => {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const currentPath = nodePath.join(folderPath, file);
      if (fs.lstatSync(currentPath).isDirectory()) {
        // 递归删除子文件夹
        deleteFolderRecursive(currentPath);
      } else {
        // 删除文件
        fs.unlinkSync(currentPath);
      }
    });
    // 删除空文件夹
    fs.rmdirSync(folderPath);
  }
};

/**
 * 递归获取文件或文件夹大小（字节）
 * @param filePath 文件或文件夹路径
 * @returns 文件或文件夹总大小（字节）
 */
export const getFileOrFolderSize = (filePath: string): number => {
  if (!fs.existsSync(filePath)) return 0;
  const stats = fs.statSync(filePath);
  if (stats.isFile()) {
    return stats.size;
  } else if (stats.isDirectory()) {
    let total = 0;
    const files = fs.readdirSync(filePath);
    for (const file of files) {
      const childPath = path.join(filePath, file);
      total += getFileOrFolderSize(childPath);
    }
    return total;
  }
  return 0;
};
