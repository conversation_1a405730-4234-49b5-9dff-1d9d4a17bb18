export default {
  title: "Gestion des pages uniques",
  list: {
    title: "Liste des pages uniques",
    add: "Ajouter une page unique",
    deleteSelected: "Supprimer la sélection",
    deleteConfirm: "Êtes-vous sûr de vouloir supprimer la page unique {title} ?"
  },
  form: {
    title: "{opt} page unique",
    basicSettings: "Paramètres de base",
    functionSettings: "Paramètres de fonction",
    name: "Nom de la page unique",
    type: "Type de page unique",
    icon: "Icône",
    path: "Adresse de route",
    pathPlaceholder: "Veuillez remplir l'adresse de route, ex: /home/<USER>",
    componentName: "Nom du composant",
    componentPath: "Adresse du composant",
    linkPath: "Adresse de lien",
    linkPathPlaceholder: "Veuillez remplir l'adresse de lien, ex: http://www.baidu.com",
    sort: "Tri",
    description: "Description",
    isHome: "Définir comme page d'accueil",
    isHide: "Masquer la page",
    isFull: "Page plein écran",
    isAffix: "Onglet fixe",
    isKeepAlive: "Cache de route",
    cancel: "Annuler",
    confirm: "Confirmer",
    nameRequired: "Veuillez entrer le nom de la page unique",
    typeRequired: "Veuillez sélectionner le type de page unique",
    pathRequired: "Veuillez entrer l'adresse de route",
    componentNameRequired: "Veuillez entrer le nom du composant",
    componentPathRequired: "Veuillez entrer l'adresse du composant",
    sortRequired: "Veuillez entrer le tri",
    iconRequired: "Veuillez sélectionner une icône"
  }
};
