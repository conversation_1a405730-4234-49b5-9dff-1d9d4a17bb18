<template>
  <div class="drag"></div>
</template>

<script setup lang="ts">
// 不需要任何 JavaScript 代码，直接使用 CSS 实现拖拽
</script>

<style lang="scss" scoped>
.drag {
  /* 确保拖拽区域在最上层 */
  z-index: 1;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  padding-left: 10px;
  font-size: 12px;
  font-style: italic;
  font-weight: 300;

  /* 确保拖拽区域可以接收事件 */
  pointer-events: auto;
  cursor: move;
  user-select: none;
  background-color: rgb(0 0 0 / 0%);
  -webkit-app-region: drag;
}
</style>
