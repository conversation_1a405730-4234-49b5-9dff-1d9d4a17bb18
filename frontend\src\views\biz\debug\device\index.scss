.content-box {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  .device-left {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 16%;
    margin-right: 6px;
  }
  .device-right {
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
  }

  // 确保分隔线在装置列表收起时仍有足够宽度显示按钮
  .resize-vertical-no-border {
    position: relative;
    width: 1px;
    min-width: 1px;
    &:hover {
      .collapse-button-container {
        .collapse-icon {
          background-color: var(--el-color-primary-light-8);
          border: 1px solid var(--el-color-primary-light-6);
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
          opacity: 1;
        }
      }
    }
    .collapse-button-container {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 10;
      width: 40px;
      height: 100%;
      pointer-events: auto;
      background: transparent;
      transition: all 0.3s ease;
      transform: translateX(-50%);
      &:hover {
        .collapse-icon {
          background-color: var(--el-color-primary-light-8);
          border: 1px solid var(--el-color-primary-light-6);
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
          opacity: 1;
        }
      }
    }
  }
}
