<template>
  <div class="not-container">
    <img src="@/assets/images/500.png" class="not-img" alt="500" />
    <div class="not-detail">
      <h2>500</h2>
      <h4>{{ t("components.error.serverError") }}</h4>
      <el-button type="primary" @click="router.back">{{ t("components.error.back") }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="500">
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

const router = useRouter();
const { t } = useI18n();
</script>

<style scoped lang="scss">
@import "./index";
</style>
