import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0800",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 14.33,
        y1: 5.33,
        x2: 18.67,
        y2: 0
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(28, 1, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 35.67,
        y1: 2.67,
        x2: 31.67,
        y2: 2.67
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(4, 0.67, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 2.33,
        x2: 4,
        y2: 2.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
