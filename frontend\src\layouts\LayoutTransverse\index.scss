.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-header) {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-header-border-color);
    .logo {
      display: flex;
      align-items: center;
      width: auto; // 改为自适应宽度
      min-width: 250px; // 设置最小宽度
      max-width: 350px; // 设置最大宽度，防止过长
      height: 55px;
      margin-right: 10px;
      margin-left: 10px;
      font-size: 20px;
      white-space: nowrap; // 防止文字换行
      .logo-img {
        width: 32px;
        object-fit: contain;
      }
      .company-name-en {
        font-family: Tahoma;
        font-size: 16px; // 稍微减小字体，避免过长
        font-weight: 600;
        color: #124198;
        white-space: nowrap; // 防止换行
        user-select: none;
      }
      .company-name-cn {
        font-family: "黑体";
        font-size: 16px;
        font-weight: 600;
        color: #124198;
        white-space: nowrap; // 防止换行
        user-select: none;
      }
      .logo-mark {
        top: 1px;
        right: 1px;
        flex-shrink: 0; // 防止压缩
        margin: 0 4px;
        font-family: Tahoma;
        font-size: 14px;
        font-weight: 600;
        color: #124198;
        white-space: nowrap; // 防止换行
        user-select: none;
      }
    }
    .el-menu {
      flex: 1;
      height: 100%;
      overflow: hidden;
      border-bottom: none;
      .el-menu-item {
        .el-icon {
          font-size: 23px;
        }
      }
      .el-sub-menu__icon-arrow {
        display: none;
      }
      .el-sub-menu__hide-arrow {
        width: 65px;
        height: 55px;
      }
      .el-menu-item.is-active {
        color: #ffffff !important;
      }
      .is-active {
        background-color: var(--el-color-primary) !important;
        border-bottom-color: var(--el-color-primary) !important;
        &::before {
          width: 0;
        }
        .el-sub-menu__title {
          color: #ffffff !important;
          background-color: var(--el-color-primary) !important;
          border-bottom-color: var(--el-color-primary) !important;
        }
      }
    }
    .other {
      flex-direction: row;
      gap: 4px;
      width: 200px;
      margin-top: 0;
      margin-left: 10px;
      .iconfont {
        color: var(--el-menu-text-color);
      }
    }
  }

  @media screen and (width <= 730px) {
    .logo {
      display: none !important;
    }
  }
}
