export default {
  search: {
    placeholder: "Buscar dispositivo",
    button: "Buscar",
    success: "Búsqueda exitosa"
  },
  device2: {
    search: {
      placeholder: "Buscar equipo",
      add: "Agregar equipo",
      duplicate: "La IP y el puerto ya existen, no agregue repetidamente"
    },
    list: {
      empty: "No se encontraron dispositivos",
      unnamed: "Dispositivo sin nombre",
      status: {
        connected: "Conectado",
        disconnected: "Desconectado"
      },
      contextMenu: {
        connect: "Conectar",
        edit: "Editar",
        disconnect: "Desconectar",
        remove: "Eliminar"
      },
      message: {
        disconnectFirst: "Por favor, desconecte primero antes de editar",
        disconnectFirstDelete: "Por favor, desconecte primero antes de eliminar",
        connectSuccess: "Dispositivo {name}: conexión exitosa",
        connectExists: "Dispositivo {name}: la conexión ya existe",
        connectFailed: "Dispositivo {name}: error de conexión",
        connectFailedReason: "Error de conexión del dispositivo: {reason}",
        disconnected: "Dispositivo {name}: desconectado",
        operationFailed: "Dispositivo {name}: operación fallida"
      }
    },
    report: {
      group: {
        openWaveConfirm: "¿Abrir el archivo de forma de onda con una herramienta de terceros?",
        tips: "Consejo",
        noWaveTool: "Ruta de la herramienta de forma de onda de terceros no configurada"
      },
      common: {
        selectRow: "Por favor, seleccione la fila a operar"
      }
    },
    backup: {
      savePath: "Ruta de Guardado",
      setPath: "Por favor establezca la ruta de respaldo",
      setPathTitle: "Establecer ruta de respaldo",
      locateFolder: "Localizar Carpeta",
      startBackup: "Iniciar Respaldo",
      cancelBackup: "Cancelar Respaldo",
      backup: "Respaldo",
      sequence: "Secuencia",
      backupType: "Tipo de Respaldo",
      backupDesc: "Descripción del Respaldo",
      progress: "Progreso",
      status: "Estado",
      noTypeSelected: "Por favor seleccione el tipo de respaldo",
      backupSuccess: "Respaldo exitoso",
      backupFailed: "Respaldo fallido",
      openFolderFailed: "Error al abrir carpeta",
      backupTypes: {
        paramValue: "Valores de Parámetros",
        faultInfo: "Información de Fallas",
        cidConfigPrjLog: "Registro de Proyecto de Configuración CID",
        waveReport: "Reporte de Ondas"
      },
      backupDescTypes: {
        paramValue: "Respaldar todos los valores de configuración de parámetros del dispositivo",
        faultInfo: "Respaldar información de registro de fallas del dispositivo",
        cidConfigPrjLog: "Respaldar archivos de configuración CID y registros de proyecto",
        waveReport: "Respaldar archivos de reporte de análisis de forma de onda"
      },
      backupStatus: {
        userCancelled: "Cancelado por Usuario",
        transferring: "Transfiriendo"
      },
      console: {
        pathNotSet: "Ruta de respaldo no establecida, no se puede iniciar el respaldo",
        noTypeSelected: "Ningún tipo de respaldo seleccionado, no se puede iniciar el respaldo",
        startBackup: "Iniciar respaldo, tipos: {types}, ruta: {path}",
        backupException: "Excepción de respaldo: {error}",
        pathSelected: "Ruta de respaldo seleccionada: {path}",
        pathNotSelected: "Ninguna ruta de respaldo seleccionada",
        pathNotSetForLocate: "Ruta de respaldo no establecida, no se puede localizar la carpeta",
        folderOpened: "Carpeta de respaldo abierta: {path}",
        openFolderFailed: "Error al abrir carpeta de respaldo: {error}",
        taskCompleted: "Tarea completada",
        taskCancelled: "Tarea cancelada",
        typeError: "Tipo [{type}] error: {error}",
        typeCompleted: "Tipo [{type}] respaldo completado",
        typeCancelled: "Tipo [{type}] cancelado",
        typeFailed: "Tipo [{type}] fallido"
      }
    },
    remoteControl: {
      directControl: "Control Directo",
      selectControl: "Control Selectivo"
    }
  }
};
