import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0501",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 4, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 1.67,
        y1: 0,
        x2: 1.67,
        y2: 4
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 28, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 1.67,
        y1: 35.67,
        x2: 1.67,
        y2: 31.67
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 3.33,
        y1: 5.67,
        x2: 3.33,
        y2: 29.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
