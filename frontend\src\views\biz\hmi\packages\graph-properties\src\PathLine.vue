<template>
  <el-tabs>
    <el-tab-pane :label="t(`hmi.graphProperties.pathLine.lineSetting`)">
      <el-form :label-width="60">
        <el-collapse>
          <el-collapse-item :title="t(`hmi.graphProperties.pathLine.style`)" name="1">
            <el-form-item :label="t(`hmi.graphProperties.pathLine.lineHeight`)">
              <el-input-number v-model="form.lineHeight" :min="0" @change="onBorder(Attrs.BORDER_WIDTH)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.lineColor`)">
              <color-picker :color="form.lineColor" @color-picker="onBorderColor"></color-picker>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.borderDasharray`)">
              <el-input v-model="form.borderDasharray" @change="onBorder(Attrs.BORDER_DASHARRAY)"></el-input>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :title="t(`hmi.graphProperties.pathLine.position`)">
            <el-form-item :label="t(`hmi.graphProperties.pathLine.width`)">
              <el-input-number v-model="form.width" :min="0" @change="onBorder(Attrs.WIDTH)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.height`)">
              <el-input-number v-model="form.height" :min="0" @change="onBorder(Attrs.HEIGHT)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.x`)">
              <el-input-number v-model="form.x" :min="0" @change="onBorder(Attrs.X)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.y`)">
              <el-input-number v-model="form.y" :min="0" @change="onBorder(Attrs.Y)"></el-input-number>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :title="t(`hmi.graphProperties.pathLine.property`)">
            <el-form-item :label="t(`hmi.graphProperties.pathLine.angle`)">
              <el-input-number v-model="form.angle" :min="0" :max="360" @change="onBorder(Attrs.ANGLE)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.pathLine.zIndex`)">
              <el-input-number v-model="form.zIndex" @change="onBorder(Attrs.ZINDEX)"></el-input-number>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { inject, Ref, ref } from "vue";
import ColorPicker from "./ColorPicker.vue";
import { Attrs, EventTypeParams, EventTypeParamsName } from "../../graph/Graph";
import { getNodeAttrValue, setPathLineWidth } from "../../graph/GraphUtil";
import { Node } from "@antv/x6";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const eventTypeParams = inject(EventTypeParamsName) as Ref<EventTypeParams>;
const node = eventTypeParams.value.eventParam.node as Node;
const nodeAttrValue = getNodeAttrValue(node);
const form = ref(nodeAttrValue);

const onBorderColor = (color: string) => {
  form.value.lineColor = color;
  onBorder(Attrs.BORDER_COLOR);
};

const onBorder = (type: Attrs) => {
  switch (type) {
    case Attrs.BORDER_COLOR:
      node.setAttrByPath("body/stroke", form.value.lineColor);
      break;
    case Attrs.BORDER_WIDTH:
      setPathLineWidth(form.value.lineHeight, node);
      break;
    case Attrs.BORDER_DASHARRAY:
      node.setAttrByPath("body/strokeDasharray", form.value.borderDasharray);
      break;
    case Attrs.WIDTH:
      const size = node.getSize();
      size.width = form.value.width;
      node.setSize(size);
      break;
    case Attrs.HEIGHT:
      const sizeHeight = node.getSize();
      sizeHeight.height = form.value.height;
      node.setSize(sizeHeight);
      break;
    case Attrs.X:
      const position = node.getPosition();
      position.x = form.value.x;
      node.setPosition(position);
      break;
    case Attrs.Y:
      const positionY = node.getPosition();
      positionY.y = form.value.y;
      node.setPosition(positionY);
      break;
    case Attrs.ANGLE:
      node.rotate(form.value.angle, { absolute: true });
      break;
    case Attrs.ZINDEX:
      node.setZIndex(form.value.zIndex);
      break;
    default:
      break;
  }
};
</script>
