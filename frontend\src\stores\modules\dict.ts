import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { sysDictApi, SysDict } from "@/api";
import { ElNotification } from "element-plus";
import { SysDictEnum, CommonStatusEnum } from "@/enums";

const name = "simple-dict"; // 定义模块名称

/** 字典值约束 */
type dictValue = string | SysDictEnum;

/**  DictState */
export interface DictState {
  /** 字典信息 */
  dictInfo: SysDict.DictTree[];
}

/** 字典模块 */
export const useDictStore = defineStore({
  id: name,
  state: (): DictState => ({
    dictInfo: []
  }),
  getters: {
    dictInfoGet: state => state.dictInfo
  },
  actions: {
    /**  设置字典信息 */
    async setDictTree() {
      /**  获取字典信息 */
      const { data } = await sysDictApi.tree();
      if (data) {
        this.dictInfo = data;
      } else {
        ElNotification({
          title: "系统错误",
          message: "获取系统字典信息失败，请联系系统管理员！",
          type: "warning",
          duration: 3000
        });
      }
    },
    /** 字典翻译 */
    dictTranslation(dictValue: dictValue, value: string) {
      const tree = this.dictInfo.find((item: { dictValue: string }) => item.dictValue === dictValue); // 通过字典值找到字典
      //如果没有找到字典，返回无此字典
      if (!tree) {
        return "无此字典";
      }
      // 通过传的值找到字典的子项
      const dict = tree.children.find((item: { dictValue: string }) => item.dictValue === value);
      return dict?.dictLabel || "无此字典";
    },
    /** 获取某个code下字典的列表，多用于字典下拉框 */
    getDictList(dictValue: dictValue, isBool?: boolean) {
      const tree = this.dictInfo.find((item: { dictValue: string }) => item.dictValue === dictValue);
      if (tree) {
        //过滤停用的子项
        tree.children = tree.children.filter((item: { status: CommonStatusEnum }) => item.status === CommonStatusEnum.ENABLE);
        return tree.children.map((item: { [x: string]: any }) => {
          //是和否要特殊处理
          if (isBool) {
            return {
              value: item["dictValue"] === "true" ? true : false,
              label: item["dictLabel"]
            };
          } else {
            return {
              value: item["dictValue"],
              label: item["dictLabel"]
            };
          }
        });
      }
      return [];
    }
  },
  persist: piniaPersistConfig(name)
});
