import DEV_0A00 from "./def/0A00";
import DEV_0B00 from "./def/0B00";
import DEV_0C00 from "./def/0C00";
import DEV_0D00 from "./def/0D00";
import DEV_0E00 from "./def/0E00";
import DEV_0F00 from "./def/0F00";
import DEV_1A00 from "./def/1A00";
import DEV_1B00 from "./def/1B00";
import DEV_1C00 from "./def/1C00";
import DEV_1D00 from "./def/1D00";
import DEV_1E00 from "./def/1E00";
import DEV_1F00 from "./def/1F00";
import DEV_0100 from "./def/0100";
import DEV_0101 from "./def/0101";
import DEV_0200 from "./def/0200";
import DEV_0201 from "./def/0201";
import DEV_0300 from "./def/0300";
import DEV_0301 from "./def/0301";
import DEV_0400 from "./def/0400";
import DEV_0401 from "./def/0401";
import DEV_0500 from "./def/0500";
import DEV_0501 from "./def/0501";
import DEV_0600 from "./def/0600";
import DEV_0601 from "./def/0601";
import DEV_0700 from "./def/0700";
import DEV_0701 from "./def/0701";
import DEV_0800 from "./def/0800";
import DEV_0801 from "./def/0801";
import DEV_0900 from "./def/0900";
import DEV_0901 from "./def/0901";
import DEV_1000 from "./def/1000";
import DEV_1100 from "./def/1100";
import DEV_1200 from "./def/1200";
import DEV_1300 from "./def/1300";
import DEV_1400 from "./def/1400";
import DEV_1500 from "./def/1500";
import DEV_1600 from "./def/1600";
import DEV_1700 from "./def/1700";
import DEV_1800 from "./def/1800";
import DEV_1900 from "./def/1900";

import DEV_3000 from "./def/3000";
import DEV_3200 from "./def/3200";
import DEV_3300 from "./def/3300";
import DEV_3400 from "./def/3400";
import DEV_3401 from "./def/3401";
import DEV_3500 from "./def/3500";
import DEV_3501 from "./def/3501";
import DEV_3600 from "./def/3600";
import DEV_3700 from "./def/3700";
import DEV_3701 from "./def/3701";
import { EquipmentItem, EquipmentStatus } from "../graph";
import { Cell } from "@antv/x6";
type options = {
  data: unknown;
  image: string;
  equipmentStatus?: EquipmentStatus;
};
const toEquipmentItem = (datas: options[]) => {
  const list: EquipmentItem[] = [];
  for (const item of datas) {
    const status = item.equipmentStatus ? item.equipmentStatus : EquipmentStatus.NONE;
    const cell = item.data as Cell;
    list.push({
      data: { value: cell, descendantCells: [] },
      img: item.image,
      equipmentStatus: status
    });
  }
  return list;
};
const DevCfgMap = new Map<string, EquipmentItem[]>([
  [
    "0A00",
    toEquipmentItem([
      {
        data: DEV_0A00,
        image: "create0A00",
        equipmentStatus: EquipmentStatus.NONE
      }
    ])
  ],
  [
    "0B00",
    toEquipmentItem([
      {
        data: DEV_0B00,
        image: "create0B00",
        equipmentStatus: EquipmentStatus.NONE
      }
    ])
  ],
  [
    "0C00",
    toEquipmentItem([
      {
        data: DEV_0C00,
        image: "create0C00",
        equipmentStatus: EquipmentStatus.NONE
      }
    ])
  ],
  [
    "0D00",
    toEquipmentItem([
      {
        data: DEV_0D00,
        image: "create0D00",
        equipmentStatus: EquipmentStatus.NONE
      }
    ])
  ],
  ["0E00", toEquipmentItem([{ data: DEV_0E00, image: "create0E00", equipmentStatus: EquipmentStatus.NONE }])],
  ["0F00", toEquipmentItem([{ data: DEV_0F00, image: "create0F00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1A00", toEquipmentItem([{ data: DEV_1A00, image: "create1A00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1B00", toEquipmentItem([{ data: DEV_1B00, image: "create1B00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1C00", toEquipmentItem([{ data: DEV_1C00, image: "create1C00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1D00", toEquipmentItem([{ data: DEV_1D00, image: "create1D00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1E00", toEquipmentItem([{ data: DEV_1E00, image: "create0E00", equipmentStatus: EquipmentStatus.NONE }])],
  ["1F00", toEquipmentItem([{ data: DEV_1F00, image: "create1F00", equipmentStatus: EquipmentStatus.NONE }])],

  [
    "0100",
    toEquipmentItem([
      { data: DEV_0100, image: "create0100", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0101, image: "create0101", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0200",
    toEquipmentItem([
      { data: DEV_0200, image: "create0200", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0201, image: "create0201", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0300",
    toEquipmentItem([
      { data: DEV_0300, image: "create0300", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0301, image: "create0301", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0400",
    toEquipmentItem([
      { data: DEV_0400, image: "create0400", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0401, image: "create0401", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0500",
    toEquipmentItem([
      { data: DEV_0500, image: "create0500", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0501, image: "create0501", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0600",
    toEquipmentItem([
      { data: DEV_0600, image: "create0600", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0601, image: "create0601", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0700",
    toEquipmentItem([
      { data: DEV_0700, image: "create0700", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0701, image: "create0701", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "0800",
    toEquipmentItem([
      { data: DEV_0800, image: "create0800", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0801, image: "create0801", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],

  [
    "0900",
    toEquipmentItem([
      { data: DEV_0900, image: "create0900", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_0901, image: "create0901", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],

  ["1000", toEquipmentItem([{ data: DEV_1000, image: "create1000" }])],
  ["1100", toEquipmentItem([{ data: DEV_1100, image: "create1100" }])],
  ["1200", toEquipmentItem([{ data: DEV_1200, image: "create1200" }])],
  ["1300", toEquipmentItem([{ data: DEV_1300, image: "create1300" }])],
  ["1400", toEquipmentItem([{ data: DEV_1400, image: "create1400" }])],
  ["1500", toEquipmentItem([{ data: DEV_1500, image: "create1500" }])],
  ["1600", toEquipmentItem([{ data: DEV_1600, image: "create1600" }])],
  ["1700", toEquipmentItem([{ data: DEV_1700, image: "create1700" }])],
  ["1800", toEquipmentItem([{ data: DEV_1800, image: "create1800" }])],
  ["1900", toEquipmentItem([{ data: DEV_1900, image: "create1900" }])],

  ["3000", toEquipmentItem([{ data: DEV_3000, image: "create3000" }])],
  ["3200", toEquipmentItem([{ data: DEV_3200, image: "create3200" }])],
  ["3300", toEquipmentItem([{ data: DEV_3300, image: "create3300" }])],
  [
    "3400",
    toEquipmentItem([
      { data: DEV_3400, image: "create3400", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_3401, image: "create3401", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  [
    "3500",
    toEquipmentItem([
      { data: DEV_3500, image: "create3500", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_3501, image: "create3501", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ],
  ["3600", toEquipmentItem([{ data: DEV_3600, image: "create3600" }])],
  [
    "3700",
    toEquipmentItem([
      { data: DEV_3700, image: "create3700", equipmentStatus: EquipmentStatus.OPEN },
      { data: DEV_3701, image: "create3701", equipmentStatus: EquipmentStatus.CLOSE }
    ])
  ]
]);

export { DevCfgMap };
