import { ReqId, ResPage, SysPosition } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/organization/position/");

const sysPositionApi = {
  /** 获取职位分页 */
  page(params: SysPosition.Page) {
    return http.get<ResPage<SysPosition.SysPositionInfo>>("page", params);
  },
  /** 获取职位树 */
  tree() {
    return http.get<SysPosition.SysPositionTree[]>("tree", {}, { loading: false });
  },
  /** 获取职位详情 */
  detail(params: ReqId) {
    return http.get<SysPosition.SysPositionInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除职位 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 职位选择器 */
  selector() {
    return http.get<SysPosition.SysPositionSelector[]>("selector", {}, { loading: false });
  }
};

export { sysPositionApi };
