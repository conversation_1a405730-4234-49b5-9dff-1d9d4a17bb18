/**
 * @description  全局指令注册
 * @license Apache License Version 2.0
 */
import { App, Directive } from "vue";
import auth from "./modules/auth";

const directivesList: { [key: string]: Directive } = {
  auth
};

const directives = {
  install: function (app: App<Element>) {
    Object.keys(directivesList).forEach(key => {
      app.directive(key, directivesList[key]);
    });
  }
};

export default directives;
