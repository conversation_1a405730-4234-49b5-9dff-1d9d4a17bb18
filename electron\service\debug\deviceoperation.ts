import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
/**
 * 装置操作相关
 * <AUTHOR>
 * @class
 */
class DeviceOperationService {
  /**
   * 重启装置
   * @param req
   * @returns
   */
  async rebootDevice(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceOperationService] ${t("logs.deviceOperationService.rebootDeviceEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.rebootDeviceStart")}`,
        req
      );
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const response = await client?.deviceReboot();
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.rebootDeviceReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return true;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceOperationService] ${t("logs.deviceOperationService.rebootDeviceError")}:`,
        error
      );
      throw error;
    }
  }
  /**
   * 清除报告
   * @param req
   * @returns
   */
  async clearReport(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceOperationService] ${t("logs.deviceOperationService.clearReportEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearReportStart")}`,
        req
      );
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const response = await client?.clearReport(["all"]);
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearReportReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return true;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearReportError")}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 装置复归
   * @param req
   * @returns
   */
  async resetDevice(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceOperationService] ${t("logs.deviceOperationService.resetDeviceEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.resetDeviceStart")}`,
        req
      );
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const response = await client?.deviceReset();
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.resetDeviceReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return true;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceOperationService] ${t("logs.deviceOperationService.resetDeviceError")}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 清除录波
   * @param req
   * @returns
   */
  async clearWave(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[DeviceOperationService] ${t("logs.deviceOperationService.clearWaveEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearWaveStart")}`,
        req
      );
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const response = await client?.clearWave();
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearWaveReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return true;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceOperationService] ${t("logs.deviceOperationService.clearWaveError")}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 手动录波
   * @param req
   * @returns
   */
  async manualWave(req: IECReq<boolean>): Promise<boolean> {
    logger.info(
      `[DeviceOperationService] ${t("logs.deviceOperationService.manualWaveEntry")}:`,
      JSON.stringify(req)
    );
    try {
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.manualWaveStart")}`,
        req
      );
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const response = await client?.mannualWave();
      logger.info(
        `[DeviceOperationService] ${t("logs.deviceOperationService.manualWaveReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return true;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DeviceOperationService] ${t("logs.deviceOperationService.manualWaveError")}:`,
        error
      );
      throw error;
    }
  }
}

DeviceOperationService.toString = () => "[class DeviceOperationService]";
const deviceOperationService = new DeviceOperationService();

export { DeviceOperationService, deviceOperationService };
