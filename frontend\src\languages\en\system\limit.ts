export default {
  limit: {
    module: {
      title: "Module Name",
      icon: "Icon",
      status: "Status",
      sort: "Sort",
      description: "Description",
      createTime: "Created Time",
      operation: "Operation",
      add: "Add Module",
      edit: "Edit Module",
      delete: "Delete Module",
      deleteConfirm: "Delete selected modules",
      deleteConfirmWithName: "Delete module [{name}]",
      form: {
        title: "Please enter module name",
        status: "Please select status",
        sort: "Please enter sort order",
        icon: "Please select icon"
      }
    },
    menu: {
      title: "Menu Name",
      icon: "Menu Icon",
      type: "Menu Type",
      component: "Component Name",
      path: "Route Path",
      componentPath: "Component Path",
      sort: "Sort",
      status: "Status",
      description: "Description",
      operation: "Operation",
      add: "Add Menu",
      edit: "Edit Menu",
      delete: "Delete Menu",
      deleteConfirm: "Delete selected menus",
      deleteConfirmWithName: "Delete menu [{name}]",
      form: {
        title: "Please enter menu name",
        parent: "Please select parent menu",
        type: "Please select menu type",
        path: "Please enter route path",
        component: "Please enter component path",
        sort: "Please enter sort order",
        icon: "Please select icon",
        status: "Please select status",
        link: "Please enter link address"
      }
    },
    button: {
      title: "Button Name",
      code: "Button Code",
      sort: "Sort",
      description: "Description",
      operation: "Operation",
      add: "Add Button",
      edit: "Edit Button",
      delete: "Delete Button",
      deleteConfirm: "Delete selected buttons",
      deleteConfirmWithName: "Delete button [{name}]",
      batch: {
        title: "Batch Add Buttons",
        shortName: "Permission Short Name",
        codePrefix: "Code Prefix",
        form: {
          shortName: "Please enter permission short name",
          codePrefix: "Please enter code prefix"
        }
      },
      form: {
        title: "Please enter button name",
        code: "Please enter button code",
        sort: "Please enter sort order"
      }
    },
    role: {
      title: "Role Name",
      org: "Organization",
      category: "Role Type",
      status: "Status",
      sort: "Sort",
      description: "Description",
      createTime: "Created Time",
      operation: "Operation",
      add: "Add Role",
      edit: "Edit Role",
      delete: "Delete Role",
      deleteConfirm: "Delete selected roles",
      deleteConfirmWithName: "Delete role [{name}]",
      grant: {
        resource: "Grant Resource",
        permission: "Grant Permission",
        dataScope: "Data Scope"
      },
      form: {
        title: "Please enter role name",
        org: "Please select organization",
        category: "Please select role type",
        status: "Please select status"
      }
    },
    spa: {
      title: "SPA Name",
      icon: "Icon",
      type: "SPA Type",
      path: "Route Path",
      component: "Component Path",
      sort: "Sort",
      description: "Description",
      createTime: "Created Time",
      operation: "Operation",
      add: "Add SPA",
      edit: "Edit SPA",
      delete: "Delete SPA",
      deleteConfirm: "Delete selected SPAs",
      deleteConfirmWithName: "Delete SPA [{name}]",
      form: {
        title: "Please enter SPA name",
        type: "Please select SPA type",
        path: "Please enter route path",
        component: "Please enter component path",
        sort: "Please enter sort order",
        icon: "Please select icon",
        link: "Please enter link address, e.g.: http://www.baidu.com"
      }
    }
  }
};
