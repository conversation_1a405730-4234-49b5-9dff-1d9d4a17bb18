// vite.config.ts
import { defineConfig, loadEnv } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite/dist/node/index.js";
import { resolve as resolve2 } from "path";

// build/getEnv.ts
function wrapperEnv(envConf) {
  const ret = {};
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");
    realName = realName === "true" ? true : realName === "false" ? false : realName;
    if (envName === "VITE_PORT") realName = Number(realName);
    if (envName === "VITE_PROXY") {
      try {
        realName = JSON.parse(realName);
      } catch (error) {
      }
    }
    ret[envName] = realName;
  }
  return ret;
}

// build/proxy.ts
function createProxy(list = []) {
  const ret = {};
  for (const [prefix, target] of list) {
    const httpsRE = /^https:\/\//;
    const isHttps = httpsRE.test(target);
    ret[prefix] = {
      target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${prefix}`), ""),
      // https is require secure=false
      ...isHttps ? { secure: false } : {}
    };
  }
  return ret;
}

// build/plugins.ts
import { resolve } from "path";
import { VitePWA } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-pwa/dist/index.js";
import { visualizer } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { createSvgIconsPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import { createHtmlPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-html/dist/index.mjs";
import vue from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import eslintPlugin from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-eslint/dist/index.mjs";
import viteCompression from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-compression/dist/index.mjs";
import vueSetupExtend from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
import UnoCSS from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import Components from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/vite.mjs";
import Icons from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/resolver.js";
import NextDevTools from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import { codeInspectorPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/code-inspector-plugin/dist/index.mjs";
var createVitePlugins = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE, VITE_REPORT, VITE_DEVTOOLS, VITE_PWA, VITE_CODEINSPECTOR } = viteEnv;
  return [
    vue(),
    // devTools
    VITE_DEVTOOLS && NextDevTools({ launchEditor: "code" }),
    // vue 可以使用 jsx/tsx 语法
    vueJsx(),
    // esLint 报错信息显示在浏览器界面上
    eslintPlugin(),
    // name 可以写在 script 标签上
    vueSetupExtend({}),
    // 创建打包压缩配置
    createCompression(viteEnv),
    // 注入变量到 html 文件
    createHtmlPlugin({
      minify: true,
      inject: {
        data: { title: VITE_GLOB_APP_TITLE }
      }
    }),
    // 使用 svg 图标
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), "src/assets/svg")],
      symbolId: "local-[dir]-[name]"
    }),
    // vitePWA
    VITE_PWA && createVitePwa(viteEnv),
    // 是否生成包预览，分析依赖包大小做优化处理
    VITE_REPORT && visualizer({ filename: "stats.html", gzipSize: true, brotliSize: true }),
    // 自动 IDE 并将光标定位到 DOM 对应的源代码位置。see: https://inspector.fe-dev.cn/guide/start.html
    VITE_CODEINSPECTOR && codeInspectorPlugin({
      bundler: "vite"
    }),
    // 自动导入组件
    AutoImport({
      imports: ["vue", "vue-router"],
      // Auto import functions from Element Plus, e.g. ElMessage, ElMessageBox... (without style)
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (不带样式，因为已在main.ts中导入完整样式)
      resolvers: [
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false
        }),
        // Auto import icon components
        // 自动导入图标组件
        IconsResolver({
          prefix: "Icon"
        })
      ],
      dts: "src/auto-import.d.ts"
      // 路径下自动生成文件夹存放全局指令
    }),
    Components({
      dirs: ["src/components"],
      // 配置需要默认导入的自定义组件文件夹，该文件夹下的所有组件都会自动 import
      resolvers: [
        // Auto register icon components
        // 自动注册图标组件
        IconsResolver({
          enabledCollections: ["ep"]
          // element-plus 图标库
        }),
        // Auto register Element Plus components
        // 自动导入 Element Plus 组件（不带样式，因为已在main.ts中导入完整样式）
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false
        })
      ]
    }),
    Icons({
      compiler: "vue3",
      autoInstall: true
    }),
    UnoCSS()
    // UnoCSS
  ];
};
var createCompression = (viteEnv) => {
  const { VITE_BUILD_COMPRESS = "none", VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE } = viteEnv;
  const compressList = VITE_BUILD_COMPRESS.split(",");
  const plugins = [];
  if (compressList.includes("gzip")) {
    plugins.push(
      viteCompression({
        ext: ".gz",
        algorithm: "gzip",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  if (compressList.includes("brotli")) {
    plugins.push(
      viteCompression({
        ext: ".br",
        algorithm: "brotliCompress",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  return plugins;
};
var createVitePwa = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE } = viteEnv;
  return VitePWA({
    registerType: "autoUpdate",
    workbox: {
      // 添加此项配置，增加需要缓存的最大文件大小
      maximumFileSizeToCacheInBytes: 6 * 1024 * 1024
    },
    manifest: {
      name: VITE_GLOB_APP_TITLE,
      short_name: VITE_GLOB_APP_TITLE,
      theme_color: "#ffffff",
      icons: [
        {
          src: "/logo.png",
          sizes: "192x192",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "any maskable"
        }
      ]
    }
  });
};

// vite.config.ts
import { visualizer as visualizer2 } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";

// package.json
var package_default = {
  name: "VisualDebug",
  private: true,
  type: "module",
  description: "\u53EF\u89C6\u5316\u5E73\u53F0\u5DE5\u7A0B\u8C03\u8BD5\u5DE5\u5177",
  license: "MIT",
  scripts: {
    dev: "vite --host --port 8080",
    "dev:force": "vite --host --port 8080 --force",
    build: "node ./node_modules/vite/bin/vite.js build",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:test": "vue-tsc && vite build --mode test",
    "build:pro": "vue-tsc && vite build --mode production",
    "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html",
    "type:check": "vue-tsc --noEmit --skipLibCheck",
    preview: "pnpm run build:dev && vite preview",
    "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src",
    "lint:prettier": 'prettier --write "src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}"',
    "lint:stylelint": 'stylelint --cache --fix "**/*.{vue,less,postcss,css,scss}" --cache --cache-location node_modules/.cache/stylelint/',
    "lint:lint-staged": "lint-staged",
    "cache:clear": "node scripts/clear-cache.js",
    "cache:clear:dev": "node scripts/clear-cache.js --dev",
    "cache:clear:build": "node scripts/clear-cache.js --build",
    "cache:clear:all": "node scripts/clear-cache.js --all",
    "deps:update": "npm update && npm audit fix",
    "deps:check": "npm outdated",
    "perf:monitor": "node scripts/performance-monitor.js",
    "perf:cache": "node scripts/performance-monitor.js --cache",
    "perf:build": "node scripts/performance-monitor.js --build",
    prepare: "husky install",
    release: "standard-version",
    commit: "git add -A && czg && git push"
  },
  dependencies: {
    "@antv/g2plot": "^2.4.32",
    "@antv/x6": "^2.18.1",
    "@antv/x6-plugin-clipboard": "2.1.6",
    "@antv/x6-plugin-dnd": "2.1.1",
    "@antv/x6-plugin-export": "^2.1.6",
    "@antv/x6-plugin-history": "2.2.4",
    "@antv/x6-plugin-keyboard": "2.2.3",
    "@antv/x6-plugin-scroller": "2.0.10",
    "@antv/x6-plugin-selection": "2.2.2",
    "@antv/x6-plugin-snapline": "2.1.7",
    "@antv/x6-plugin-transform": "^2.1.8",
    "@element-plus/icons-vue": "^2.3.1",
    "@highlightjs/vue-plugin": "^2.1.0",
    "@iconify/vue": "^4.1.2",
    "@vueuse/core": "^11.0.3",
    axios: "^1.7.7",
    "crypto-js": "^4.1.1",
    dayjs: "^1.11.13",
    "decimal.js": "^10.5.0",
    "default-passive-events": "^2.0.0",
    echarts: "^5.5.1",
    "element-plus": "^2.5.6",
    entities: "^4.5.0",
    "highlight.js": "^11.10.0",
    "markdown-it": "^14.1.0",
    md5: "^2.3.0",
    mitt: "^3.0.1",
    nprogress: "^0.2.0",
    pinia: "^2.2.2",
    "pinia-plugin-persistedstate": "^3.2.1",
    "print-js": "^1.6.0",
    qs: "^6.13.0",
    sortablejs: "^1.15.3",
    "split.js": "^1.6.5",
    "sprintf-js": "^1.1.3",
    "v-contextmenu": "^3.2.0",
    vue: "^3.5.5",
    "vue-cropper": "^1.1.1",
    "vue-i18n": "^9.13.1",
    "vue-router": "^4.4.5"
  },
  devDependencies: {
    "@commitlint/cli": "^19.5.0",
    "@commitlint/config-conventional": "^19.5.0",
    "@iconify/json": "^2.2.247",
    "@types/markdown-it": "^14.1.2",
    "@types/md5": "^2.3.5",
    "@types/nprogress": "^0.2.3",
    "@types/qs": "^6.9.15",
    "@types/sm-crypto": "^0.3.4",
    "@types/sortablejs": "^1.15.8",
    "@types/uuid": "^10.0.0",
    "@typescript-eslint/eslint-plugin": "^7.14.1",
    "@typescript-eslint/parser": "^7.14.1",
    "@vitejs/plugin-vue": "^5.1.3",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    autoprefixer: "^10.4.20",
    "code-inspector-plugin": "^0.16.1",
    "cz-git": "^1.9.4",
    czg: "^1.9.4",
    eslint: "^8.57.0",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.1.3",
    "eslint-plugin-vue": "^9.26.0",
    "hotkeys-js": "3.13.7",
    husky: "^9.0.11",
    "lint-staged": "^15.2.10",
    "naive-ui": "^2.39.0",
    postcss: "^8.4.45",
    "postcss-html": "^1.7.0",
    prettier: "^3.3.3",
    "rollup-plugin-visualizer": "^5.14.0",
    sass: "1.74.1",
    "sm-crypto": "^0.3.13",
    "standard-version": "^9.5.0",
    stylelint: "^16.9.0",
    "stylelint-config-html": "^1.1.0",
    "stylelint-config-recess-order": "^5.1.0",
    "stylelint-config-recommended-scss": "^14.1.0",
    "stylelint-config-recommended-vue": "^1.5.0",
    "stylelint-config-standard": "^36.0.1",
    "stylelint-config-standard-scss": "^13.1.0",
    typescript: "~5.4.0",
    unocss: "^0.62.3",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-icons": "^0.19.3",
    "unplugin-vue-components": "^0.25.2",
    "unplugin-vue-setup-extend-plus": "^1.0.1",
    uuid: "^8.3.2",
    vite: "^5.4.5",
    "vite-plugin-compression": "^0.5.1",
    "vite-plugin-eslint": "^1.8.1",
    "vite-plugin-html": "^3.2.2",
    "vite-plugin-pwa": "^0.20.5",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.3.5",
    "vue-tsc": "^2.1.6"
  },
  overrides: {},
  engines: {
    node: ">=16.0.0"
  },
  browserslist: {
    production: [
      "> 1%",
      "not dead",
      "not op_mini all"
    ],
    development: [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite.config.ts
import dayjs from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/dayjs/dayjs.min.js";

// build/element-plus-config.ts
var elementPlusCoreIncludes = [
  // Element Plus 核心库
  "element-plus",
  "element-plus/es",
  "@element-plus/icons-vue"
];
var elementPlusStylePaths = [
  // 表单组件
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/slider/style/css",
  "element-plus/es/components/rate/style/css",
  "element-plus/es/components/color-picker/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/time-picker/style/css",
  "element-plus/es/components/upload/style/css",
  // 数据展示组件
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/progress/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/card/style/css",
  "element-plus/es/components/collapse/style/css",
  "element-plus/es/components/timeline/style/css",
  "element-plus/es/components/divider/style/css",
  "element-plus/es/components/image/style/css",
  "element-plus/es/components/calendar/style/css",
  // 导航组件
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/tabs/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/page-header/style/css",
  "element-plus/es/components/steps/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  // 反馈组件
  "element-plus/es/components/alert/style/css",
  "element-plus/es/components/loading/style/css",
  "element-plus/es/components/message/style/css",
  "element-plus/es/components/message-box/style/css",
  "element-plus/es/components/notification/style/css",
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/popover/style/css",
  "element-plus/es/components/popconfirm/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/result/style/css",
  // 布局组件
  "element-plus/es/components/container/style/css",
  "element-plus/es/components/header/style/css",
  "element-plus/es/components/aside/style/css",
  "element-plus/es/components/main/style/css",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/space/style/css",
  // 其他组件
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/button-group/style/css",
  "element-plus/es/components/link/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/scrollbar/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/avatar/style/css",
  "element-plus/es/components/empty/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/skeleton-item/style/css",
  "element-plus/es/components/affix/style/css",
  "element-plus/es/components/anchor/style/css",
  "element-plus/es/components/anchor-link/style/css"
];
function getElementPlusIncludes(useStyleIncludes = false) {
  if (useStyleIncludes) {
    return [...elementPlusCoreIncludes, ...elementPlusStylePaths];
  }
  return elementPlusCoreIncludes;
}
function isElementPlusModule(id) {
  return id.includes("element-plus") || id.includes("@element-plus");
}
function getElementPlusChunkName(id) {
  if (id.includes("@element-plus/icons-vue")) {
    return "element-icons";
  }
  if (id.includes("element-plus/es/components")) {
    return "element-components";
  }
  if (id.includes("element-plus")) {
    return "element-core";
  }
  return "element-vendor";
}

// vite.config.ts
var __vite_injected_original_dirname = "E:\\\u5DE5\u5177\u8F6F\u4EF6\\visualdebug\\frontend";
var { dependencies, devDependencies, name } = package_default;
var __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};
var vite_config_default = defineConfig(({ mode }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve2(__vite_injected_original_dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve2("node_modules/async-validator/dist-node/index.js"),
        mousetrap: "mousetrap/mousetrap.js"
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler"
          // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心框架依赖 - 高优先级预构建
        "vue",
        "vue-router",
        "pinia",
        "pinia-plugin-persistedstate",
        // UI组件库 - 使用配置文件管理
        ...getElementPlusIncludes(false),
        // false表示不包含样式路径，因为已导入完整样式
        // 工具库 - 稳定依赖预构建
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash-es",
        "@vueuse/core",
        "mitt",
        "nprogress",
        "qs",
        // 加密和工具
        "crypto-js",
        "md5",
        // 国际化
        "vue-i18n",
        // 数学计算
        "decimal.js",
        // 打印功能
        "print-js",
        // 拖拽排序
        "sortablejs",
        // 分割面板
        "split.js",
        // 字符串格式化
        "sprintf-js",
        // 键盘快捷键库
        "mousetrap"
      ],
      // 排除大型库和动态导入的依赖
      exclude: [
        // 图标库 - 按需加载
        "@iconify/json",
        "@iconify/vue",
        // 图表库 - 延迟加载
        "echarts",
        "@antv/g2plot",
        "@antv/x6",
        "@antv/x6-plugin-clipboard",
        "@antv/x6-plugin-dnd",
        "@antv/x6-plugin-export",
        "@antv/x6-plugin-history",
        "@antv/x6-plugin-keyboard",
        "@antv/x6-plugin-scroller",
        "@antv/x6-plugin-selection",
        "@antv/x6-plugin-snapline",
        "@antv/x6-plugin-transform",
        // 代码高亮 - 按需加载
        "highlight.js",
        "@highlightjs/vue-plugin",
        // Markdown处理 - 按需加载
        "markdown-it",
        // 图片裁剪 - 按需加载
        "vue-cropper",
        // 右键菜单 - 按需加载
        "v-contextmenu",
        // 实体编码 - 小型库
        "entities"
      ],
      // 开发环境强制重新预构建（生产环境设为false）
      force: process.env.NODE_ENV === "development" ? false : false,
      // 预构建入口
      entries: ["src/main.ts", "src/App.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // 开发服务器缓存配置
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },
      // 预热常用文件，提升开发体验
      warmup: {
        clientFiles: ["src/main.ts", "src/App.vue", "src/layouts/index.vue", "src/routers/index.ts", "src/stores/index.ts"]
      },
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // Element Plus 配置已在 build/plugins.ts 中处理
      visualizer2({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      }),
      // 过滤 sourcemap 警告的插件
      {
        name: "suppress-sourcemap-warnings",
        buildStart() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        },
        configureServer() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        }
      }
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2e3,
      // 构建缓存配置
      emptyOutDir: true,
      // 静态资源处理
      assetsInlineLimit: 4096,
      // 小于4kb的资源内联为base64
      rollupOptions: {
        // 忽略有问题的 sourcemap 警告
        onwarn(warning, warn) {
          if (warning.code === "SOURCEMAP_ERROR") return;
          if (warning.message?.includes("entities/lib/esm") && warning.message?.includes("points to missing source files")) {
            return;
          }
          warn(warning);
        },
        // 缓存优化
        cache: true,
        // 外部依赖（如果需要CDN加载）
        external: [],
        output: {
          // 优化代码分割策略 - 基于缓存友好的文件名
          chunkFileNames: (chunkInfo) => {
            if (chunkInfo.name?.includes("vendor")) {
              return "assets/vendor/[name]-[hash].js";
            }
            if (chunkInfo.name?.includes("async")) {
              return "assets/async/[name]-[hash].js";
            }
            return "assets/chunks/[name]-[hash].js";
          },
          entryFileNames: "assets/entry/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const extType = assetInfo.name?.split(".").pop() || "";
            if (["png", "jpg", "jpeg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/images/[name]-[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/fonts/[name]-[hash].[ext]";
            }
            if (["css"].includes(extType)) {
              return "assets/styles/[name]-[hash].[ext]";
            }
            return "assets/[ext]/[name]-[hash].[ext]";
          },
          // 手动分割代码块，优化缓存策略
          manualChunks: (id) => {
            if (id.includes("vue") && !id.includes("node_modules")) {
              return "vue-vendor";
            }
            if (id.includes("vue-router")) {
              return "vue-vendor";
            }
            if (id.includes("pinia")) {
              return "vue-vendor";
            }
            if (isElementPlusModule(id)) {
              return getElementPlusChunkName(id);
            }
            if (id.includes("@iconify")) {
              return "icons-vendor";
            }
            if (id.includes("axios") || id.includes("dayjs") || id.includes("lodash")) {
              return "utils-vendor";
            }
            if (id.includes("crypto-js") || id.includes("md5") || id.includes("qs")) {
              return "utils-vendor";
            }
            if (id.includes("echarts") || id.includes("@antv")) {
              return "charts-vendor";
            }
            if (id.includes("highlight.js") || id.includes("@highlightjs")) {
              return "highlight-vendor";
            }
            if (id.includes("vue-i18n")) {
              return "i18n-vendor";
            }
            if (id.includes("node_modules")) {
              return "vendor";
            }
            if (id.includes("src/views/")) {
              const match = id.match(/src\/views\/([^\/]+)/);
              if (match) {
                return `views-${match[1]}`;
              }
            }
            if (id.includes("src/components/")) {
              return "components";
            }
            if (id.includes("src/utils/") || id.includes("src/hooks/")) {
              return "utils";
            }
          }
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
