import { Graph, Node } from "@antv/x6";
/**
 * 快捷键
 * <AUTHOR>
 * @version 1.0 2025-03-08
 */
class GraphKeyBoard {
  graph: Graph;
  constructor(graph: Graph) {
    this.graph = graph;
    this.bindEvent(graph);
  }
  bindEvent(graph: Graph) {
    // 绑定复制，粘贴
    graph.bindKey("ctrl+c", () => {
      this.copy();
    });
    graph.bindKey("ctrl+v", () => {
      this.paste();
    });
    graph.bindKey("delete", () => {
      this.delete();
    });
    graph.bindKey("up", () => {
      this.moveUp(1);
    });
    graph.bindKey("right", () => {
      this.moveRight(1);
    });
    graph.bindKey("down", () => {
      this.moveDown(1);
    });
    graph.bindKey("left", () => {
      this.moveLeft(1);
    });
  }
  copy() {
    const cells = this.graph.getSelectedCells();
    if (cells.length > 0) {
      this.graph.copy(cells);
    }
    return false;
  }
  paste() {
    if (!this.graph.isClipboardEmpty()) {
      this.graph.paste({ offset: 32 });
    }
  }
  delete() {
    const cells = this.graph.getSelectedCells();
    if (cells.length > 0) {
      this.graph.removeCells(cells);
    }
  }
  moveUp(dis: number) {
    const cells = this.graph.getSelectedCells();
    let node;
    let pos;
    for (const cell of cells) {
      node = cell as Node;
      pos = node.getPosition();
      node.setPosition({ x: pos.x, y: pos.y - dis });
    }
  }
  moveRight(dis: number) {
    const cells = this.graph.getSelectedCells();
    let node;
    let pos;
    for (const cell of cells) {
      node = cell as Node;
      pos = node.getPosition();
      node.setPosition({ x: pos.x + dis, y: pos.y });
    }
  }
  moveDown(dis: number) {
    const cells = this.graph.getSelectedCells();
    let node;
    let pos;
    for (const cell of cells) {
      node = cell as Node;
      pos = node.getPosition();
      node.setPosition({ x: pos.x, y: pos.y + dis });
    }
  }
  moveLeft(dis: number) {
    const cells = this.graph.getSelectedCells();
    let node;
    let pos;
    for (const cell of cells) {
      node = cell as Node;
      pos = node.getPosition();
      node.setPosition({ x: pos.x - dis, y: pos.y });
    }
  }
}
export default GraphKeyBoard;
