<template>
  <!--
  <el-row>
    <el-col :span="24">
      <el-menu mode="horizontal">
        <el-menu-item @click="onToolClick('undo')">撤销</el-menu-item>
        <el-menu-item @click="onToolClick('redo')">重做</el-menu-item>
        <el-menu-item @click="onToolClick('front')">置前</el-menu-item>
        <el-menu-item @click="onToolClick('back')">置后</el-menu-item>
        <el-menu-item @click="onEquipmentList">图符管理</el-menu-item>
      </el-menu>
    </el-col>
  </el-row>
-->
  <div class="toolbar">
    <div :title="t('graphDefine.graphTools.undo')" class="toolbar-item" @click="onToolClick('undo')">
      <hmi-icon-undo :width="32" :height="32" />
    </div>
    <div :title="t('graphDefine.graphTools.redo')" class="toolbar-item" @click="onToolClick('redo')">
      <hmi-icon-redo :width="32" :height="28" />
    </div>
    <el-divider direction="vertical"></el-divider>
    <div :title="t('graphDefine.graphTools.front')" class="toolbar-item" @click="onToolClick('front')">
      <hmi-icon-front :width="32" :height="18" />
    </div>
    <div :title="t('graphDefine.graphTools.back')" class="toolbar-item" @click="onToolClick('back')">
      <hmi-icon-back :width="32" :height="18" />
    </div>
    <el-divider direction="vertical"></el-divider>
    <div :title="t('graphDefine.graphTools.equipmentList')" class="toolbar-item" @click="onEquipmentList">
      <hmi-icon-figure-list :width="32" :height="20" />
    </div>
  </div>
</template>
<script setup lang="ts">
import HmiIconUndo from "../../graph-icons/undo.vue";
import HmiIconRedo from "../../graph-icons/redo.vue";
import HmiIconFront from "../../graph-icons/front.vue";
import HmiIconBack from "../../graph-icons/back.vue";
import HmiIconFigureList from "../../graph-icons/figure-list.vue";
import { Graph } from "@antv/x6";
import GraphOperator from "../../../graph/GraphOperator";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const graphOperator = new GraphOperator();
const prop = defineProps({
  graph: { type: Graph, required: true }
});
// 定义事件
const emit = defineEmits<{
  (e: "equipmentList"): void;
}>();
const graph = prop.graph;

const onToolClick = (type: string) => {
  switch (type) {
    case "undo":
      graph.undo();
      break;
    case "redo":
      graph.redo();
      break;
    case "front":
      graphOperator.toFront(graph);
      break;
    case "back":
      graphOperator.toBack(graph);
      break;
    default:
      break;
  }
};
// 获取自定义图符
const onEquipmentList = () => {
  emit("equipmentList");
};
</script>
