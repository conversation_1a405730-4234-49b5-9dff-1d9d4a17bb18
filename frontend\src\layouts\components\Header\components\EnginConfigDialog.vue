<template>
  <div class="engin-main">
    <el-dialog v-model="dialogVisible" width="45%" :title="title" draggable>
      <div class="engin-root">
        <div class="engin-type">
          {{ t("layout.header.enginConfig.configType") }}：
          <el-select v-model="configType" style="width: 280px">
            <el-option v-for="item in configTypes" :key="item.value" :label="item.label" :value="item.value">
              <template #default>
                <svg-icon v-if="item.type === 'all'" icon="ep:menu" style="margin-right: 6px; font-size: 16px" />
                <svg-icon v-else-if="item.type === 'device'" icon="ep:cpu" style="margin-right: 6px; font-size: 16px" />
                <svg-icon v-else-if="item.type === 'configure'" icon="ep:list" style="margin-right: 6px; font-size: 16px" />
                <span>{{ item.label }}</span>
              </template>
            </el-option>
          </el-select>
        </div>

        <div class="content-box mb10">
          <el-input v-model="filePath" :placeholder="placeholder" :readonly="true">
            <template #append>
              <el-tooltip
                :content="t('layout.header.enginConfig.openDirectory')"
                popper-class="is-small"
                transition="none"
                effect="light"
                placement="right-start"
                :show-after="0"
                :hide-after="0"
              >
                <el-button :icon="Folder" @click="openDirectory" />
              </el-tooltip>
            </template>
          </el-input>
        </div>
        <div class="engin-button">
          <el-button @click="cancel">{{ t("layout.header.enginConfig.cancel") }}</el-button>
          <el-button type="primary" @click="confirmMain">{{ t("layout.header.enginConfig.confirm") }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { Folder } from "@element-plus/icons-vue";
import { Message } from "@/scripts/message";
import { osControlApi } from "@/api/modules/biz/os";
import { isEmpty, find, filter } from "lodash";
import { moreControlApi } from "@/api/modules/biz/more";
import { MoreInfo } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
import mittBus from "@/utils/mittBus";
import { useHmiStore } from "@/stores/modules";
import { ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const debugStore = useDebugStore();
const hmiStore = useHmiStore();
const props = defineProps<{
  enginConfigParam: { type: string; title: string; placeholder: string };
}>();
const dialogVisible = ref(false);
const configType = ref(0);
const configTypes = computed(() => [
  {
    value: 0,
    label: t("layout.header.enginConfig.all"),
    type: "all"
  },
  {
    value: 1,
    label: t("layout.header.enginConfig.deviceList"),
    type: "device"
  },
  {
    value: 2,
    label: t("layout.header.enginConfig.configureList"),
    type: "configure"
  }
]);
const title = computed(() => {
  return props.enginConfigParam.title;
});
const placeholder = computed(() => {
  return props.enginConfigParam.placeholder;
});
const filePath = ref<string>("");

const confirmMain = async (): Promise<void> => {
  confirm().then(() => {
    dialogVisible.value = false;
  });
};
const confirm = async (): Promise<void> => {
  const config = find(configTypes.value, { value: configType.value }) as { value: number; label: string; type: string } | undefined;
  if (!config) {
    return;
  }
  if (props.enginConfigParam.type == "export") {
    const req: MoreInfo.ExportConfigParam = { targetPath: filePath.value, type: config.type };
    const res: any = await moreControlApi.exportConfig(req);
    if (res.code == 0) {
      addSuccessTips(t("layout.header.enginConfig.exportSuccess"));
      return;
    }
    addWarningTips(res.msg);
    return;
  }
  if (!(await checkData(config.type))) {
    return;
  }
  const req: MoreInfo.ImportConfigParam = { sourcePath: filePath.value, type: config.type };
  const res: any = await moreControlApi.importConfig(req);
  if (res.code == 0) {
    addSuccessTips(t("layout.header.enginConfig.importSuccess"));
    notifyImport(config.type);
    return;
  }
  addWarningTips(res.msg);
};

const checkData = async (type: string): Promise<boolean> => {
  if (type === "device") {
    return checkDevice();
  } else if (type === "configure") {
    return checkConfigure();
  }
  const resDevice = await checkDevice();
  const resConfigure = await checkConfigure();
  return resDevice && resConfigure;
};
const checkDevice = async (): Promise<boolean> => {
  if (debugStore.deviceList.length > 0) {
    const connectedList = filter(debugStore.deviceList, { isConnect: true });
    if (connectedList && connectedList.length > 0) {
      Message.warning(t("layout.header.enginConfig.disconnectDeviceFirst"));
      return false;
    }
  }
  return true;
};
const checkConfigure = async (): Promise<boolean> => {
  const res = ref(false);
  const confList = hmiStore.hmiInfo.configureList;
  if (confList && confList[0].children && confList[0].children.length > 0) {
    await ElMessageBox.confirm(t("layout.header.enginConfig.overrideConfirm"), t("layout.header.enginConfig.warmTips"), {
      confirmButtonText: t("layout.header.enginConfig.confirm"),
      cancelButtonText: t("layout.header.enginConfig.cancel"),
      type: "warning"
    })
      .then(async () => {
        res.value = true;
      })
      .catch(() => {
        // 用户取消操作，res.value 保持 false
      });
  } else {
    res.value = true;
  }
  return res.value;
};

const addSuccessTips = (msg: string) => {
  Message.success(msg);
  debugStore.addConsole(msg);
};
const addWarningTips = (msg: string) => {
  Message.warning(msg);
  debugStore.addConsole(msg);
};

const cancel = async (): Promise<void> => {
  dialogVisible.value = false;
};

const openDirectory = async (): Promise<void> => {
  const config = find(configTypes.value, { value: configType.value }) as { value: number; label: string; type: string } | undefined;
  if (!config) {
    return;
  }
  if (
    props.enginConfigParam.type == "export" ||
    config.type == MoreInfo.ConfigTypeEnum.TYPE_ALL ||
    config.type == MoreInfo.ConfigTypeEnum.TYPE_CONFIGURE
  ) {
    const res: any = await osControlApi.selectFolder();
    const path: string = res;
    if (!isEmpty(path)) {
      filePath.value = path;
    }
    return;
  }
  const res: any = await osControlApi.selectFileByParams({
    title: t("layout.header.enginConfig.importConfigFile"),
    filterList: [{ name: "json", extensions: ["json"] }]
  });
  const path: string = res.path;
  if (!isEmpty(path)) {
    filePath.value = path;
  }
};

const notifyImport = (type: string) => {
  mittBus.emit("afterImportConfig", type);
};

const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });

watch(
  () => dialogVisible.value,
  newValue => {
    if (newValue == true) {
      filePath.value = "";
    }
  }
);
</script>

<style lang="scss" scoped>
.engin-main {
  :deep(.el-dialog__header) {
    padding: 5px;
  }
  :deep(.el-dialog__body) {
    padding: 10px;
  }
  .engin-root {
    .engin-type {
      margin-bottom: 15px;
    }
    .engin-button {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      justify-content: flex-end;
      margin-top: 25px;
    }
  }
}
</style>
