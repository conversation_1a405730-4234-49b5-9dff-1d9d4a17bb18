<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :pagination="false"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      table-key="deviceBackup"
      highlight-current-row
      @search="getBackupInfo"
      row-key="type"
      @selection-change="onSelectionChange"
    >
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("device.backup.savePath") }}：</el-text>
          <el-input v-model="filePath" :placeholder="t('device.backup.setPath')" style="width: 200px" readonly> </el-input>
          <el-button type="primary" plain @click="openFileDialog" :icon="Setting" :title="t('device.backup.setPathTitle')"></el-button>
          <el-button type="success" :icon="FolderOpened" :disabled="!filePath" @click="locateFolder">
            {{ t("device.backup.locateFolder") }}
          </el-button>
          <el-button
            type="primary"
            :icon="Upload"
            :disabled="backuping || scope.selectedList.length == 0"
            @click="backupFile(tableData.filter(row => scope.selectedList.some(sel => sel.type === row.type)))"
          >
            {{ t("device.backup.startBackup") }}
          </el-button>
          <el-button type="warning" :icon="Dish" :disabled="!backuping || canceling" plain @click="cancelBackup">
            {{ t("device.backup.cancelBackup") }}
          </el-button>
        </div>
      </template>
      <template #operation="scope">
        <el-button
          type="primary"
          link
          :icon="Briefcase"
          :disabled="backuping && scope.row.taskId !== currentBackupTaskId.value"
          @click="backupFile(scope.row)"
        >
          {{ t("device.backup.backup") }}
        </el-button>
        <el-button
          type="primary"
          link
          v-if="scope.row.hasTask && scope.row.taskId === currentBackupTaskId.value"
          :icon="Dish"
          @click="cancelSingleBackup(scope.row)"
        >
          {{ t("device.backup.cancelBackup") }}
        </el-button>
      </template>
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive, onMounted, onBeforeUnmount, computed } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { Upload, Setting, Briefcase, FolderOpened, Dish } from "@element-plus/icons-vue";
import { BackupInfo } from "@/api/interface/biz/debug/backupinfo";
// 扩展类型，解决 hasTask、taskId 语法错误
import { v4 as uuidv4 } from "uuid"; // 需安装 uuid 库

// 备份类型常量，避免使用中文
const BackupType = {
  ParamValue: "paramValue",
  FaultInfo: "faultInfo",
  CidConfigPrjLog: "cidConfigPrjLog",
  WaveReport: "waveReport"
} as const;
type BackupTypeEnum = (typeof BackupType)[keyof typeof BackupType];

type BackupTableRow = BackupInfo & {
  hasTask?: boolean;
  taskId?: string;
  progress: number;
  status: string;
  type: BackupTypeEnum;
  percentType?: string; // 新增
};

// 新增：后端一键备份API（假设已实现）
import { backupApi } from "@/api/modules/biz/debug/backup";

const { debugIndex } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();

const { t } = useI18n();

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });
const { addConsole } = useDebugStore();
const backuping = ref(false);
const canceling = ref(false);
const currentBackupTaskId = ref<string>(""); // 当前备份任务ID
// tableData 类型修正
const tableData = ref<BackupTableRow[]>([]);
const filePath = ref("");
import { ipc } from "@/api/request/ipcRenderer";

// 使用设备ID作为键的一部分，确保每个设备的路径独立保存
const FILE_PATH_KEY = computed(() => `device_backup_filePath_${props.deviceId}`);
const SELECTED_TYPE_KEY = computed(() => `device_backup_selectedTypes_${props.deviceId}`);

// 新增：单行任务状态
function initTableData() {
  tableData.value = [
    {
      type: BackupType.ParamValue,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.FaultInfo,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.CidConfigPrjLog,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.WaveReport,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    }
  ];
}
// 勾选变化时保存 type
function onSelectionChange(selectedRows: BackupTableRow[]) {
  const types = selectedRows.map(row => row.type);
  localStorage.setItem(SELECTED_TYPE_KEY.value, JSON.stringify(types));
}
const getBackupInfo = async () => {
  // 1. 记录当前已勾选的 type
  let checkedTypes: string[] = [];
  try {
    const saved = localStorage.getItem(SELECTED_TYPE_KEY.value);
    if (saved) {
      checkedTypes = JSON.parse(saved);
    } else {
      checkedTypes = proTable.value?.selectedList?.map(row => row.type) || [];
    }
  } catch {
    checkedTypes = proTable.value?.selectedList?.map(row => row.type) || [];
  }
  initTableData();
  proTable.value?.refresh();
  // 2. 刷新后恢复勾选
  setTimeout(() => {
    if (proTable.value && checkedTypes.length > 0) {
      tableData.value.forEach(row => {
        if (checkedTypes.includes(row.type)) {
          proTable.value?.element?.toggleRowSelection(row, true);
        }
      });
    }
  }, 0);
};
// backupFile 传递给后端的 types
const backupFile = async (selectedList: BackupTableRow[]) => {
  if (!filePath.value) {
    ElMessage.warning(t("device.backup.setPath"));
    addConsole(t("device.backup.console.pathNotSet"));
    return;
  }
  if (!selectedList || selectedList.length === 0) {
    ElMessage.warning(t("device.backup.noTypeSelected"));
    addConsole(t("device.backup.console.noTypeSelected"));
    return;
  }
  backuping.value = true;
  addConsole(t("device.backup.console.startBackup", { types: selectedList.map(i => i.type).join(", "), path: filePath.value }));
  try {
    const now = dayjs();
    const backupRoot = `${filePath.value}/back-${now.format("YYYYMMDD-HHmmss")}`;
    const taskId = uuidv4(); // 生成uuid
    currentBackupTaskId.value = taskId; // 保存当前任务ID
    selectedList.forEach(row => {
      row.taskId = taskId; // 保存到每个row
      row.hasTask = true; // 设置有任务标志
      row.progress = 0; // 重置进度
      row.status = t("device.backup.backupStatus.starting"); // 设置初始状态
      row.percentType = "primary"; // 设置进度条类型
    });
    // 组装备份类型，直接传英文枚举值
    const types = selectedList.map(item => item.type);
    const res = await backupApi.oneKeyBackupByDevice(props.deviceId, {
      backupRoot,
      types,
      taskId // 传递给后端
    });
    if (res && res.code === 0) {
      ElMessage.success(t("device.backup.backupSuccess"));
      // addConsole("备份成功");
    } else {
      ElMessage.error(t("device.backup.backupFailed") + (res?.msg || ""));
      // addConsole("备份失败：" + (res?.msg || "未知错误"));
    }
  } catch (err: any) {
    ElMessage.error(t("device.backup.backupFailed") + (err.message || err));
    addConsole(t("device.backup.console.backupException", { error: err.message || err }));
  } finally {
    backuping.value = false;
    currentBackupTaskId.value = ""; // 清理任务ID
  }
};
const openFileDialog = async () => {
  const savePath = await osControlApi.selectFolder();
  if (String(savePath) !== "") {
    filePath.value = String(savePath);
    localStorage.setItem(FILE_PATH_KEY.value, filePath.value); // 记忆路径
    addConsole(t("device.backup.console.pathSelected", { path: filePath.value }));
  } else {
    addConsole(t("device.backup.console.pathNotSelected"));
  }
};
const locateFolder = async () => {
  if (!filePath.value) {
    addConsole(t("device.backup.console.pathNotSetForLocate"));
    return;
  }
  try {
    await osControlApi.openDirectory({ id: filePath.value });
    addConsole(t("device.backup.console.folderOpened", { path: filePath.value }));
  } catch (err: any) {
    ElMessage.error(t("device.backup.openFolderFailed") + (err.message || err));
    addConsole(t("device.backup.console.openFolderFailed", { error: err.message || err }));
  }
};
// 取消备份功能
const cancelBackup = async () => {
  if (!backuping.value) {
    ElMessage.warning(t("device.backup.noBackupToCancel"));
    return;
  }

  canceling.value = true;
  addConsole(t("device.backup.console.attemptCancel"));

  try {
    // 使用当前任务ID取消备份
    if (!currentBackupTaskId.value) {
      ElMessage.error(t("device.backup.noTaskIdFound"));
      addConsole(t("device.backup.console.noTaskIdFound"));
      return;
    }

    addConsole(t("device.backup.console.attemptCancel") + ` (TaskID: ${currentBackupTaskId.value})`);

    const result = await backupApi.cancelBackupByDevice(props.deviceId, {
      id: currentBackupTaskId.value
    });

    if (Number(result.code) === 0) {
      ElMessage.success(t("device.backup.cancelSuccess"));
      addConsole(t("device.backup.console.cancelSuccess"));

      // 重置所有正在进行的任务状态
      let cancelledCount = 0;
      tableData.value.forEach(row => {
        if (row.taskId === currentBackupTaskId.value) {
          row.status = t("device.backup.backupStatus.userCancelled");
          row.progress = 0;
          row.percentType = "warning";
          row.taskId = "";
          row.hasTask = false;
          cancelledCount++;
        }
      });

      addConsole(`已取消 ${cancelledCount} 个备份任务，包括正在进行的文件上传`);
    } else {
      ElMessage.error(t("device.backup.cancelFailed") + (result.msg || ""));
      addConsole(t("device.backup.console.cancelFailed", { error: result.msg || "" }));
    }
  } catch (err: any) {
    ElMessage.error(t("device.backup.cancelFailed") + (err.message || err));
    addConsole(t("device.backup.console.cancelException", { error: err.message || err }));
  } finally {
    canceling.value = false;
    backuping.value = false;
    currentBackupTaskId.value = "";
  }
};

// 取消单个备份任务
const cancelSingleBackup = async (row: BackupTableRow) => {
  if (!row.taskId) {
    ElMessage.error(t("device.backup.noTaskIdFound"));
    return;
  }

  try {
    addConsole(`尝试取消类型[${row.type}]的备份任务 (TaskID: ${row.taskId})`);

    const result = await backupApi.cancelBackupByDevice(props.deviceId, {
      id: row.taskId
    });

    if (Number(result.code) === 0) {
      ElMessage.success(t("device.backup.cancelSuccess"));
      addConsole(t("device.backup.console.singleCancelSuccess", { type: row.type }));

      // 保存任务ID用于后续比较
      const taskId = row.taskId;

      // 重置该任务状态
      row.status = t("device.backup.backupStatus.userCancelled");
      row.progress = 0;
      row.percentType = "warning";
      row.taskId = "";
      row.hasTask = false;

      addConsole(`类型[${row.type}]的文件上传任务已取消`);

      // 如果这是当前任务，清理全局状态
      if (currentBackupTaskId.value === taskId) {
        backuping.value = false;
        currentBackupTaskId.value = "";
      }
    } else {
      ElMessage.error(t("device.backup.cancelFailed") + (result.msg || ""));
      addConsole(t("device.backup.console.singleCancelFailed", { type: row.type, error: result.msg || "" }));
    }
  } catch (err: any) {
    ElMessage.error(t("device.backup.cancelFailed") + (err.message || err));
    addConsole(t("device.backup.console.singleCancelException", { type: row.type, error: err.message || err }));
  }
};

// 监听备份进度通知
function handleBackupNotify(_event: any, notify: any) {
  console.log("notify", notify);
  if (!notify || notify.type === undefined || !notify.data) return;
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  // 新增：处理 type 为 "backup" 的情况
  if (notify.type === "backup") {
    addConsole(t("device.backup.console.taskCompleted"));
    return;
  }
  if (notify.type === "cancelbackup") {
    addConsole(t("device.backup.console.taskCancelled"));
    return;
  }
  // type: 具体备份类型，data: { step, percent, status, errorMsg }
  const { type, data } = notify;
  // 找到 tableData 中对应类型的行
  const row = tableData.value.find(item => item.type === type);
  if (row) {
    row.progress = data.percent;
    row.status = data.status || data.step || "";
    // 状态赋值
    if (data.errorMsg) {
      row.status = `${data.errorMsg}`;
      row.percentType = "exception";
      addConsole(t("device.backup.console.typeError", { type, error: data.errorMsg }));
    } else if (data.status === "completed" || data.status === "success" || data.percent === 100) {
      row.percentType = "success";
      row.hasTask = false;
      row.taskId = "";
      addConsole(t("device.backup.console.typeCompleted", { type }));

      // 检查是否所有任务都完成了
      const hasRunningTasks = tableData.value.some(r => r.hasTask && r.taskId === currentBackupTaskId.value);
      if (!hasRunningTasks) {
        backuping.value = false;
        currentBackupTaskId.value = "";
      }
    } else if (data.status === "cancelled" || data.status === "canceled" || data.status === "USER_CANCEL") {
      row.percentType = "warning";
      row.status = t("device.backup.backupStatus.userCancelled");
      row.hasTask = false;
      row.taskId = "";
      addConsole(t("device.backup.console.typeCancelled", { type }));

      // 检查是否所有任务都取消了
      const hasRunningTasks = tableData.value.some(r => r.hasTask && r.taskId === currentBackupTaskId.value);
      if (!hasRunningTasks) {
        backuping.value = false;
        currentBackupTaskId.value = "";
      }
    } else if (data.status === "failed" || data.status === "error") {
      row.percentType = "exception";
      row.hasTask = false;
      row.taskId = "";
      addConsole(t("device.backup.console.typeFailed", { type }));

      // 检查是否所有任务都结束了
      const hasRunningTasks = tableData.value.some(r => r.hasTask && r.taskId === currentBackupTaskId.value);
      if (!hasRunningTasks) {
        backuping.value = false;
        currentBackupTaskId.value = "";
      }
    } else if (data.status === "DATA_TRANSFER") {
      row.percentType = "primary";
      row.status = t("device.backup.backupStatus.transferring");
    } else {
      row.percentType = "";
      // addConsole(`类型[${type}]进度：${data.percent}%，状态：${data.status || data.step || "-"}`);
    }
    // 强制刷新tableData，确保进度条刷新
    tableData.value = [...tableData.value];
  }
}

onMounted(() => {
  const savedPath = localStorage.getItem(FILE_PATH_KEY.value);
  if (savedPath) {
    filePath.value = savedPath;
    console.log(`DeviceBackUp mounted: 恢复保存的路径 ${savedPath} for device ${props.deviceId}`);
  } else {
    console.log(`DeviceBackUp mounted: 没有找到保存的路径 for device ${props.deviceId}`);
  }
  getBackupInfo();
  if (ipc && ipc.on) {
    ipc.on("backup_notify", handleBackupNotify);
  }
});

onBeforeUnmount(() => {
  if (ipc && ipc.removeListener) {
    ipc.removeListener("backup_notify", handleBackupNotify);
  }
});

// 表格配置项
const columns = reactive<ColumnProps<BackupTableRow>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("device.backup.sequence"), fixed: "left", width: 60 },
  {
    prop: "type",
    label: t("device.backup.backupType"),
    width: 200,
    render: scope => {
      let label = "";
      switch (scope.row.type) {
        case BackupType.ParamValue:
          label = t("device.backup.backupTypes.paramValue");
          break;
        case BackupType.FaultInfo:
          label = t("device.backup.backupTypes.faultInfo");
          break;
        case BackupType.CidConfigPrjLog:
          label = t("device.backup.backupTypes.cidConfigPrjLog");
          break;
        case BackupType.WaveReport:
          label = t("device.backup.backupTypes.waveReport");
          break;
        default:
          label = scope.row.type;
      }
      return <span>{label}</span>;
    }
  },
  {
    prop: "desc",
    label: t("device.backup.backupDesc"),
    minWidth: 300,
    render: scope => {
      let desc = "";
      switch (scope.row.type) {
        case BackupType.ParamValue:
          desc = t("device.backup.backupDescTypes.paramValue");
          break;
        case BackupType.FaultInfo:
          desc = t("device.backup.backupDescTypes.faultInfo");
          break;
        case BackupType.CidConfigPrjLog:
          desc = t("device.backup.backupDescTypes.cidConfigPrjLog");
          break;
        case BackupType.WaveReport:
          desc = t("device.backup.backupDescTypes.waveReport");
          break;
        default:
          desc = "-";
      }
      return <span>{desc}</span>;
    }
  },
  {
    prop: "progress",
    label: t("device.backup.progress"),
    render: scope => {
      return (
        <div>
          <el-progress percentage={scope.row.progress} text-inside={true} stroke-width={16} status={scope.row.percentType}></el-progress>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: t("device.backup.status")
  }
]);

watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      proTable.value?.reset();
      getBackupInfo();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
