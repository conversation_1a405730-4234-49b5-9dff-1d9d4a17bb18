import { defineStore } from "pinia";

const name = "simple-userCenter"; // 定义模块名称

const accountBasic = "accountBasic";
const file = "file";
const message = "message";
const short = "short";
/* MqttState */
export interface UserCenterState {
  /** 当前标签页 */
  tab: string;
  accountBasic: string;
  file: string;
  message: string;
  short: string;
}

/** Mqtt模块 */
export const useCenterStore = defineStore({
  id: name,
  state: (): UserCenterState => ({
    tab: accountBasic,
    accountBasic: accountBasic,
    file: file,
    message: message,
    short: short
  }),
  getters: {
    getTab: state => state.tab,
    getAccountBasic: state => state.accountBasic,
    getFile: state => state.file,
    getMessage: state => state.message,
    getShort: state => state.short
  },
  actions: {
    setTab(tab: string) {
      this.tab = tab;
    },
    resetTab() {
      this.tab = accountBasic;
    },
    setMessage() {
      this.tab = "message";
    }
  }
});
