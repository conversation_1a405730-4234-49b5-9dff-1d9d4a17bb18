<script lang="ts" setup>
const props = defineProps<{
  title?: string;
}>();

const { title } = toRefs(props);
</script>

<template>
  <div class="c-card">
    <div v-if="title" class="c-card-title">
      {{ title }}
    </div>
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.c-card {
  background-color: var(--bl-bg-color);
  border-radius: 4px;

  &-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
  }
}
</style>
