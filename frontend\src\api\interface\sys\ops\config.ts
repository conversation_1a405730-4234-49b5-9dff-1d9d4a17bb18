import { SysConfigTypeEnum } from "@/enums";

export namespace SysConfig {
  /** 系统配置返回 */
  export interface ConfigInfo {
    /** id */
    id: number | string;
    /** 分类 */
    category: SysConfigTypeEnum;
    /** key */
    configKey: string;
    /** value */
    configValue: string;
    /** 描述 */
    remark?: string;
    /** 排序 */
    sortCode?: number;
  }

  /** 工作台 */
  export interface WorkBenchData {
    /** 快捷方式 */
    shortcut: number[] | string[];
  }

  // 超链接接口
  export interface FooterLinkProps {
    /** 标题 */
    name: string;
    /** 地址 */
    url: string;
    /** 排序 */
    sortCode: number;
  }

  /** 系统配置接口 */
  export interface SysBaseConfig {
    /** 系统logo */
    SYS_LOGO: string;
    /** 系统名称 */
    SYS_NAME: string;
    /** 系统版本 */
    SYS_VERSION: string;
    /** 第三方波形工具路径 */
    WAVE_TOOL_PATH: string;
    /** 系统描述 */
    SYS_COPYRIGHT: string;
    /** 系统描述地址 */
    SYS_COPYRIGHT_URL: string;
  }

  /** 参数配置 */
  export interface ParamConfig {
    /** 参数刷新间隔 */
    PARAM_REFRESH_TIME: number;
    /** 变量刷新间隔 */
    VARI_REFRESH_TIME: number;
    /** 状态量刷新间隔 */
    STATE_REFRESH_TIME: number;
    /** 报告刷新间隔 */
    REPORT_REFRESH_TIME: number;
  }
  /** 登录配置接口 */
  export interface LoginPolicyConfig {
    /** 单用户登录开关 */
    LOGIN_SINGLE_OPEN: string;
    /** 登录验证码开关 */
    LOGIN_CAPTCHA_OPEN: string;
    /** 登录验证码类型 */
    LOGIN_CAPTCHA_TYPE: string;
    /** 登录错误次数 */
    LOGIN_ERROR_COUNT: number;
    /** 登录重置时间 */
    LOGIN_ERROR_RESET_TIME: number;
    /** 登录错误锁定时间 */
    LOGIN_ERROR_LOCK: number;
  }

  /** 密码配置接口 */
  export interface PwdPolicyConfig {
    /** 默认密码 */
    PWD_DEFAULT_PASSWORD: string;
    /** 密码定期提醒更新 */
    PWD_REMIND: string;
    /** 密码提醒时间 */
    PWD_REMIND_DAY: number;
    /** 修改初始密码提醒 */
    PWD_UPDATE_DEFAULT: string;
    /** 密码最小长度 */
    PWD_MIN_LENGTH: number;
    /** 包含数字 */
    PWD_CONTAIN_NUM: string;
    /** 包含小写字母 */
    PWD_CONTAIN_LOWER: string;
    /** 包含大写字母 */
    PWD_CONTAIN_UPPER: string;
    /** 包含特殊字符 */
    PWD_CONTAIN_CHARACTER: string;
  }

  /** mqtt配置接口 */
  export interface MqttPolicyConfig {
    /** MQTT服务端地址 */
    MQTT_PARAM_URL: string;
    /** MQTT用户名 */
    MQTT_PARAM_USERNAME: string;
    /** MQTT密码 */
    MQTT_PARAM_PASSWORD: string;
  }
}
