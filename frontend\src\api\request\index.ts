import { ResultEnum } from "@/enums/httpEnum";
import { createRequest } from "./request";
import { createIpcRequest } from "@/api/request/ipcRequest";

const isHttpProxy = import.meta.env.VITE_HTTP_PROXY === "true"; // 是否使用代理
const url = import.meta.env.VITE_API_URL as string; // 请求地址

export const moduleRequest = (moduleUrl: string, prefix: string = "/api") =>
  createRequest({
    //请求地址,可在 .env.** 文件中修改
    baseURL: isHttpProxy ? prefix + moduleUrl : url + prefix + moduleUrl,
    // 设置超时时间
    timeout: ResultEnum.TIMEOUT as number,
    // 跨域时候允许携带凭证
    withCredentials: true
  });

export const moduleIpcRequest = (prefix: string) => createIpcRequest(prefix);
