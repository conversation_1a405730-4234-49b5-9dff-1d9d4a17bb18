export type RemoteStatus = "sync_check" | "dead_chek" | "no_check" | "split" | "join" | "";
export type RemoteData = "2_sbo" | "1_sbo" | "2_direct" | "1_direct";

export interface SelectRequestData {
  /** 控制源即name */
  ctlObj: string;
  /** 值 */
  ctlVal: string;
  /** 模式 sbo=选控，direct=直控 */
  ctlModel: CtlMode;
  /** 当前时间iso8601格式 */
  opemTm: string;
  test: string;
  check: RemoteStatus;
}

export enum CtlMode {
  /** 选控 */
  SBO = "sbo",
  /** 直控 */
  DIRECT = "direct"
}
export enum CtlCheck {
  /** 不检 */
  NO_CHECK = "no_check",
  /** 检同期 */
  SYNC_CHECK = "sync_check",
  /** 检无压 */
  DEAD_CHECK = "dead_check"
}

export interface MenuIdName {
  id: string;
  type: string;
  names: Array<string>;
}

export interface CtlResult {
  success: boolean;
  addCauseDesc: string;
}

export interface DataValue {
  vkey: string;
  value: unknown;
  error?: string;
}
export interface DataValueReadRequestData {
  /** 信息体地址数组对应debug_info.xml中inf属性 */
  infItems: string[];
  /** 收到数据后的回调（当多帧数据时仅当前帧数据） */
  cb?: (values: DataValue[]) => void;
}

export interface GroupDataValueItem {
  value: unknown;
  quality: unknown;
  error?: string;
}
