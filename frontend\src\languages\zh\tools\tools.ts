export default {
  search: {
    placeholder: "根据关键词查找"
  },
  categories: {
    title: "📦IT工具",
    formatting: "📝格式化工具",
    xml: "🟡xml格式化",
    json: "🟡json格式化",
    conversion: "🔄转换工具",
    radix: "🟢进制转换",
    temperature: "🟢温度转换",
    encryption: "🔑加解密工具",
    textEncryption: "🔵文本加解密"
  },
  encryption: {
    title: "文本加解密",
    description: "使用加密算法（如AES、TripleDES、Rabbit或RC4）加密和解密文本明文",
    encrypt: "加密",
    inputText: "待加密文本:",
    inputPlaceholder: "请输入需要加密的文本内容...",
    key: "密钥:",
    keyPlaceholder: "请输入加密密钥",
    algorithm: "加密算法:",
    outputText: "加密后文本:",
    outputPlaceholder: "加密结果将显示在这里...",
    decrypt: "解密",
    decryptInputText: "待解密文本:",
    decryptInputPlaceholder: "请输入需要解密的密文...",
    decryptKey: "密钥:",
    decryptAlgorithm: "解密算法:",
    decryptOutputText: "解密后文本:",
    decryptError: "无法解密文本"
  },
  json: {
    title: "JSON格式化",
    description: "将JSON字符串修饰为友好的可读格式",
    sortKeys: "字段排序",
    indentSize: "缩进尺寸",
    inputLabel: "待格式化JSON",
    inputPlaceholder: "请粘贴你的JSON...",
    outputLabel: "格式化后JSON",
    invalid: "该文档不符合JSON规范，请检查"
  },
  xml: {
    title: "XML格式化",
    description: "将XML字符串修饰为友好的可读格式",
    collapseContent: "折叠内容 :",
    indentSize: "缩进尺寸:",
    inputLabel: "待格式化XML",
    inputPlaceholder: "请粘贴你的XML...",
    outputLabel: "格式化后XML",
    invalid: "该文档不符合XML规范，请检查"
  },
  temperature: {
    title: "温度转换",
    description: "开尔文、摄氏度、华氏度、兰金、德莱尔、牛顿、雷奥穆尔和罗默温度度数转换",
    kelvin: "开尔文",
    kelvinUnit: "K",
    celsius: "摄氏度",
    celsiusUnit: "°C",
    fahrenheit: "华氏度",
    fahrenheitUnit: "°F",
    rankine: "兰金",
    rankineUnit: "°R",
    delisle: "德莱尔",
    delisleUnit: "°De",
    newton: "牛顿",
    newtonUnit: "°N",
    reaumur: "雷奥穆尔",
    reaumurUnit: "°Ré",
    romer: "罗默",
    romerUnit: "°Rø"
  },
  radix: {
    title: "进制转换",
    description: "在不同的进制（十进制、十六进制、二进制、八进制、base64…）之间转换数字",
    inputLabel: "待转换数字",
    inputPlaceholder: "请输入数字(如: 100)",
    outputLabel: "转换结果",
    binary: "2进制(2)",
    binaryPlaceholder: "二进制结果...",
    octal: "8进制(8)",
    octalPlaceholder: "八进制结果...",
    decimal: "10进制(10)",
    decimalPlaceholder: "十进制结果...",
    hex: "16进制(16)",
    hexPlaceholder: "十六进制结果...",
    base64: "Base64(64)",
    base64Placeholder: "Base64结果...",
    customBase: "自定义进制",
    customBasePlaceholder: "Base {{base}} 结果..."
  },
  jsonViewer: {
    title: "JSON格式化",
    description: "将JSON字符串修饰为友好的可读格式",
    sortKeys: "字段排序",
    indentSize: "缩进尺寸",
    inputJson: "待格式化JSON",
    formattedJson: "格式化后JSON",
    placeholder: "请粘贴你的JSON...",
    validationError: "该文档不符合JSON规范.请检查"
  }
};
