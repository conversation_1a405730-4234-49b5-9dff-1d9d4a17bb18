package demo

import (
	"time"
	"electron-egg/demo/job"
	//"electron-egg/demo/sql/sqlitelib"
	"electron-egg/demo/util"

	"github.com/wallace5303/ee-go/eapp"
	"github.com/wallace5303/ee-go/elog"
)

// 使用 router Ctx
func Index() {
	startTime := time.Now()
	elog.Logger.Info("Start Demo")

	// 异步初始化非关键组件，提高启动速度
	go func() {
		// 初始化基础数据
		util.Boot()
		elog.Logger.Info("基础数据初始化完成，耗时: %v", time.Since(startTime))
	}()

	// 延迟初始化任务系统
	go func() {
		time.Sleep(100 * time.Millisecond) // 延迟100ms
		job.Boot()
		elog.Logger.Info("任务系统初始化完成，耗时: %v", time.Since(startTime))
	}()

	// 初始化数据库（如果需要）
	//sqlitelib.InitDB(false)

	// 注册关闭前的处理函数
	eapp.Register("beforeClose", func() {
		//sqlitelib.CloseDatabase()
	})

	elog.Logger.Info("Demo模块核心初始化完成，耗时: %v", time.Since(startTime))
}
