export default {
  downList: {
    title: "Lista de Descargas",
    deviceDirectory: "Directorio del Dispositivo",
    addFile: "Agregar Archivo",
    addFolder: "Agregar Carpeta",
    defaultExportFileName: "Lista de Archivos Descargados.xlsx",
    exportTitle: "Exportar Lista de Archivos Descargados",
    importTitle: "Importar Lista de Archivos Descargados",
    exportSuccess: "Lista de archivos descargados exportada correctamente, {path}",
    exportFailed: "Error al exportar la lista de archivos descargados",
    importSuccess: "Lista de archivos descargados importada correctamente",
    importFailed: "Error al importar la lista de archivos descargados: {msg}",
    fileExists: "El archivo {path} ya existe, ¡no se pudo agregar!",
    fileDeleted: "Archivo {path} eliminado",
    filesDeleted: "Archivos eliminados en lote",
    buttons: {
      addFile: "Agregar Archivo",
      addFolder: "Agregar Carpeta",
      import: "Importar",
      export: "Exportar",
      delete: "Eliminar",
      clear: "Limpiar",
      moveUp: "Subir",
      moveDown: "Bajar"
    },
    columns: {
      index: "Índice",
      fileName: "Nombre de Archivo",
      fileSize: "Tamaño de Archivo",
      filePath: "Ruta de Archivo",
      lastModified: "Última Modificación",
      operation: "Operación"
    },
    export: {
      defaultPath: "Lista de Descargas",
      title: "Exportar Lista de Descargas"
    },
    import: {
      title: "Importar Lista de Descargas"
    },
    dialog: {
      title: "Aviso",
      confirm: "Confirmar"
    },
    messages: {
      filesDeleted: "Archivos eliminados",
      exportSuccess: "Exportación exitosa",
      exportFailed: "Error en la exportación",
      importSuccess: "Importación exitosa",
      importFailed: "Error en la importación"
    }
  }
};
