/**
 * @description 电子签名组件接口
 * @license Apache License Version 2.0
 */
import ESign from "./index.vue";

/** 电子签名组件属性接口 */
export interface ESignProps {
  /** 画布宽度，即导出图片的宽度 */
  width: number;
  /** 画布高度，即导出图片的高度 */
  height: number;
  /** 画笔粗细 */
  lineWidth: number;
  /** 画笔颜色 */
  lineColor: string;
  /** 背景颜色画布背景色，为空时画布背景透明，支持多种格式 '#ccc'，'#E5A1A1'，'rgb(229, 161, 161)'，'rgba(0,0,0,.6)'，'red' */
  bgColor: string;
  /** 是否裁剪，在画布设定尺寸基础上裁掉四周空白部分 */
  isCrop: boolean;
  /** 清空画布时(reset)是否同时清空设置的背景色(bgColor) */
  isClearBgColor?: boolean;
  /** 生成图片格式 image/jpeg(jpg格式下生成的图片透明背景会变黑色请慎用或指定背景色)、 image/webp */
  format?: string;
  /** 生成图片质量；在指定图片格式为 image/jpeg 或 image/webp的情况下，可以从 0 到 1 的区间内选择图片的质量。如果超出取值范围，将会使用默认值 0.92。其他参数会被忽略。 */
  quality?: number;
  /** 前面图片 */
  image: string;
}

/** 电子签名组件点位坐标接口 */
export interface Points {
  x: number;
  y: number;
}

/**
 * @description 角色选择器实例类型
 */
export type ESignInstance = Omit<InstanceType<typeof ESign>, keyof ComponentPublicInstance | keyof ESignProps>;
