<template>
  <div class="device-list-collapse">
    <el-tooltip
      ref="tooltipRef"
      effect="dark"
      :content="globalStore.isDeviceList ? t('layout.header.collapse.fold') : t('layout.header.collapse.expand')"
      placement="top"
      trigger="hover"
      :show-arrow="true"
      :enterable="false"
    >
      <el-icon class="collapse-icon" @click="changeCollapse">
        <ArrowLeft v-if="globalStore.isDeviceList" />
        <ArrowRight v-else />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules/global";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { ref } from "vue";

const globalStore = useGlobalStore();
const { t } = useI18n();
const tooltipRef = ref();

const changeCollapse = () => {
  // 立即隐藏tooltip
  if (tooltipRef.value) {
    tooltipRef.value.hide();
  }
  globalStore.isDeviceList = !globalStore.isDeviceList;
};
</script>

<style scoped lang="scss">
.device-list-collapse {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: transparent;
  .collapse-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 200px;
    font-size: 12px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-radius: 3px;
    box-shadow: none;
    opacity: 0;
    transition: all 0.3s ease;
    &:hover {
      width: 16px;
      background-color: var(--el-color-primary-light-7);
      border: 1px solid var(--el-color-primary-light-5);
      box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
      opacity: 1;
      transform: translateX(-2px);
    }
    &:active {
      transform: translateX(-2px) scale(0.95);
    }

    // 显示箭头图标
    .el-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: var(--el-text-color-regular);
      text-shadow: 0 1px 2px rgb(255 255 255 / 80%);
    }
  }
}
</style>
