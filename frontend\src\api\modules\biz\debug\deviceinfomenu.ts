import { moduleIpcRequest } from "@/api/request";
import type { DebugInfoMenu } from "@/api/interface/biz/debug/debuginfo";
import { DebugDeviceInfo } from "@/stores/interface";
import { MenuIdName } from "@/api/interface/biz/debug/remote";

const ipc = moduleIpcRequest("controller/debug/debuginfomenu/");

const connectipc = moduleIpcRequest("controller/debug/deviceconnect/");

const deviceipc = moduleIpcRequest("controller/debug/deviceoperate/");

const deviceInfoMenutreeApi = {
  /** 获取后台数据（直接传 deviceId 字符串；服务端签名为 getDeviceMenuTree(id: string)） */
  getDeviceMenuTree(deviceId: string) {
    return ipc.invoke<DebugInfoMenu[]>("getDeviceMenuTree", deviceId);
  },

  // 获取items信息（指定 deviceId）
  getDeviceMenuItemByDevice(deviceId: string, param: MenuIdName) {
    return ipc.iecInvokeWithDevice<DebugInfoMenu[]>("getTreeItemByName", param, deviceId);
  },

  // 按照名称查找（指定 deviceId）
  getGroupInfoListByDevice(deviceId: string, param: any) {
    return ipc.iecInvokeWithDevice("getGroupInfoList", param, deviceId);
  }
};

const deviceConnectApi = {
  // 连接装置：此接口按服务端要求传入纯字符串参数，不使用装置包装
  deviceConnectByRpc(_deviceId: string, param: DebugDeviceInfo) {
    return connectipc.invoke("connectDeviceByRpc", JSON.stringify(param));
  },

  // 断开连接（指定 deviceId）
  disconnectDevice(deviceId: string) {
    return connectipc.iecInvokeWithDevice("deviceDisconnect", deviceId, deviceId);
  },

  // 启动报文监视（指定 deviceId）
  startMessageMonitor(deviceId: string) {
    return connectipc.iecInvokeWithDevice("startMessageMonitor", {}, deviceId);
  },

  // 停止报文监视（指定 deviceId）
  stopMessageMonitor(deviceId: string) {
    return connectipc.iecInvokeWithDevice("stopMessageMonitor", {}, deviceId);
  }
};

const deviceOperateApi = {
  // 获取所有装置
  getDeviceCfgList() {
    return deviceipc.invoke("getDeviceCfgList");
  },

  // 添加装置
  addDeviceCfg(param: DebugDeviceInfo) {
    return deviceipc.invoke("addDeviceCfg", param);
  },

  removeDeviceCfg(deviceId: string) {
    return deviceipc.iecInvokeWithDevice("removeDeviceCfg", undefined, deviceId);
  },

  updateDeviceCfg(deviceId: string, param: DebugDeviceInfo) {
    return deviceipc.iecInvokeWithDevice("updateDeviceCfg", param, deviceId);
  }
};

export { deviceInfoMenutreeApi, deviceConnectApi, deviceOperateApi };
