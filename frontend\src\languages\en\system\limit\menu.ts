export default {
  limit: {
    menu: {
      title: "Menu Management",
      form: {
        add: "Add Menu",
        edit: "Edit Menu",
        view: "View Menu",
        title: "Menu Name",
        icon: "Menu Icon",
        type: "Menu Type",
        path: "Route Path",
        component: "Component Path",
        permission: "Permission Identifier",
        sort: "Sort",
        status: "Status",
        description: "Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          title: "Please enter menu name",
          type: "Please select menu type",
          path: "Please enter route path",
          component: "Please enter component path",
          sort: "Please enter sort order",
          status: "Please select status"
        }
      },
      columns: {
        title: "Menu Name",
        icon: "Menu Icon",
        type: "Menu Type",
        path: "Route Path",
        component: "Component Path",
        permission: "Permission Identifier",
        sort: "Sort",
        status: "Status",
        operation: "Operation"
      },
      type: {
        menu: "Menu",
        button: "Button"
      },
      status: {
        enable: "Enable",
        disable: "Disable"
      }
    }
  }
};
