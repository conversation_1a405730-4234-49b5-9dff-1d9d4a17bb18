<template>
  <template v-for="subItem in menuList" :key="subItem.path">
    <el-sub-menu v-if="subItem.children?.length" :index="subItem.path">
      <template #title>
        <svg-icon v-if="subItem.meta.icon" :icon="subItem.meta.icon" class="el-icon" />
        <span class="sle">{{ $t(subItem.meta.title) }}</span>
      </template>
      <SubMenu :menu-list="subItem.children" />
    </el-sub-menu>
    <el-menu-item v-else :index="subItem.path" @click="handleClickMenu(subItem)">
      <svg-icon v-if="subItem.meta.icon" :icon="subItem.meta.icon" class="el-icon" />
      <template #title>
        <span class="sle">{{ $t(subItem.meta.title) }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

defineProps<{ menuList: Menu.MenuOptions[] }>();

const router = useRouter();
useI18n();

const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style lang="scss">
.el-sub-menu .el-sub-menu__title:hover {
  color: var(--el-menu-hover-text-color) !important;
  background-color: transparent !important;
}
.el-menu--collapse {
  .is-active {
    .el-sub-menu__title {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
    }
  }
}
.el-sub-menu {
  .el-icon {
    font-size: 28px;
  }
}
.el-menu-item {
  .el-icon {
    font-size: 28px;
  }
  &:hover {
    color: var(--el-menu-hover-text-color);
    background-color: var(--bl-hover-bg-color);
  }
  &.is-active {
    color: var(--el-menu-active-color) !important;
    background-color: var(--el-menu-active-bg-color) !important;
    border-radius: 8px;
    &::before {
      display: none;
    }
  }
}
.vertical,
.classic,
.transverse {
  .el-menu-item {
    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}
.columns {
  .el-menu-item {
    &.is-active {
      &::before {
        right: 0;
      }
    }
  }
}
</style>
