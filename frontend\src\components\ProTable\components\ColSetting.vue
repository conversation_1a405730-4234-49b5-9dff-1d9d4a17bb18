<template>
  <!-- 列设置 -->
  <el-drawer v-model="drawerVisible" :title="t('components.proTable.colSetting.title')" size="450px">
    <div class="table-main">
      <el-table :data="colSetting" :border="true" row-key="prop" default-expand-all :tree-props="{ children: '_children' }">
        <el-table-column prop="label" align="center" :label="t('components.proTable.colSetting.title')" />
        <el-table-column v-slot="scope" prop="isShow" align="center" :label="t('components.proTable.colSetting.fixedLeft')">
          <el-switch v-model="scope.row.isShow"></el-switch>
        </el-table-column>
        <el-table-column v-slot="scope" prop="sortable" align="center" :label="t('components.proTable.colSetting.fixedRight')">
          <el-switch v-model="scope.row.sortable"></el-switch>
        </el-table-column>
        <template #empty>
          <div class="table-empty">
            <img src="@/assets/images/notData.png" alt="notData" />
            <div>{{ t("components.proTable.table.empty") }}</div>
          </div>
        </template>
      </el-table>
    </div>
  </el-drawer>
</template>

<script setup lang="ts" name="ColSetting">
import { ref, watch } from "vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { useI18n } from "vue-i18n";
import { localSet } from "@/utils/index";

const { t } = useI18n();
const props = defineProps<{ colSetting: ColumnProps[]; tableKey?: string }>();
const colSetting = props.colSetting;
const tableKey = props.tableKey || "default";

const drawerVisible = ref<boolean>(false);

const openColSetting = () => {
  drawerVisible.value = true;
};

// 监听 colSetting 变化并保存到 localStorage
watch(
  () => colSetting,
  val => {
    const key = `proTable_colSetting_${location.pathname}_${tableKey}`;
    console.log("列设置保存key:", key, "val:", val);
    localSet(key, val);
  },
  { deep: true }
);

defineExpose({
  openColSetting
});
</script>

<style scoped lang="scss">
.cursor-move {
  cursor: move;
}
</style>
