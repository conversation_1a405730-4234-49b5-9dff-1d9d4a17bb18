<template>
  <div class="function-list-collapse">
    <el-tooltip
      ref="tooltipRef"
      effect="dark"
      :content="globalStore.isFunctionList ? t('layout.header.collapse.fold') : t('layout.header.collapse.expand')"
      placement="top"
      trigger="hover"
      :show-arrow="true"
      :enterable="false"
    >
      <el-icon class="collapse-icon" @click="changeCollapse">
        <ArrowLeft v-if="globalStore.isFunctionList" />
        <ArrowRight v-else />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { ref } from "vue";
import type { GlobalState } from "@/stores/interface";

const globalStore = useGlobalStore() as unknown as GlobalState;
const { t } = useI18n();
const tooltipRef = ref();

if (!("isFunctionList" in globalStore)) {
  (globalStore as any).isFunctionList = true;
}
const changeCollapse = () => {
  if (tooltipRef.value) {
    tooltipRef.value.hide();
  }
  globalStore.isFunctionList = !globalStore.isFunctionList;
};
</script>

<style scoped lang="scss">
.function-list-collapse {
  position: absolute;
  top: 50%;
  right: -30px;
  z-index: 1000;
  pointer-events: auto;
  background: transparent;
  transform: translateY(-50%);
  .collapse-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    font-size: 14px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transition: all 0.3s ease;
    &:hover {
      color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
      transform: scale(1.1);
    }
    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
