import { defineAsyncComponent, markRaw } from "vue";
import SkeletonLoading from "@/components/SkeletonLoading.vue";

interface Dictionary<T> {
  [key: string | number]: T;
}

export const undefinedToEmpty = (data: unknown): string => {
  if (data == undefined) {
    return "";
  }
  if (data instanceof Object) {
    return data.toString();
  }
  return data + "";
};

export const getComponentByMenuName = (name: string, isNode: boolean, fc: string, method: string): any => {
  console.log("getComponentByMenuName:", name, isNode, fc);

  // 根节点
  if (name == "Root") {
    return markRaw(DeviceSummary);
  }
  // 自定义组
  if (fc === "customGroup") {
    return markRaw(CustomGroupView);
  }
  // 子节点
  if (isNode) {
    return markRaw(GroupInfo);
  }
  // 报告
  if (fc == "LG" && method) {
    const components: Dictionary<any> = {
      QueryHisEvtByTime: ReportCommon,
      QueryHisFaultByTime: ReportGroup,
      QueryOpReportByTime: ReportOperate,
      QueryAuditLogByTime: ReportAuditLog
    };
    const comp = components[method];
    if (!comp) {
      console.warn("getComponentByMenuName: 未找到对应的LG报告组件", method);
      return null;
    }
    return markRaw(comp);
  }
  // DeviceSummary: DeviceSummary,
  const components: Dictionary<any> = {
    ALL_SP: AllParamSetting, // 全部定值
    ALL_SG: AllEditParamSetting, // 全部定值
    DC: DeviceInfo,
    CO: RemoteControl, // 遥控
    ST: RemoteSignal, // 遥信
    MX: RemoteTelemetry, // 遥测
    AO: RemoteYt, // 遥调
    CI: RemoteYm, // 遥脉
    BO: RemoteDrive, // 开出传动
    SP: ParamSetting, // 参数定值
    SG: ParamSetting // 参数定值
  };
  const comp = components[fc];
  if (!comp) {
    console.warn("getComponentByMenuName: 未找到对应的功能组件", fc);
    return markRaw(NotFound);
  }
  return markRaw(comp);
};

export const genRpcTimeParam = (date: Date): string => {
  // 使用与装置对时功能相同的格式：YYYY-MM-DD HH:mm:ss.SSS
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

function withSkeleton(loader: () => Promise<any>) {
  return defineAsyncComponent({
    loader,
    loadingComponent: SkeletonLoading,
    delay: 200 // 可选，延迟显示骨架屏
  });
}

const DeviceInfo = withSkeleton(() => import("@/views/biz/debug/device/views/DeviceInfo.vue"));
const ReportCommon = withSkeleton(() => import("@/views/biz/debug/device/views/ReportCommon.vue"));
const ReportGroup = withSkeleton(() => import("@/views/biz/debug/device/views/ReportGroup.vue"));
const ReportOperate = withSkeleton(() => import("@/views/biz/debug/device/views/ReportOperate.vue"));
const ReportAuditLog = withSkeleton(() => import("@/views/biz/debug/device/views/ReportAuditLog.vue"));
const ParamSetting = withSkeleton(() => import("@/views/biz/debug/device/views/ParamSetting.vue"));
const RemoteControl = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteControl.vue"));
const RemoteSignal = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteSignal.vue"));
const RemoteTelemetry = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteTelemetry.vue"));
const RemoteYt = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteYt.vue"));
const RemoteYm = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteYm.vue"));
const RemoteDrive = withSkeleton(() => import("@/views/biz/debug/device/views/RemoteDrive.vue"));
const DeviceSummary = withSkeleton(() => import("@/views/biz/debug/device/views/DeviceSummary.vue"));
const AllParamSetting = withSkeleton(() => import("@/views/biz/debug/device/views/AllParamSetting.vue"));
const GroupInfo = withSkeleton(() => import("@/views/biz/debug/device/views/GroupInfo.vue"));
const AllEditParamSetting = withSkeleton(() => import("@/views/biz/debug/device/views/AllEditParamSetting.vue"));
const CustomGroupView = withSkeleton(() => import("@/views/biz/debug/device/views/CustomGroup.vue"));
const NotFound = withSkeleton(() => import("@/components/ErrorMessage/404.vue"));
