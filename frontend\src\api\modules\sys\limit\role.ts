import { ReqId, ResPage, SysRole, SysUser } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/limit/role/");

const sysRoleApi = {
  /** 获取角色分页 */
  page(params: SysRole.Page) {
    return http.get<ResPage<SysRole.SysRoleInfo>>("page", params);
  },
  /** 获取角色树 */
  tree() {
    return http.get<SysRole.SysRoleTree[]>("tree", {}, { loading: false });
  },
  /** 获取角色详情 */
  detail(params: ReqId) {
    return http.get<SysRole.SysRoleInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除角色 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 获取资源授权树 */
  resourceTreeSelector() {
    return http.get<SysRole.ResTreeSelector[]>("resourceTreeSelector", {}, { loading: false });
  },
  /** 获取权限授权树 */
  permissionTreeSelector(params: ReqId) {
    return http.get<string[]>("permissionTreeSelector", params, { loading: false });
  },
  /** 获取角色拥有资源 */
  ownResource(params: ReqId) {
    return http.get<SysRole.RoleOwnResource>("ownResource", params, { loading: false });
  },
  /** 给角色授权资源 */
  grantResource(params: SysRole.GrantResourceReq) {
    return http.post("grantResource", params);
  },
  /** 获取角色拥有权限 */
  ownPermission(params: ReqId) {
    return http.get<SysRole.RoleOwnPermission>("ownPermission", params, { loading: false });
  },
  /** 给角色授权权限 */
  grantPermission(params: SysRole.GrantPermissionReq) {
    return http.post("grantPermission", params);
  },
  /** 获取角色下的用户 */
  ownUser(params: ReqId) {
    return http.get<SysUser.SysUserInfo[]>("ownUser", params, { loading: false });
  },
  /** 给角色授权用户 */
  grantUser(params: SysRole.GrantUserReq) {
    return http.post("grantUser", params);
  },
  /** 获取角色选择器 */
  roleSelector(params: SysRole.RoleSelectorReq) {
    return http.get<SysRole.SysRoleInfo[]>("roleSelector", params, { loading: false });
  }
};

export { sysRoleApi };
