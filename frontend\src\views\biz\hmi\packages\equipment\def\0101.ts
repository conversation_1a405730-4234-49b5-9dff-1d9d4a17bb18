import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "0101",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 30,
        x2: 5,
        y2: 35
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 0,
        x2: 5,
        y2: 4
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(0, 4.6, 10, 25)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "#000",
      stroke: "#000"
    }
  }
};

export default e;
