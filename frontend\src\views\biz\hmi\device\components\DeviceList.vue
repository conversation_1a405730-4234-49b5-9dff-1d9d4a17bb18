<template>
  <div class="scroll-container">
    <el-scrollbar v-if="searchForData.length > 0" class="scrollbar-list">
      <div
        v-for="item in searchForData"
        class="device-item flx-center card"
        :key="item.ip"
        :id="item.ip"
        :class="fnClass(item)"
        v-contextmenu:contextmenu
        @click="handleListClick(item)"
        @contextmenu.prevent="handleContextMenuEvent($event, item)"
      >
        <!-- 样式 -->
        <div class="pinned-tag"></div>
        <!-- 连接状态 -->
        <el-badge :hidden="false" style="margin-top: 3px">
          <svg-icon v-if="item.isConnect" icon="ep:circle-check-filled" class="status-icon connected"></svg-icon>
          <svg-icon v-else icon="ep:circle-close-filled" class="status-icon disconnected"></svg-icon>
        </el-badge>
        <!-- 装置 -->
        <div class="device-item-right">
          <div class="device-item-right-left">
            <div class="device-name flx-start">
              <span class="name-title">{{ item.ip + "(" + item.port + ")" }}</span>
            </div>
            <div class="device-item-right-bottom">
              {{ item?.name || t("hmi.device.deviceList.unnamed") }}
              <span class="device-item-right-right" :class="item.isConnect ? 'connected' : 'disconnected'">{{
                item.isConnect ? t("hmi.device.deviceList.connect") : t("hmi.device.deviceList.disconnect")
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备表单弹窗 -->
      <DeviceForm v-if="showForm" v-model:visible="showForm" :device="editForm" @submit="handleSubmit" @cancel="showForm = false" />

      <!-- 右键菜单 -->
      <contextmenu ref="contextmenu" :disabled="!isRight">
        <contextmenu-item v-for="item in contextMenuItems" :key="item.id" :class="item.class" @click="handleClickMenuItem(item)">
          <svg-icon :icon="item.svgIcon" class="menu-svg" />
          <span> {{ t("hmi.device.deviceList." + item.id) }}</span>
        </contextmenu-item>
      </contextmenu>
    </el-scrollbar>
    <div v-else class="device-item-empty flx-center">
      <span>{{ t("hmi.device.deviceList.notFound") }}</span>
    </div>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts">
import { ref, computed, ComputedRef } from "vue";
import { filter, forEach, set } from "lodash";
import { Contextmenu, ContextmenuItem } from "v-contextmenu";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugDeviceInfo } from "@/stores/interface";
import DeviceForm from "../../../debug/device/dialog/DeviceFormDialog.vue";
import ProgressDialog from "../../../debug/device/dialog/ProgressDialog.vue";
import { IECNotify, ResultData } from "@/api";
import { deviceConnectApi } from "@/api/modules/biz/debug/deviceinfomenu";
import { ipc } from "@/api/request/ipcRenderer";
import mittBus from "@/utils/mittBus";
import Message from "@/scripts/message";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const isRight = ref(true);
const contextMenuItems = ref<any>([]);
const contextMenuItemInfo = ref<DebugDeviceInfo>();
const debugStore = useDebugStore();
const progressDialog = ref();
contextMenuItems.value = [
  {
    id: "connect",
    icon: "",
    svgIcon: "ant-design:link-outlined",
    text: t("hmi.device.deviceList.connect")
  },
  {
    id: "edit",
    icon: "",
    svgIcon: "ant-design:edit-outlined",
    text: t("hmi.device.deviceList.edit")
  },
  {
    id: "disconnect",
    icon: "",
    svgIcon: "ant-design:disconnect-outlined",
    text: t("hmi.device.deviceList.disconnect")
  },
  {
    id: "remove",
    icon: "",
    svgIcon: "ant-design:delete-outlined",
    text: t("hmi.device.deviceList.delete")
  }
];

// const deviceListStyle = computed<any>(() => {
//   let offset = 0;
//   if (globalStore.tabs) {
//     offset += 20;
//   }
//   if (globalStore.footer) {
//     offset += 15;
//   }
//   return { deviceEmpty: { height: `calc(100vh - 561px - ${offset}px)` } };
// });

const searchForData: ComputedRef<DebugDeviceInfo[]> = computed(() => {
  const deviceList = debugStore.deviceList;
  const searchText = debugStore.searchDevice.toLowerCase();

  return [
    ...filter(deviceList, obj => {
      return (
        obj.ip.toLowerCase().includes(searchText) ||
        obj.port.toString().toLowerCase().includes(searchText) ||
        obj.name.toLowerCase().includes(searchText)
      );
    })
  ];
});

// const getTreeScrollbar = () => {
//   let offset = 550;
//   if (globalStore.tabs) {
//     offset += 40;
//   }
//   if (globalStore.footer) {
//     offset += 30;
//   }
//   return `calc(100vh  - ${offset}px)`;
// };

const fnClass = item => {
  if (item.isActive == true) {
    return "is-active";
  }
  return "";
};

// 右键菜单
const handleContextMenuEvent = (e, item) => {
  isRight.value = true;
  contextMenuItemInfo.value = item;
};

// 点击装置
const handleListClick = data => {
  setCurrDevice(data);
};

const setCurrDevice = data => {
  forEach(debugStore.deviceList, item => {
    set(item, "isActive", false);
  });
  data.isActive = true;
  debugStore.setCurrDevice(data);
};

const handleClickMenuItem = async item => {
  const data = contextMenuItemInfo.value;
  if (!data) return;
  setCurrDevice(data);
  debugStore.initReportData(data.id);
  if (item.id === "edit") {
    if (data?.isConnect) {
      ElMessage.warning(t("hmi.device.deviceList.editWarn"));
      return;
    }
    // 打开编辑弹窗
    editForm.value = data;
    showForm.value = true;
  } else if (item.id === "connect") {
    // 调用装置连接接口
    progressDialog.value?.show();
    try {
      const res: ResultData = await deviceConnectApi.deviceConnectByRpc(data.id, data);
      let msg: string | undefined;
      if (res.code === 0) {
        data.isConnect = true;
        const nowTime =
          getCurrentData() + new Date().getHours() + ":" + (new Date().getMinutes() > 10 ? new Date().getMinutes() : "0" + new Date().getMinutes());
        data.connectTime = nowTime;
        mittBus.emit("hmi.deviceConnectSuccess", data);
        debugStore.addConsole(t("hmi.device.deviceList.connectSuccess", { name: data.name }));
      } else if (res.code === 103) {
        msg = t("hmi.device.deviceList.connectExist", { name: data.name });
        debugStore.addConsole(msg);
      } else {
        msg = t("hmi.device.deviceList.connectFailed", { name: data.name });
        debugStore.addConsole(msg);
        if (res.data) {
          msg = t("hmi.device.deviceList.connectFailedReason", { reason: res.data });
          debugStore.addConsole(msg);
        }
      }
      if (msg) {
        Message.error(msg);
      }
    } finally {
      progressDialog.value?.hide();
    }

    debugStore.initReportData(data.id);
  } else if (item.id === "disconnect") {
    const res: ResultData = await deviceConnectApi.disconnectDevice(data.id);
    if (res.code === 0) {
      data.isConnect = false;
      debugStore.addConsole(t("hmi.device.deviceList.disconnectSuccess", { name: data.name }));
    } else {
      debugStore.addConsole(t("hmi.device.deviceList.operateFailed", { name: data.name }));
    }
  } else if (item.id === "remove") {
    if (data?.isConnect) {
      ElMessage.warning(t("hmi.device.deviceList.deleteWarn"));
      return;
    }
    const index = debugStore.deviceList.findIndex(item => item.id === data.id);
    if (index >= 0) {
      const removceDevice = debugStore.deviceList[index];
      debugStore.removeDevice(removceDevice.id);
    }
  }
};

const showForm = ref(false);
const editForm = ref<DebugDeviceInfo>();

const handleSubmit = (device: DebugDeviceInfo) => {
  if (device.id) {
    // 更新已有设备
    debugStore.updateDevice(device);
  } else {
    // 添加新设备
    debugStore.addDevice({
      ...device,
      id: "",
      prjType: 1,
      deviceType: 1,
      isConnect: false,
      isActive: false,
      connectTime: ""
    });
  }
  showForm.value = false;
};

ipc.removeAllListeners("notify");
ipc.on("notify", (_event: unknown, notify: IECNotify) => {
  // 连接进度通知
  if (notify.type == "disconnect") {
    // 检查是否有deviceId，如果有则只更新对应设备的状态
    if (notify.deviceId) {
      const targetDevice = debugStore.deviceList.find(device => device.id === notify.deviceId);
      if (targetDevice) {
        targetDevice.isConnect = false;
        debugStore.addConsole(`装置 ${targetDevice.name} 连接已断开`);

        // 如果断开的是当前选中的设备，也更新当前设备状态
        if (debugStore.currDevice.id === notify.deviceId) {
          debugStore.currDevice.isConnect = false;
        }
      }
    } else {
      // 兼容旧版本：如果没有deviceId，则更新当前设备状态
      debugStore.currDevice.isConnect = false;
      debugStore.addConsole(`当前装置连接已断开`);
    }
  }
});

const getCurrentData = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};
</script>

<style lang="scss" scoped>
.scroll-container {
  position: relative;
  width: 100%;
  min-height: 30%;
  max-height: 30%;
  border: 1px solid var(--el-border-color);
  .scrollbar-list {
    background: var(--el-bg-color);
  }
  .device-item {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 45px;
    padding: 12px 16px;
    margin: 3px;
    overflow: hidden;
    color: var(--el-text-color);
    cursor: pointer;
    user-select: none;
    &:hover {
      background: var(--bl-hover-bg-color);
    }

    // 添加分割线
    &:last-child::after {
      display: none; // 最后一个item不需要分割线
    }
    .pinned-tag {
      position: absolute;
      top: 3px;
      left: 3px;
      display: block;
      border: 6px solid var(--el-color-primary);
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-radius: 2px;
      opacity: 0.8;
    }
    .status-icon {
      margin-right: 10px;
      font-size: 35px;
      &.connected {
        color: limegreen;
      }
      &.disconnected {
        color: red;
      }
    }
    .device-item-right {
      position: relative;
      width: 100%;
      height: 40px;
      margin-top: 5px;
      .device-item-right-left {
        width: 100%;
        .device-name {
          max-width: 180px;
          max-height: 18px;
          font-size: 14px;
          line-height: 18px;
          color: var(--el-text-color);
          .name-title {
            padding-right: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .device-item-right-bottom {
        font-size: 12px;
      }
      .device-item-right-right {
        float: right;
        width: auto;
        overflow: hidden;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        text-overflow: ellipsis;
        white-space: nowrap;
        &.connected {
          color: limegreen;
        }
        &.disconnected {
          color: red;
        }
      }
    }
  }
  .device-item-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--el-text-color-secondary);
    background: var(--el-bg-color);
  }
  .is-active {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);
    background: var(--el-color-primary-light-9) !important;
  }
}
</style>
