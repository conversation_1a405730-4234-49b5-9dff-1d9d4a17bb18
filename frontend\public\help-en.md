# User Manual

## 1. Function Overview

**VisualDebug** is a debugging tool specifically designed for Sieyuan visualization platform devices (R&D and engineering debugging and management), integrating rich debugging, configuration, management and auxiliary functions to help engineers efficiently complete various debugging tasks.

![Tool Overall Interface](./help-en/工具整体界面.png)

Main functions include:

- Device debugging and management
- Configuration tools
- IT tools
- Tool settings, theme configuration, language switching, more functions

Through the above functions, VisualDebug can meet the full-process R&D and engineering debugging, configuration design and device daily management needs of Sieyuan visualization platform devices.

### 1.1 Device Debugging and Management

- Device connection information configuration
- Device basic information viewing
- Setting values, analog values, status values, remote signaling, remote measurement, remote control, remote adjustment, outlet transmission, reports
- Device time synchronization, variable debugging, setting value import and export
- File upload, file download

![Device Debugging and Management](./help-en/装置调试与管理.png)

### 1.2 Configuration Tools

- Configuration graphics drawing (preview, add, edit, custom symbols)
- Association of device information with graphic elements

![Configuration Tools](./help-en/组态工具.png)

### 1.3 IT Tools

- Batch download, batch import setting values
- Program packaging
- XML/JSON formatting
- Base conversion, temperature conversion
- Text encryption and decryption

![IT Tools](./help-en/IT小工具.png)

### 1.4 Engineering Configuration and System Settings

- Engineering configuration import, export
- System configuration, parameter configuration
- Theme customization, multi-language switching

![Engineering Configuration and System Settings](./help-en/工程配置导入和导出.png)

## 2. Main Modules

- **Menu Bar**: The default left menu bar centrally displays all function entries, including debugging, configuration, tools, settings, themes, languages, more, etc.
- **Device Debugging**: Supports multi-device connection, real-time connection to view device status, parameters, variables, device time synchronization, file upload, file download, etc.
- **Configuration Function**: Configuration graphics drawing (preview, add, edit, custom symbols), real-time display of device configuration interface information.
- **Console**: Real-time display of system logs, debugging information, error prompts, convenient for problem location.
- **Multi-language Support**: Can switch between Simplified Chinese, English and other languages in settings.
- **Theme Switching**: Can freely switch between light, dark and other themes to enhance visual experience.
- **Configuration Management**: Supports import, export, backup and recovery of engineering configuration files, convenient for project migration and collaboration.

![Tool Overall Modules](./help-en/工具整体模块.png)

## 3. Operating Environment Requirements

To ensure that VisualDebug software can run stably and efficiently, it is recommended to use the following hardware and software environment:

- **Operating System**: Windows 10 and above (64-bit recommended), supports some Linux distributions (such as Debian12+).
- **Processor**: Dual-core and above mainstream x86 architecture CPU.
- **Memory**: 4GB and above recommended, 8GB and above recommended for better experience.
- **Storage Space**: At least 500MB available disk space required for decompression and operation.
- **Display Resolution**: 1366×768 and above recommended, 1920×1080 full HD resolution recommended.
- **Network Environment**: When debugging devices, ensure that you are on the same LAN as the target device.

- **Note:**
  - It is recommended to run the software with administrator privileges to avoid configuration problems caused by insufficient permissions.
  - This tool is green software, no complex installation required, can be run directly after decompression, no need to modify the registry or environment variables of the system. All user data and configuration files are saved in the software directory, convenient for backup and migration.

## 4. Software Activation

VisualDebug software adopts local activation authorization mechanism, and activation operation needs to be completed when using it for the first time. The activation process is as follows:

![Authorization Activation](./help-en/授权激活.png)

1. **Get Machine Code**

   - After starting the software, if not activated, the activation window will automatically pop up, displaying the unique machine code of this machine.
   - You can also view the machine code in the menu bar "More" > "About" page.

2. **Apply for Activation Code**

   - Send the machine code to the software administrator or technical support personnel to apply for the activation code.

3. **Enter Activation Code**

   - Enter the received activation code in the activation window and click the "Activate" button.
   - After successful activation, the software will enter normal use state

4. **Notes**
   - The activation code corresponds to the machine code one-to-one and can only be used on the applied device. By default, activation is only required for the first time.
   - If you change computers or reinstall the system, you need to reapply for the activation code.
   - Activation information is saved in the operating system credential manager - Windows credentials - general credentials, please do not delete it at will.

![Credential Manager](./help-en/凭据管理器.png)

> If you encounter activation failure or authorization-related issues, please contact technical support for assistance.

## 5. Debugging Functions

### 5.1 Device Configuration Instructions

The device configuration function is used to manage and maintain the information of devices that need to be debugged, facilitating subsequent connection and parameter management. The main operations are as follows:

1. **Enter Device Configuration Interface**

   - Click "Debug" > "Device List" in the menu bar to enter the device management page.

2. **Add New Device**

   - Click the "Add Device" button, fill in device name, IP address, port (default 58000), whether to encrypt and other information.
   - Click "Expand Advanced Options" to configure connection timeout (default 5000 milliseconds), global request timeout (default 30000 milliseconds), setting modification timeout (default 30000 milliseconds)

3. **Edit/Delete Device**

   - In the device list, select the corresponding device and right-click the "Edit" button in the pop-up menu to modify device information.
   - Click the "Delete" button to remove unnecessary devices. Delete operation should be done with caution.

4. **Device Status and Connection**

   - The device list will display the online/offline status of devices in real time.
   - Supports one-click connect/disconnect devices, connection status will have obvious identification.

5. **Device Search and Filter**

   The device list supports **search filter** function, convenient for quickly locating target devices:

   - Enter any part of device name, IP address or port number in the input box in the upper right corner of the device management page, and the list will automatically filter matching devices.
   - Supports fuzzy search, no need to enter complete information to locate.
   - After clearing the search box, restore display of all devices.

6. **Device Expand/Collapse Function**

   - There are expand/collapse buttons in the middle of the device configuration list page, supporting **expand device** and **collapse device**, convenient for users to quickly browse or collapse device information.

![Device Configuration](./help-en/装置配置.png)

> **Tip:**
>
> - Device configuration data will be automatically saved to local configuration files and automatically loaded after restarting the software.
> - Device configuration can be exported regularly to prevent data loss.

### 5.2 Group Information Management

After successfully connecting to the device, the software will automatically display the **group information** of the device, facilitating users to manage various functions and data under the device. The group information interface is shown in the figure:

![Group Information Interface Example](./help-en/分组信息界面示例.png)

1. **Group Information Display**

   - The left area will automatically list all groups under the current device (such as setting values, status values, remote measurement, remote signaling, remote control, remote adjustment, outlet transmission, reports, etc.), each group contains corresponding function items or data points.
   - The group structure supports multi-level nesting, convenient for hierarchical management.

2. **Group Search**

   - A **search box** is provided above the group list, where you can enter group names or keywords to quickly locate target groups or function items.
   - Supports fuzzy search, no need to enter complete names to filter.

3. **Create Custom Menu**
   - Users can create custom menus according to actual needs by clicking the "New Menu" button, adding commonly used groups or function items to custom menus to improve operation efficiency.
   - When adding a new custom menu, you can configure the menu name and description. After successful creation, the menu description is displayed on the group menu.

![Create Custom Menu Interface Example](./help-en/创建自定义菜单界面示例.png)

4. **Create Custom Report**
   - Supports creating custom reports under custom menus, classifying and managing related function items.
   - When creating a new custom report, you can customize the report name, description, keywords, inherit reports, and filter inherited report data according to keywords

![Create Custom Report Interface Example](./help-en/创建自定义报告界面示例.png)

5. **Custom Menu Operations**
   - Right-click on custom menu to perform "Edit Menu", "Delete Menu" and other operations.

![Custom Menu Operation Interface Example](./help-en/自定义菜单操作界面示例.png)

> **Tip:**
>
> - Custom menus and group information will be automatically refreshed with device connection to ensure the latest group structure is displayed. By default, created group information only takes effect for this device.
> - Custom menus and groups are only saved locally and remain effective after restarting the software.

### 5.3 Device Group Information Overview

To facilitate users to quickly understand the group and data point distribution of the current device, the software provides **Device Group Information Overview** function. The interface is shown below:

![Device Group Information Overview Interface](./help-en/装置分组信息总览界面.png)

1. **Overview Card Area**

   - The left side displays the "Total Device Setting Values" of the current device in large card format, convenient for understanding the setting scale of the device at a glance.
   - The right side displays the number of various groups in multi-color cards, such as remote measurement, remote signaling, remote control, outlet transmission, etc., helping users quickly understand the distribution of various group types.

2. **Group Data Proportion Chart**
   - The right side displays the proportion of data points of each group in a ring chart (pie chart), different groups are distinguished by different colors, and the legend clearly marks the group names and quantities.
   - Hover the mouse over the chart area to display the detailed number of data points and percentage of the corresponding group.

> **Tip:**
>
> - Group information overview only counts groups and data points of currently connected devices, and will no longer be displayed after disconnection.
> - If group data is abnormal or statistics do not match, please check device configuration or contact technical support.

### 5.4 Device Basic Information

After connecting to the device, the software will automatically read and display the **basic information** of the current device, facilitating users to quickly understand the core parameters of the equipment. The device basic information interface is shown below:

![Device Basic Information Interface Example](./help-en/装置基本信息界面示例.png)

1. **Information Display Area**

   - Displays the main parameters of the device in table format, including but not limited to:
     - Serial: Device unique identifier code.
     - dev_name (Device Name): Such as BY_CIN_31K.
     - ver_time (Version Time): Firmware or configuration generation time.
     - dev_model (Device Model): Such as APEC-311MC.
     - ver_code (Version Number): Such as 07_02.
     - Checksum: Such as C62F7FC0.
     - sec_billno (Serial Number): Such as 000020.
     - msg_serial (Message Serial): Such as D0000000.

2. **Operation Instructions**

   - Click the "Export" button to export the basic information of the current device as a local file, convenient for archiving or technical support use.
   - If device information display is abnormal, please check device connection status or contact technical support.

3. **Data Source**
   - Device basic information interface corresponds to DEV_INFO menu in debug_info.xml.
   - Serial comes from the output result of cat /proc/cpuinfo | grep Serial command. Other information except Serial comes from device configuration file factoryinfo.xml.

> **Tip:**
>
> - Device basic information is only displayed when the device is successfully connected, and will be automatically hidden after disconnection.
> - Some parameters may vary slightly due to different device models, subject to the actual interface.

### 5.5 Remote Signaling Management and Operation

Remote signaling refers to switch information of device status (such as open/close, alarm, protection, etc.). In **VisualDebug**, unified management and operation can be performed. The remote signaling interface is shown below:

![Remote Signaling Management Interface Example](./help-en/遥信量管理界面示例.png)

1. **Interface Description**

   - The remote signaling list is displayed in table format, including fields such as serial number, name, description, current value, quality, etc., convenient for users to quickly browse and locate.
   - Supports fuzzy query by name, description, value and other keywords through the top filter bar to improve search efficiency.
   - Operations such as remote signaling refresh and data export can be performed through "Refresh", "Export" and other buttons.

2. **Field Description**

   - **Serial Number**: Unique number of remote signaling, convenient for sorting and searching.
   - **Name**: Identification name of remote signaling, usually corresponding to device actual points one-to-one.
   - **Description**: Brief description of the function or purpose of remote signaling.
   - **Current Value**: Real-time display of the current status of remote signaling (such as 0/1, open/close, etc.).
   - **Quality**: Indicates the validity or quality status of current remote signaling data.

3. **Common Operations**

   - Supports checking and configuring automatic refresh in remote signaling interface to ensure real-time data update.
   - Supports exporting remote signaling data to Excel and other formats, convenient for subsequent analysis and archiving.
   - Can quickly browse large amounts of remote signaling data through pagination function.

4. **Data Source**
   - Remote signaling corresponds to STATE_TABLE - YX_TABLE in debug_info.xml.
   - Remote signaling data comes from FUNCS - STATE_TABLE - YX_TABLE in device configuration file device_config.xml.
   - DCA_ST_URGENT_TABLE, DCA_ST_IMPORTANT_TABLE, DCA_ST_NORMAL_TABLE are three groups of external remote signaling groups

> **Tip:**
>
> - Remote signaling data is refreshed in real time to ensure the latest device status is displayed.
> - If remote signaling display is abnormal or no data, please check device connection and configuration.
> - Export function supports exporting results to Excel and other formats, convenient for subsequent analysis and archiving.

### 5.6 Remote Measurement Management and Operation

Remote measurement refers to analog data collected by devices (such as voltage, current, temperature, etc.). **VisualDebug** supports unified management and operation of remote measurement. The remote measurement management interface is shown below:

![Remote Measurement Management Interface Example](./help-en/遥测量管理界面示例.png)

1. **Interface Description**

   - The remote measurement list is displayed in table format, including fields such as serial number, name, description, value, quality, etc., convenient for users to intuitively view and analyze various remote measurement data.
   - Supports fuzzy query by name, description, value and other keywords through the top filter bar to improve search efficiency.
   - Operations such as remote measurement refresh and data export can be performed through "Refresh", "Export" and other buttons.

2. **Field Description**

   - **Serial Number**: Unique number of remote measurement, convenient for sorting and searching.
   - **Name**: Identification name of remote measurement, usually corresponding to device actual points one-to-one.
   - **Description**: Brief description of the function or purpose of remote measurement.
   - **Value**: Real-time display of the current value of remote measurement (such as voltage, current, etc.).
   - **Quality**: Indicates the validity or quality status of current remote measurement data.

3. **Common Operations**

   - Supports checking and configuring automatic refresh in remote measurement interface to ensure real-time data update.
   - Supports exporting remote measurement data to Excel and other formats, convenient for subsequent analysis and archiving.
   - Can quickly browse large amounts of remote measurement data through pagination function.

4. **Data Source**
   - Remote measurement corresponds to STATE_TABLE - YC_TABLE in debug_info.xml.
   - Remote measurement data comes from FUNCS - STATE_TABLE - YC_TABLE in device configuration file device_config.xml.
   - DCA_MX_TABLE is external remote measurement group.

> **Tip:**
>
> - Remote measurement data is refreshed in real time to ensure the latest analog status of the device is displayed.
> - If remote measurement display is abnormal or no data, please check device connection and related configuration.
> - Export function supports exporting results to Excel and other formats, convenient for subsequent analysis and archiving.

### 5.7 Remote Control Management and Operation

Remote control refers to remote control switch operations on devices (such as opening, closing, switching in/out, etc.). **VisualDebug** supports unified management and operation of remote control. The remote control management interface is shown below:

![Remote Control Management Interface Example](./help-en/遥控量管理界面示例.png)

1. **Interface Description**

   - The remote control list is displayed in table format, including fields such as serial number, short address, description, control open/close, control type, operation buttons, etc., convenient for users to intuitively view and operate various remote control points.
   - Supports fuzzy query by short address, description and other keywords through the top filter bar to improve search efficiency.
   - Operations such as remote control refresh and data export can be performed through "Refresh", "Export" and other buttons.
   - Each row can directly perform remote control operations (select control open, control close, etc., select type such as no check, check synchronization, check no voltage, etc.), then can perform direct control or select control.

2. **Field Description**

   - **Serial Number**: Unique number of remote control, convenient for sorting and searching.
   - **Short Address**: Identification name of remote control, usually corresponding to device actual points one-to-one.
   - **Description**: Brief description of the function or purpose of remote control.
   - **Control Open/Close**: Displays executable remote control operation types (such as control open, control close, etc.).
   - **Type**: Select control type (such as no check, check synchronization, check no voltage, etc.).
   - **Operation**: Contains remote control execution buttons (such as "Direct Control", "Select Control", etc.), supports sending remote control commands.

3. **Data Source**
   - Remote control corresponds to CTRL_TABLE - YK_TABLE in debug_info.xml.
   - Remote control data comes from FUNCS - CTRL_TABLE - YK_TABLE in device configuration file device_config.xml.

> **Tip:**
>
> - Remote control operations involve actual device actions, please be sure to confirm the operation object and command to avoid misoperation.
> - If remote control display is abnormal or operation has no response, please check device connection, enable switch permission configuration and related safety settings.
> - Export function supports exporting results to Excel and other formats, convenient for subsequent analysis and archiving.

### 5.8 Remote Adjustment Management and Operation

Remote adjustment refers to the function of remotely adjusting device parameters (such as remote modification of rate values, thresholds, etc.). **VisualDebug** supports unified management and operation of remote adjustment. The remote adjustment management interface is shown below:

![Remote Adjustment Management Interface Example](./help-en/遥调量管理界面示例.png)

1. **Interface Description**

   - The remote adjustment list is displayed in table format, including fields such as serial number, short address, description, value, operation buttons, etc., convenient for users to intuitively view and adjust various remote adjustment parameters.
   - Supports fuzzy query by short address, description and other keywords through the top filter bar to improve search efficiency.
   - Operations such as remote adjustment refresh and data export can be performed through "Refresh", "Export" and other buttons.
   - Each row can directly perform remote adjustment operations (such as inputting new setting values, clicking "Select Control" or "Direct Control" buttons to send).

2. **Field Description**

   - **Serial Number**: Unique number of remote adjustment, convenient for sorting and searching.
   - **Short Address**: Identification name of remote adjustment, usually corresponding to device actual points one-to-one.
   - **Description**: Brief description of the function or purpose of remote adjustment.
   - **Value**: Users can input new setting values, ready to send to the device.
   - **Operation**: Contains remote adjustment execution buttons (such as "Select Control" or "Direct Control"), supports sending remote adjustment commands.

3. **Data Source**
   - Remote adjustment corresponds to CTRL_TABLE - YT_TABLE in debug_info.xml.
   - Remote adjustment data comes from FUNCS - CTRL_TABLE - YT_TABLE in device configuration file device_config.xml.

> **Tip:**
>
> - Remote adjustment operations involve device parameter changes, please be sure to confirm setting values and operation objects to avoid misoperation.
> - If remote adjustment display is abnormal or operation has no response, please check device connection, enable switch permission configuration and related safety settings.
> - Export function supports exporting results to Excel and other formats, convenient for subsequent analysis and archiving.

### 5.9 Outlet Transmission Management and Operation

The outlet transmission function is used to remotely monitor and operate the switch status of device outlets, facilitating users to uniformly manage and control the operating status of each outlet. The interface is shown below:

![Outlet Transmission Management Interface Example](./help-en/出口传动管理界面示例.png)

1. **Interface Description**

   - The outlet transmission list is displayed in table format, including fields such as serial number, short address, description, operation, etc., convenient for users to intuitively view and operate each outlet point.
   - Supports fuzzy query by short address, description and other keywords through the top filter bar to improve search efficiency.
   - Data refresh can be performed through the "Refresh" button to ensure real-time outlet status.
   - Each row can directly perform outlet operations (such as "Action"), operation buttons are intuitive and clear.

2. **Field Description**

   - **Serial Number**: Unique number of outlet transmission point, convenient for sorting and searching.
   - **Short Address**: Identification name of outlet transmission point, usually corresponding to device actual points one-to-one.
   - **Description**: Brief description of the function or purpose of outlet transmission point.
   - **Operation**: Contains operation buttons such as "Action", supports remote control of outlets.

3. **Data Source**
   - Outlet transmission corresponds to CTRL_TABLE - DRIVE_TABLE in debug_info.xml.
   - Outlet transmission data comes from FUNCS - CTRL_TABLE - DRIVE_TABLE in device configuration file device_config.xml.

> **Tip:**
>
> - Outlet transmission operations involve actual device actions, please be sure to confirm the operation object and command to avoid misoperation.
> - If outlet transmission status display is abnormal or operation has no response, please check device connection, enable switch permission configuration and related safety settings.

## 6. Configuration Functions

### 6.1 Purpose

This chapter describes the operation instructions of the configuration tool, describing the operation process steps for users, facilitating users to quickly understand and use the configuration tool.

### 6.2 Location

Open the tool, left navigation bar - [Configuration]

![Configuration Function Location Interface](./help-en/组态功能位置界面.png)

### 6.3 Interface Layout

- Upper left: Display device list and debug list common, device lists on both sides reuse one data.
- Lower left: Configuration list, right-click supports operations such as add, edit, delete, rename. Left-click displays configuration interface.
- Right side: Display configuration interface

### 6.4 Device List

Configuration monitoring device data requires connecting devices, upper left displays device list.

#### 6.4.1 Add

The [+] in the upper right corner can add device information.

![Add Device Interface](./help-en/新增装置界面.png)

#### 6.4.2 Operations

Select a device in the device list, right-click to select corresponding operations. Operations here share data with debugging, deletion will also adjust the device list on the debugging side.

![Device List Operation Interface](./help-en/装置列表操作界面.png)

### 6.5 Configuration List

The lower left interface displays configuration information in tree form, root node configuration monitoring. Configurations are classified by project, so you need to add projects first, then add configurations under projects.

#### 6.5.1 Add Project

Right-click [Device Monitoring] node, select add project.
![Add Project Interface](./help-en/新增项目界面.png)

Enter project name, click [OK].
![Add Project Interface 2](./help-en/新增项目界面2.png)

A new project node appears under the device monitoring node.
![Add Project Interface 3](./help-en/新增项目界面3.png)

#### 6.5.2 Add Configuration

Configurations belong to different projects, need to select project node, right-click select add.
![Add Configuration Interface 1](./help-en/新增组态界面1.png)

Enter configuration name, click [OK], configuration name node appears under project node.
![Add Configuration Interface 2](./help-en/新增组态界面2.png)

#### 6.5.3 Edit Configuration

After adding configuration, it's a blank interface, need to add symbols to become configuration interface. Right-click [Device Data Monitoring], select [Edit Configuration].
![Edit Configuration Interface 1](./help-en/编辑组态界面1.png)

Specific operations refer to [Configuration Drawing] chapter.
![Edit Configuration Interface 2](./help-en/编辑组态界面2.png)

#### 6.5.4 Delete Configuration

Select configuration node under project, right-click select [Delete].

## 7. IT Tools

This chapter introduces 6 built-in IT tools in the software, helping users efficiently complete common data conversion and auxiliary operations in debugging, data processing, engineering management and other scenarios.

### 7.1 Batch Download

**Function Introduction:**
Batch download tool integrates three major function modules: device list configuration, download file configuration, parameter setting configuration, supports batch download and import of files and setting parameters from multiple devices, greatly improving engineering file management and archiving efficiency.

**Function Structure and Main Operation Instructions:**

1. **Device List Configuration**

   - Displays all operable devices, supports batch selection, addition, deletion, sorting and other operations.
   - Can set connection information, port, whether to encrypt, whether to participate in file download and setting import and other parameters for each device.
   - Supports one-click select all/deselect all, batch import and export device information.

2. **Download File Configuration**

   - Displays file directories of selected devices, supports multi-file batch selection, import, export, deletion, sorting and other operations.
   - Displays detailed information such as file name, size, path, last modification time.
   - Supports setting local save path, all downloaded files will be automatically saved to specified directory.
   - Supports one-click import/export file list, convenient for batch task management and recovery.

3. **Parameter Setting Configuration**
   - Displays all setting parameters of devices, supports group filtering, keyword search, batch selection, import, export and other operations.
   - Displays detailed information such as parameter name, description, minimum value, maximum value, step, unit, current value.
   - Supports batch export of setting parameters, convenient for archiving and subsequent batch configuration.

**Typical Application Scenarios:**

- Batch archiving of configuration files and setting parameters of multiple devices after engineering debugging.
- Unified export backup of setting parameters of multiple devices, convenient for project management and data analysis.
- Quick recovery or migration of device configurations, improving engineering efficiency.

### 7.2 Program Packaging Function

**Function Introduction:**
Program packaging tool is used to batch compress multiple local files into zip files, convenient for subsequent unified download to device specified directories, achieving batch deployment, upgrade or migration. Supports flexible selection of files and folders, convenient and efficient operation.

![Program Packaging Function Interface](./help-en/程序打包界面示例.png)

**Main Operation Instructions:**

- Click "Select Files" button, pop up file selection window, supports multi-selection, add selected files or folders to packaging list.
- File list can view detailed information such as file name, size, path, last modification time.
- Supports single file removal, clear all, flexibly adjust packaging content.
- Click "Package" button, compress all files in current list into one zip file, automatically named.

**Typical Application Scenarios:**

- Engineering site batch deployment, upgrade of multiple program files.
- Multi-file unified packaging then download to device, improving operation efficiency.
- When need to overall migrate, archive or backup multiple related files.

### 7.3 XML Formatting

**Function Introduction:**
XML formatting tool can quickly beautify original XML strings into clear, easy-to-read format, supports custom indentation and formatting preview, convenient for data viewing and editing.

![XML Formatting Function Example](./help-en/XML格式化功能示例.png)

**Main Operation Instructions:**

- Paste or input original XML content in "XML to be formatted" input box.
- Can choose whether to collapse display through "Collapse Content" switch, adjust "Indent Size" to customize spaces for each level indentation.
- After clicking format button, "Formatted XML" area below automatically displays beautified XML content.
- Supports one-click copy formatting results, convenient for subsequent use.

**Typical Application Scenarios:**

- View, edit and beautify XML files such as device configuration, debug logs.
- Process XML data exported from third-party systems, improve data readability and troubleshooting efficiency.

### 7.4 JSON Formatting

**Function Introduction:**
JSON formatting tool can quickly beautify original JSON strings into clear, easy-to-read format, supports dictionary sorting, custom indentation and formatting preview, convenient for data viewing, debugging and editing.

![JSON Formatting Function Example](./help-en/JSON格式化功能示例.png)

**Main Operation Instructions:**

- Paste or input original JSON content in "JSON to be formatted" input box.
- Can choose whether to sort JSON key names through "Dictionary Sort" switch, adjust "Indent Size" to customize spaces for each level indentation.
- "Formatted JSON" area below automatically displays beautified JSON content.
- Supports one-click copy formatting results, convenient for subsequent use.

**Typical Application Scenarios:**

- View, edit and beautify JSON data such as interface returns, configuration files.
- Process JSON content exported from third-party systems, improve data readability and troubleshooting efficiency.

### 7.5 Base Conversion

**Function Introduction:**
Base conversion tool supports quick conversion of values between different bases (such as binary, octal, decimal, hexadecimal, Base64 and custom bases), suitable for various scenarios such as protocol debugging, data analysis.

![Base Conversion Function Example](./help-en/进制转换功能示例.png)

**Main Operation Instructions:**

- Input values of any base (such as decimal, hexadecimal, etc.) in "Number to be converted" input box.
- Tool will automatically convert input values in real time to common bases such as binary, octal, decimal, hexadecimal, Base64, and display results in corresponding fields.
- Supports custom base conversion, can set target base through custom base input box below, automatically display conversion results.
- All results can be directly copied, convenient for subsequent use.

**Typical Application Scenarios:**

- Protocol debugging, register address analysis, message content base conversion.
- Multi-base conversion and verification of device parameters, engineering data.

### 7.6 Temperature Conversion

**Function Introduction:**
Temperature conversion tool supports quick conversion between multiple common and professional temperature scales (such as Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur, Rømer), meeting needs of engineering, scientific research and other scenarios.

![Temperature Conversion Function Example](./help-en/温度转换功能示例.png)

**Main Operation Instructions:**

- Input temperature values in any temperature scale input box, tool will automatically calculate and synchronously display conversion results of all other temperature scales.
- Supports conversion between common temperature scales (K, ℃, ℉, °R) and professional temperature scales (°De, °N, °Ré, °Rø).
- All results can be directly copied, convenient for subsequent use.

**Typical Application Scenarios:**

- Device temperature parameter debugging and verification.
- Multi-temperature scale unit conversion in engineering sites, scientific experiments.

### 7.7 Text Encryption and Decryption

**Function Introduction:**
Text encryption and decryption tool supports multiple mainstream encryption algorithms (such as AES, TripleDES, Rabbit, RC4, etc.), can quickly encrypt and decrypt text data, suitable for sensitive information protection, technical support and other scenarios.

**Supported Algorithms:**
Currently the tool supports the following mainstream encryption algorithms, users can choose according to actual needs:

- **AES** (Advanced Encryption Standard)
- **TripleDES** (Triple Data Encryption Standard)
- **Rabbit** (Stream Cipher Algorithm)
- **RC4** (Stream Cipher Algorithm)

![Text Encryption and Decryption Function Example](./help-en/文本加解密功能示例.png)

**Main Operation Instructions:**

- Encryption: Input plaintext content in "Text to be encrypted" input box, fill in key, select encryption algorithm, system automatically generates encrypted ciphertext.
- Decryption: Input ciphertext content in "Text to be decrypted" input box, fill in key, select decryption algorithm, system automatically restores plaintext content.
- Supports multiple encryption and decryption algorithm selection, result area can be directly copied, convenient for subsequent use.

**Typical Application Scenarios:**

- Encrypted storage and transmission of sensitive parameters, configuration files.
- Quick decryption and verification of encrypted information during technical support, problem troubleshooting.

## 8. More Functions

Click the "More" button at the bottom of the left menu bar to quickly access the following auxiliary functions, improving software usability and extensibility:

![More Functions Example Description](./help-en/更多功能示例说明.png)

### 8.1 Import Engineering Configuration

Supports importing exported tool configuration files into current tool software environment. Can select configuration type (such as all, partial), and specify import file path through file selector. After import, related configurations will be automatically applied to current project, convenient for project migration and batch configuration.

**Operation Steps:**

1. Click "Import Engineering Configuration" option under "More" menu
2. Select configuration file to import in pop-up file selector
3. Select import type (all configuration or partial configuration)
4. Confirm import, system will automatically apply configuration to current environment

**Notes:**

- It is recommended to backup current configuration before import to avoid data loss
- Please do not close software or perform other operations during import
- If import fails, please check file format and content integrity

![Import Engineering Configuration Function Example](./help-en/导入工程配置功能示例.png)

### 8.2 Export Engineering Configuration

Supports exporting all or part of current engineering configuration as files, convenient for backup, archiving or migration to other environments. Can select export directory, generated configuration files after export can be used for subsequent import operations.

**Operation Steps:**

1. Click "Export Engineering Configuration" option under "More" menu
2. Select export type (all configuration or partial configuration)
3. Select export directory
4. Confirm export, system will generate configuration files

**Export Content:**

- All
- Device List
- Configuration List

**Use Scenarios:**

- Project backup and archiving
- Configuration migration to other environments
- Team collaboration configuration sharing
- Technical support problem troubleshooting

![Export Engineering Configuration Function Example](./help-en/导出工程配置功能示例.png)

### 8.3 Screenshot Function

One-click capture of current software interface, convenient for saving debugging process, problem feedback or technical support. Screenshot files can be copied to clipboard or saved to specified directory, supports subsequent viewing and sharing.

**Function Features:**

- Supports full screen screenshot and area screenshot
- Supports copy to clipboard or save to specified directory

**Operation Steps:**

1. Click "Screenshot" option under "More" menu
2. Select screenshot mode (full screen or area)
3. If area screenshot is selected, drag to select screenshot area

**Application Scenarios:**

- Debugging process recording
- Problem feedback and technical support
- Operation step documentation
- Interface design reference

**Notes:**

- Please ensure interface display is complete before screenshot
- It is recommended to hide sensitive information when taking screenshots
- Screenshot files will occupy disk space, regular cleanup is recommended

### 8.4 Menu Search

Supports quick search by menu name or path, convenient for quickly locating required function entries when there are many functions. After inputting keywords, system automatically filters matching menu items, improving operation efficiency.

**Search Functions:**

- Supports fuzzy search and exact search
- Real-time display of search results
- Search results highlight display

**Operation Steps:**

1. Click "Menu Search" option under "More" menu
2. Input keywords in search box
3. System automatically displays matching menu items
4. Click search results to directly jump to corresponding function

![Menu Search Function Example](./help-en/菜单搜索功能示例.png)

### 8.5 Help Documentation

Open this help documentation, view software function descriptions, operation guides, FAQ and other content, convenient for beginners to quickly get started and self-help find solutions when encountering problems.

**Documentation Content:**

- Software function introduction and overview
- Detailed operation step instructions
- Frequently Asked Questions (FAQ)
- Troubleshooting guide
- Best practice recommendations

**Usage Methods:**

- Online viewing: Click "Help" option under "More" menu
- Offline viewing: Help documentation is integrated in software
- Search function: Supports keyword search for quick location
- Directory navigation: Quick jump to specified chapters through directory

**Applicable Users:**

- New users quick start
- Daily use reference
- Problem troubleshooting and resolution
- Function in-depth learning and research

![Help Function Example](./help-en/帮助功能示例.png)

### 8.6 About Information

View basic information of software, including tool name, version number, machine code, etc. Can be used for software registration, technical support and version management.

**Display Information:**

- Software name and version number
- Copyright information and company information
- Machine code (used for software activation)
- Build time and version identification

**Main Uses:**

- Software version confirmation
- Activation code application (requires machine code)

**Operation Instructions:**

1. Click "About" option under "More" menu
2. View software detailed information
3. Copy machine code for activation application

**Notes:**

- Machine code is unique identifier for software activation
- Please properly keep machine code information
- Version information helps problem troubleshooting

![About Function Example](./help-en/关于功能示例.png)

> **Tip:**
>
> - Functions under "More" menu are auxiliary tools, aimed at improving software flexibility and user experience.
> - Import and export operations are recommended to be performed regularly to prevent data loss.
> - If function abnormalities or operation questions occur, please refer to help documentation or contact technical support.
> - Regular backup of important configurations and screenshot files is recommended.
> - Reasonable use of search function can significantly improve operation efficiency.

## 9. Frequently Asked Questions (FAQ)

- **Q: Group menu display is incorrect after device connection?**
  - Check if urpc in device shr directory is updated to tool supporting version.
  - Read debug_info.xml in shr directory and upload locally, contact technical support for problem location.
- **Q: Unable to connect device?**
  - Check device power and network connection, ensure on same LAN as computer.
  - Check firewall settings, ensure software has network access permission.
- **Q: Configuration import failed?**
  - Please confirm imported configuration file format is correct and supported by **VisualDebug**.
- **Q: Encrypted Excel files cannot be imported?**
  - Due to IT policy impact, encrypted Excel cannot be recognized. If need to import encrypted Excel files, please decrypt first.
- **Q: Software interface display abnormal?**
  - Try switching themes or restarting software, if problem persists please contact technical support.
- **Q: Setting values with differences not displayed after setting import?**
  - Please confirm if setting group names in imported setting file Excel/xml/csv are completely consistent with group names on tool group menu. Inconsistency cannot compare differences.
- **Q: Which directory should packaged files from program packaging function be downloaded to?**
  - Packaged zip is in encrypted format, needs to be downloaded to device /dwld directory, restart required after download completion to take effect.
- **Q: Why doesn't restart take effect when download completion restart is checked in file download interface?**
  ![Restart Failed](./help-en/重启失败.png)
  - Restart has requirements for CPU board firmware. If ls -l /sbin/reboot shows soft link as above image, reboot is not supported, firmware upgrade required.

## 10. Technical Support

If you encounter problems that cannot be resolved, please contact us through the following methods:

- **Company**: Sieyuan Electric Co., Ltd.
- **Department**: Central Research Institute - Embedded Application Development Department
- **Professional Group**: Tool Software Development Group

Thank you for using this software, wish you smooth debugging!
