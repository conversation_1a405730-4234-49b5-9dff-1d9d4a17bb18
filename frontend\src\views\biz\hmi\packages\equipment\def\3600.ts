import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "3600",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.83,
        y1: 29,
        x2: 4.83,
        y2: 36
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.5,
        y1: 0,
        x2: 4.67,
        y2: 23
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(0, 8.34, 9.33, 21.17)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.67,
        y1: 23.67,
        x2: 1.67,
        y2: 17.67
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 4.67,
        y1: 23.67,
        x2: 7.33,
        y2: 17.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
