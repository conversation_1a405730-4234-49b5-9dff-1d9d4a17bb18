import { ReqPage } from "@/api/interface";

export namespace Module {
  /**模块分页查询 */
  export interface Page extends ReqPage {
    title: string;
  }

  /** 单页信息 */
  export interface ModuleInfo {
    /** id */
    id: number | string;
    /** 菜单名称 */
    title: string;
    /** 菜单描述 */
    description: string;
    /** 菜单图标 */
    icon: string;
    /** 状态 */
    status: string;
    /** 排序 */
    sortCode: number;
    /** 创建时间 */
    createTime: string;
  }
}
