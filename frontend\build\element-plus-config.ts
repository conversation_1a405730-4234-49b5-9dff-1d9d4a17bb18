/**
 * Element Plus 优化配置
 * 用于管理 Element Plus 组件的预构建和样式导入策略
 */

/**
 * Element Plus 核心依赖列表
 * 这些依赖会被预构建以提升开发体验
 */
export const elementPlusCoreIncludes = [
  // Element Plus 核心库
  "element-plus",
  "element-plus/es",
  "@element-plus/icons-vue"
];

/**
 * Element Plus 组件样式路径
 * 由于项目已在 main.ts 中导入完整样式，这里仅作为参考
 * 如果需要按需导入样式，可以将这些路径添加到 optimizeDeps.include 中
 */
export const elementPlusStylePaths = [
  // 表单组件
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/slider/style/css",
  "element-plus/es/components/rate/style/css",
  "element-plus/es/components/color-picker/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/time-picker/style/css",
  "element-plus/es/components/upload/style/css",

  // 数据展示组件
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/progress/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/card/style/css",
  "element-plus/es/components/collapse/style/css",
  "element-plus/es/components/timeline/style/css",
  "element-plus/es/components/divider/style/css",
  "element-plus/es/components/image/style/css",
  "element-plus/es/components/calendar/style/css",

  // 导航组件
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/tabs/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/page-header/style/css",
  "element-plus/es/components/steps/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",

  // 反馈组件
  "element-plus/es/components/alert/style/css",
  "element-plus/es/components/loading/style/css",
  "element-plus/es/components/message/style/css",
  "element-plus/es/components/message-box/style/css",
  "element-plus/es/components/notification/style/css",
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/popover/style/css",
  "element-plus/es/components/popconfirm/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/result/style/css",

  // 布局组件
  "element-plus/es/components/container/style/css",
  "element-plus/es/components/header/style/css",
  "element-plus/es/components/aside/style/css",
  "element-plus/es/components/main/style/css",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/space/style/css",

  // 其他组件
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/button-group/style/css",
  "element-plus/es/components/link/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/scrollbar/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/avatar/style/css",
  "element-plus/es/components/empty/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/skeleton-item/style/css",
  "element-plus/es/components/affix/style/css",
  "element-plus/es/components/anchor/style/css",
  "element-plus/es/components/anchor-link/style/css"
];

/**
 * Element Plus 自动导入解析器配置
 */
export const elementPlusResolverConfig = {
  // 禁用样式自动导入，因为已在 main.ts 中导入完整样式
  importStyle: false,
  // 指定解析的库名
  resolveIcons: true,
  // 版本检测
  version: "^2.5.6"
};

/**
 * 获取 Element Plus 预构建配置
 * @param useStyleIncludes 是否包含样式路径（当不使用完整样式导入时）
 * @returns 预构建包含列表
 */
export function getElementPlusIncludes(useStyleIncludes = false): string[] {
  if (useStyleIncludes) {
    return [...elementPlusCoreIncludes, ...elementPlusStylePaths];
  }
  return elementPlusCoreIncludes;
}

/**
 * 检查是否为 Element Plus 相关模块
 * @param id 模块ID
 * @returns 是否为 Element Plus 模块
 */
export function isElementPlusModule(id: string): boolean {
  return id.includes("element-plus") || id.includes("@element-plus");
}

/**
 * 获取 Element Plus 模块的 chunk 名称
 * @param id 模块ID
 * @returns chunk 名称
 */
export function getElementPlusChunkName(id: string): string {
  if (id.includes("@element-plus/icons-vue")) {
    return "element-icons";
  }
  if (id.includes("element-plus/es/components")) {
    return "element-components";
  }
  if (id.includes("element-plus")) {
    return "element-core";
  }
  return "element-vendor";
}

/**
 * Element Plus 优化建议
 */
export const elementPlusOptimizationTips = {
  // 样式导入策略
  styleStrategy: {
    full: {
      description: "导入完整样式文件（推荐）",
      import: 'import "element-plus/dist/index.css"',
      pros: ["无需配置", "避免样式丢失", "开发体验好"],
      cons: ["包体积稍大", "无法tree-shaking样式"]
    },
    onDemand: {
      description: "按需导入样式",
      import: "通过 unplugin-element-plus 自动导入",
      pros: ["包体积小", "支持tree-shaking"],
      cons: ["配置复杂", "可能遗漏样式"]
    }
  },

  // 组件导入策略
  componentStrategy: {
    auto: {
      description: "自动导入组件（推荐）",
      config: "unplugin-vue-components + ElementPlusResolver",
      pros: ["无需手动导入", "支持tree-shaking", "开发体验好"],
      cons: ["需要配置解析器"]
    },
    manual: {
      description: "手动导入组件",
      import: 'import { ElButton } from "element-plus"',
      pros: ["完全可控", "包体积最小"],
      cons: ["开发效率低", "容易遗漏"]
    }
  }
};

export default {
  elementPlusCoreIncludes,
  elementPlusStylePaths,
  elementPlusResolverConfig,
  getElementPlusIncludes,
  isElementPlusModule,
  getElementPlusChunkName,
  elementPlusOptimizationTips
};
