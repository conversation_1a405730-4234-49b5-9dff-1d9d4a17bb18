<template>
  <div class="header">
    <GraphTools v-if="form.toolShow" :graph="graph" @equipment-list="onEquipmentList"></GraphTools>
  </div>
  <div class="layout">
    <div id="left">
      <GraphComponent @add-component="addComponent" @save="onSave"></GraphComponent>
    </div>
    <div id="content" class="container">
      <div style="width: 100%; height: 100%">
        <div id="canvas" ref="canvas" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div id="right">
      <GraphProperties></GraphProperties>
    </div>
  </div>
  <el-dialog v-model="form.equipmentListShow" :close-on-click-modal="false" :destroy-on-close="true">
    <EquipmentList :data="form.equipmentlist" @copy="onEquipmentCopy" @delete="onEquipmentDelete"></EquipmentList>
  </el-dialog>
  <el-dialog
    v-model="form.statusShow"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="t('graphDefine.contextMenu.setStatus')"
    :width="400"
  >
    <SetStatus :value="form.statusValue" @change="onStatusChange"></SetStatus>
  </el-dialog>
</template>
<script lang="ts">
export default {
  name: "GraphDefine"
};
</script>
<script setup lang="ts">
import Split from "split.js";
import { GraphProperties } from "../../..//graph-properties";
import GraphTools from "./GraphTools.vue";
import GraphComponent from "./GraphComponent.vue";
import EquipmentList from "./EquipmentList.vue";
import SetStatus from "./SetStatus.vue";
import { Graph, Node } from "@antv/x6";
import { onMounted, ref } from "vue";
import GraphUsePlugin from "../../..//graph/GraphUsePlugin";
import GraphEvent from "../../..//graph/GraphEvent";
import { Dnd } from "@antv/x6-plugin-dnd";
import {
  EquipmentData,
  EventType,
  EventTypeParams,
  EventTypeParamsName,
  EquipmentType,
  ContextMenuItem,
  ContextMenuItemType,
  EquipmentStatus,
  DataInfo
} from "../../../graph/Graph";
import { provide } from "vue";
import GraphCreate from "./GraphCreate";
import { ElMessageBox } from "element-plus";
import GraphContextMenuManager from "./GraphContextMenuManager";
import GrapgGroup from "../../../graph/GraphGroup";
import { equipmentDataToCell, getCellStatus, getSelectedCells, setCellStatus } from "../../../graph/GraphUtil";
import GraphOperator from "../../../graph/GraphOperator";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
// 定义事件
const emit = defineEmits<{
  (e: "get"): void;
  (e: "save", data: EquipmentData): void;
  (e: "delete", data: EquipmentData): void;
}>();
//

// 定义方法
defineExpose({
  equipmentListShow: (datas: EquipmentData[]) => {
    equipmentListShow(datas);
  }
});
const canvas = ref();
// 注册事件提供
const graphPropertiesProvide = ref<EventTypeParams>({
  type: EventType.NONE,
  eventParam: {}
});
provide(EventTypeParamsName, graphPropertiesProvide);
// 定义表单属性
const form = ref<{
  toolShow: boolean;
  equipmentListShow: boolean;
  statusShow: boolean;
  statusValue: EquipmentStatus;
  equipmentlist: EquipmentData[];
  edit: EquipmentData | undefined;
}>({
  toolShow: false,
  equipmentListShow: false,
  statusShow: false,
  statusValue: EquipmentStatus.NONE,
  equipmentlist: [],
  edit: undefined
});
let graph: Graph;
let dnd: Dnd;
const contextMenuManager = new GraphContextMenuManager(t);
onMounted(async () => {
  console.log("GraphDefine: onMounted called");
  console.log("GraphDefine: canvas.value:", canvas.value);

  Split(["#left", "#content", "#right"], {
    gutterSize: 6,
    sizes: [15, 65, 20]
  });

  graph = new Graph({
    container: canvas.value as HTMLElement,
    grid: {
      visible: true,
      size: 10,
      type: "mesh"
    },
    background: {
      color: "#fff"
    },
    translating: {
      restrict: true
    },

    autoResize: true
  });
  console.log("GraphDefine: Graph created:", graph);
  form.value.toolShow = true;
  console.log("GraphDefine: toolShow set to true, form.value:", form.value);
  // 加载插件
  const dataInfo: DataInfo = {
    operator: new GraphOperator(),
    group: new GrapgGroup(graph)
  };
  const grapgUsePlugin = new GraphUsePlugin(graph);
  grapgUsePlugin.use(dataInfo);
  dnd = grapgUsePlugin.dnd;
  // 处理事件
  new GraphEvent(
    graph,
    {
      create: (graph: Graph) => contextMenuManager.createContextMenu(graph),
      trigger: contextMenuEvent
    },
    emitEvent
  );
});
const addComponent = (event: MouseEvent, nodeMeta: Node.Metadata) => {
  if (!graph) {
    console.log(t("graphDefine.graphDefine.waitCanvasInit"));
    return;
  }
  const node = graph.createNode(nodeMeta);
  dnd.start(node, event);
};

// 画布事件
const emitEvent = (args: EventTypeParams) => {
  switch (args.type) {
    case EventType.BLANK_CLICK:
    case EventType.NODE_CLICK:
      graphPropertiesProvide.value = {
        ...args
      };
      break;
    default:
      break;
  }
};
const contextMenuEvent = (item: ContextMenuItem, graph: Graph, graphGroup: GrapgGroup) => {
  switch (item.type) {
    case ContextMenuItemType.EQUIPMENT_STATUS:
      onSetStatus();
      break;
    default:
      contextMenuManager.trigger(item, graph, graphGroup);
      break;
  }
};
/**
 * 设置设备状态
 */
const onSetStatus = () => {
  const cells = getSelectedCells(graph);
  if (cells.length != 1) {
    ElMessageBox.alert(t("graphDefine.graphDefine.selectOneGraph"), {
      title: t("graphDefine.graphDefine.tip"),
      type: "warning"
    });
    return;
  }
  let value = getCellStatus(cells[0].value);
  if (value == undefined) {
    value = EquipmentStatus.NONE;
  }
  form.value.statusValue = value;
  form.value.statusShow = true;
};
/**
 * 设置图符状态
 * @param status
 */
const onStatusChange = (status: EquipmentStatus) => {
  const cells = getSelectedCells(graph);
  if (cells.length == 1) {
    setCellStatus(cells[0].value, status);
    form.value.statusShow = false;
  }
};

// 触发get事件，查询设备信息
const onEquipmentList = () => {
  // 查询数据
  emit("get");
};
/**
 * 显示设备管理
 */
const equipmentListShow = (datas: EquipmentData[]) => {
  console.log("GraphDefine: equipmentListShow called with data:", datas);
  console.log("GraphDefine: form.value before:", form.value);
  form.value.equipmentlist = datas;
  form.value.equipmentListShow = true;
  console.log("GraphDefine: form.value after:", form.value);
};
const onEquipmentCopy = (data: EquipmentData) => {
  graph.clearCells();
  const cells = equipmentDataToCell(data, graph);
  if (cells) {
    cells.forEach(item => {
      graph.addCell(item);
    });
  }
  form.value.equipmentListShow = false;
};

const onEquipmentDelete = (data: EquipmentData) => {
  emit("delete", data);
};
// 触发save事件
const onSave = async (data: { name: string; type: EquipmentType }) => {
  // 获取画布数据
  const graphCreate = new GraphCreate(graph, t);
  const result = await graphCreate.create(data);
  if (!result.isSuccess()) {
    ElMessageBox.alert(result.msg, {
      title: t("graphDefine.graphDefine.tip"),
      type: "warning"
    });
    return;
  }
  emit("save", result.data);
};
</script>
<style>
@import "../../../styles/Graph.css";
.layout {
  display: flex;
}
.container {
  height: calc(100vh - 40px);
}
.gutter {
  background-color: #eeeeee;
  background-repeat: no-repeat;
  background-position: 50%;
}
.gutter.gutter-horizontal {
  cursor: col-resize;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==");
}

/** 文本编辑器定位问题 */
.x6-cell-tool-editor {
  position: absolute;
}
</style>
./GraphEvent
