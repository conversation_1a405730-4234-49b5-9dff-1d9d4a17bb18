<!-- 横向布局 -->
<template>
  <el-container class="layout">
    <el-header>
      <div class="logo flx-space-around">
        <img class="logo-img" :src="sysInfo.SYS_LOGO" alt="logo" />
        <span class="company-name-en">{{ $t("layout.header.company.englishName") }}</span>
        <div class="logo-mark">®</div>
        <span class="company-name-cn">&nbsp;{{ $t("layout.header.company.name") }}</span>
      </div>
      <el-menu mode="horizontal" :router="false" :default-active="activeMenu">
        <!-- 不能直接使用 SubMenu 组件，无法触发 el-menu 隐藏省略功能 -->
        <template v-for="subItem in menuList" :key="subItem.path">
          <el-sub-menu v-if="subItem.children?.length" :key="subItem.path" :index="subItem.path + 'el-sub-menu'">
            <template #title>
              <svg-icon :icon="subItem.meta.icon" class="el-icon" />
              <span>{{ $t(subItem.meta.title) }}</span>
            </template>
            <SubMenu :menu-list="subItem.children" />
          </el-sub-menu>
          <el-menu-item v-else :key="subItem.path + 'el-menu-item'" :index="subItem.path" @click="handleClickMenu(subItem)">
            <svg-icon :icon="subItem.meta.icon" class="el-icon" />
            <template #title>
              <span>{{ $t(subItem.meta.title) }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
      <ToolBarDrag />
      <div class="other flx-justify-between">
        <GlobalSetting id="globalSetting" v-model="place" />
        <ThemeQuickSwitch id="themeQuickSwitch" v-model="place" />
        <Language id="language" v-model="place" />
        <MoreInfo id="moreInfo" v-model="place" />
      </div>
      <ToolBarRight />
    </el-header>
    <Main />
  </el-container>
</template>

<script setup lang="ts" name="layoutTransverse">
import { computed } from "vue";
import { useAuthStore, useConfigStore } from "@/stores/modules";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import Main from "@/layouts/components/Main/index.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import ThemeQuickSwitch from "@/layouts/components/Header/components/ThemeQuickSwitch.vue";
import GlobalSetting from "@/layouts/components/Header/components/GlobalSetting.vue";
import Language from "@/layouts/components/Header/components/Language.vue";
import MoreInfo from "@/layouts/components/Header/components/MoreInfo.vue";
import ToolBarDrag from "@/layouts/components/Header/ToolBarDrag.vue";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const configStore = useConfigStore();
const menuList = computed(() => authStore.showMenuListGet);
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const sysInfo = computed(() => configStore.sysBaseInfoGet);
const place: string = "bottom";
useI18n();
const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
