<script setup lang="ts">
import { useVModel } from "@vueuse/core";
import { useCopy } from "@/composable/copy";
import { CopyDocument } from "@element-plus/icons-vue";
const props = defineProps<{ value: string }>();
const emit = defineEmits(["update:value"]);

const value = useVModel(props, "value", emit);
const { copy, isJustCopied } = useCopy({ source: value, createToast: true });
const tooltipText = computed(() => (isJustCopied.value ? "Copied!" : "复制"));
</script>

<template>
  <c-input-text v-model:value="value">
    <template #suffix>
      <c-tooltip :tooltip="tooltipText">
        <c-button circle variant="text" size="small" @click="copy()">
          <el-tooltip content="复制" popper-class="is-small" transition="none" effect="light" placement="bottom" :show-after="0" :hide-after="0">
            <el-icon><CopyDocument /></el-icon>
          </el-tooltip>
        </c-button>
      </c-tooltip>
    </template>
  </c-input-text>
</template>
