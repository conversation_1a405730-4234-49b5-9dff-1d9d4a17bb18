<script setup lang="ts">
import JSON5 from "json5";
import { useStorage } from "@vueuse/core";
import { formatJson } from "./json.models";
import { withDefaultOnError } from "@/utils/defaults";
import { useValidation } from "@/composable/validation";
import TextareaCopyable from "@/components/Tools/TextareaCopyable.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const inputElement = ref<HTMLElement>();

const rawJson = ref<string>('{"hello": "world", "foo": "bar"}');
const indentSize = useStorage("json-prettify:indent-size", 3);
const sortKeys = useStorage("json-prettify:sort-keys", true);
const cleanJson = computed(() => withDefaultOnError(() => formatJson({ rawJson, indentSize, sortKeys }), ""));

const rawJsonValidation = useValidation({
  source: rawJson,
  rules: [
    {
      validator: (v): void | boolean => v === "" || JSON5.parse(v),
      message: t("tools.jsonViewer.validationError")
    }
  ]
});
</script>

<template>
  <div class="json-viewer-container">
    <div class="head">
      {{ t("tools.jsonViewer.title") }}
      <div class="sub-head">{{ t("tools.jsonViewer.description") }}</div>
    </div>
    <div class="main-index">
      <div flex justify-center>
        <el-form-item :label="t('tools.jsonViewer.sortKeys')" label-placement="left" label-width="150" style="margin-right: 20px">
          <el-switch v-model="sortKeys" />
        </el-form-item>
        <el-form-item :label="t('tools.jsonViewer.indentSize')" label-placement="left" label-width="150" :show-feedback="false">
          <el-input-number v-model="indentSize" :min="0" :max="10" w-100px />
        </el-form-item>
      </div>
    </div>
    <div class="sub-title-span">
      <span class="input-icon">📝</span>
      {{ t("tools.jsonViewer.inputJson") }}
    </div>
    <div class="first-form">
      <el-form-item>
        <c-input-text
          ref="inputElement"
          v-model:value="rawJson"
          :placeholder="t('tools.jsonViewer.placeholder')"
          rows="20"
          multiline
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
          :is-valid="rawJsonValidation.isValid"
          monospace
        />
        <div style="height: 3px">
          <span class="feedback"> {{ rawJsonValidation.message || "\u00a0" }} </span>
        </div>
      </el-form-item>
    </div>

    <div class="output-title">
      <span class="output-icon">✨</span>
      {{ t("tools.jsonViewer.formattedJson") }}
    </div>
    <el-form-item>
      <TextareaCopyable :value="cleanJson" language="json" :follow-height-of="inputElement" />
    </el-form-item>
  </div>
</template>
<style lang="scss" scoped>
@import "@/styles/utils";
.json-viewer-container {
  width: 100%;
  height: 100%;
}
.head {
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.main-index {
  padding: 16px;
  margin-bottom: 24px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  div {
    @include flex(row, flex-start, center);

    gap: 20px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
    .el-form-item__label {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-regular);
    }
  }
  :deep(.el-switch) {
    --el-switch-on-color: var(--el-color-primary);
  }
  :deep(.el-input-number) {
    .el-input__inner {
      text-align: center;
    }
  }
}
.feedback {
  margin-top: 4px;
  font-size: 14px;
  color: var(--el-color-error);
}
.sub-title-span {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .input-icon {
    font-size: 16px;
  }
}
.output-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .output-icon {
    font-size: 16px;
  }
}
.first-form {
  margin-bottom: 24px;
  :deep(.feedback-wrapper .feedback) {
    display: none;
  }
  :deep(.c-input-text) {
    .input-wrapper {
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      transition: border-color 0.2s ease;
      &:hover {
        border-color: var(--el-border-color-hover);
      }
      &:focus-within {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 2px var(--el-color-primary-light-9);
      }
    }
  }
}
:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  .textarea-copyable {
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
  }
}
</style>
