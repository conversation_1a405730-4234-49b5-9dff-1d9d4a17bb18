import { ReqPage } from "@/api";
import { CascaderOption } from "element-plus";

export namespace SysPosition {
  /** 职位分页查询 */
  export interface Page extends ReqPage {}

  /** 职位信息 */
  export interface SysPositionInfo {
    /** 职位id */
    id: number | string;
    /** 职位ID */
    orgId: number | string;
    /** 职位名称 */
    name: string;
    /** 职位编码 */
    code: string;
    /** 职位分类 */
    category: string;
    /** 状态 */
    status: string;
    /** 排序码 */
    sortCode: number;
  }
  /** 职位树 */
  export interface SysPositionTree {
    /** id */
    id: number | string;
    /** 名称 */
    name: string;
    /** 是否是职位 */
    isPosition: boolean;
    /** 子集 */
    children: SysPositionTree[];
  }

  /** 职位选择器 */
  export interface SysPositionSelector extends CascaderOption {
    /** id */
    id: string | number;
    /** 名称 */
    name: string;
    /** 组织Id */
    orgId: number | string;
    /** 子集 */
    children: SysPositionSelector[];
  }
}
