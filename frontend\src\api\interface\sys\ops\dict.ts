import { ReqPage } from "@/api";

export namespace SysDict {
  /* 字典信息 */
  export interface DictTree {
    /** 字典值 */
    dictValue: string;
    /** 字典标签 */
    dictLabel: string;
    /** 状态 */
    status: string;
    /** 子集 */
    children: DictTree[];
  }

  /** 字典分页查询 */
  export interface Page extends ReqPage {
    /** 所属模块名称 */
    parentId: number | string;
    /** 字典分类 */
    category: string;
  }

  /** 字典信息 */
  export interface DictInfo extends DictTree {
    /** 字典id */
    id: number | string;
    /** 父Id */
    parentId: number | string;
    /** 字典分类 */
    category: string;
    /** 字典排序 */
    sortCode: number;
  }
}
