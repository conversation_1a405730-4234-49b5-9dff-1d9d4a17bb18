{"name": "VisualDebug", "version": "1.00.001", "feature": "1、包含可视化工具连接、装置信息查看、设定量、模拟量、状态量、遥信、遥测、遥控、报告、装置对时、定值导入导出、变量调试功能；2、包含组态工具预览、新增、编辑、自定义图符、关联装置信息功能；3、包含主题定制、it小工具、装置配置导入导出功能", "description": "可视化平台工程调试工具", "main": "./public/electron/main.js", "scripts": {"dev": "chcp 65001 && ee-bin dev", "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt", "start": "chcp 65001 && ee-bin start", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "dev-go": "ee-bin exec --cmds=go", "dev-go-w": "ee-bin exec --cmds=go_w", "dev-python": "ee-bin exec --cmds=python", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-electron": "ee-bin build --cmds=electron", "build-go-w": "ee-bin move --flag=go_static,go_config,go_package && ee-bin build --cmds=go_w", "build-go-m": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_m", "build-go-l": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_l", "build-python": "ee-bin build --cmds=python && ee-bin move --flag=python_dist", "encrypt": "ee-bin encrypt", "icon": "ee-bin icon", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "ee-bin build --cmds=win64", "build-we": "ee-bin build --cmds=win_e", "build-win32": "ee-bin build --cmds=win32", "build-7z": "ee-bin build --cmds=win_7z", "build-7z-auto": "node ./build/script/set-builder-version.js && npm run build-7z && node ./build/script/set-builder-version.js --restore", "build-m": "ee-bin build --cmds=mac", "build-m-arm64": "ee-bin build --cmds=mac_arm64", "build-l": "ee-bin build --cmds=linux", "debug-dev": "cross-env DEBUG=ee-* ee-bin dev", "debug-encrypt": "ee-bin encrypt", "debug-electron": "cross-env DEBUG=ee-* ee-bin dev --serve=electron", "debug-move": "ee-bin move --flag=frontend_dist"}, "homepage": "https://www.sieyuan.com", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.6.0", "@types/node": "^20.16.0", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "cross-env": "^7.0.3", "debug": "^4.4.0", "ee-bin": "^4.1.10", "electron": "31.0.1", "electron-builder": "^23.6.0", "eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "icon-gen": "^5.0.0", "typescript": "^5.4.2"}, "dependencies": {"axios": "^1.7.9", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "ee-core": "^4.1.5", "electron-updater": "^6.3.8", "entities": "^1.1.2", "entries": "^1.0.1", "esbuild": "^0.21.5", "exceljs": "^4.4.0", "fast-csv": "^4.3.6", "iec-common": "file:./libs/iec-common-1.0.13.tgz", "iec-net": "file:./libs/iec-net-1.0.21.tgz", "iec-upadrpc": "file:./libs/iec-upadrpc-1.0.48.tgz", "license": "file:./libs/license-1.0.2.tgz", "rollup": "^4.37.0", "uuid": "^8.3.2", "xml-formatter": "^3.6.3", "xml2js": "^0.6.2", "node-gyp": "^9.4.1", "xml2js-xpath": "^0.13.0"}, "publishConfig": {"registry": "http://***********:5000"}}