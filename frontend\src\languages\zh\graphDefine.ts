export default {
  equipmentList: {
    sequence: "序号",
    name: "名称",
    type: "类型",
    operation: "操作",
    preview: "预览",
    copy: "复制",
    delete: "删除",
    confirmDelete: "确定删除吗?",
    tip: "提示信息",
    error: "错误"
  },
  graphComponent: {
    deviceType: "设备类型",
    deviceName: "设备名称",
    save: "保存"
  },
  contextMenu: {
    group: "组合",
    ungroup: "解组",
    setStatus: "设置状态",
    copy: "复制",
    delete: "删除",
    rename: "重命名"
  },
  graphCreate: {
    needTwoDevices: "开关或刀闸需要选中两个设备图形",
    needCorrectStatus: "请为开关或刀闸设置正确的状态属性",
    needOneDevice: "请选中一个设备图形"
  },
  graphDefine: {
    waitCanvasInit: "请等待画布初始化完成",
    selectOneGraph: "请选择一个图符",
    tip: "提示"
  },
  setStatus: {
    open: "打开",
    close: "闭合",
    none: "无"
  },
  graphTools: {
    undo: "撤销",
    redo: "重做",
    front: "置前",
    back: "置后",
    delete: "删除",
    save: "保存",
    equipmentList: "设备列表"
  },
  graphEditor: {
    dataConfig: "数据配置",
    loadEquipmentFailed: "加载图符失败"
  }
};
