import { ReqPage } from "@/api/interface";
import { MessageTypeDictEnum, MessageSendWayDictEnum } from "@/enums";
import { SysUser, SysRole } from "@/api";

export namespace SysMessage {
  /** 消息类型 */
  export type MessageType = MessageTypeDictEnum.INFORM | MessageTypeDictEnum.NOTICE | MessageTypeDictEnum.MESSAGE;

  /** 消息信息 */
  export interface SysMessageInfo {
    /** 消息ID */
    id: number | string;
    /** 消息类型 */
    category: string;
    /** 消息标题 */
    subject: string;
    /** 消息内容 */
    content: string;
    /** 消息接收者 */
    receiverType: string;
    /** 是否已读 */
    read: boolean;
    /** 创建时间 */
    createTime: string;
    /** 接受者Id列表 */
    receiverInfo: SysUser.SysUserInfo[] | SysRole.SysRoleInfo[];
    /**发送方式 */
    sendWay: MessageSendWayDictEnum;
    /**发送时间 */
    sendTime: string;
    /**发送时间格式化 */
    sendTimeFormat: string;
    /**延迟时间 */
    delayTime: number;
    /**发送状态 */
    status: string;
    /**接收详情 */
    receiverDetail: receiverDetail[]; //接收者详情
  }

  /** 消息信息 */
  export interface receiverDetail {
    /** 消息ID */
    id: number | string;
    /** 名称 */
    name: string;
    /** 是否已读 */
    read: boolean;
  }
  /** 消息分页查询 */
  export interface Page extends ReqPage {}
}
