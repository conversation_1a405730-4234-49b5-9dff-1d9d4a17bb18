<script setup lang="ts">
import _ from "lodash";
import { reactive, computed } from "vue";
import {
  convertCelsiusToKelvin,
  convertDelisleToKelvin,
  convertFahrenheitToKelvin,
  convertKelvinToCelsius,
  convertKelvinToDelisle,
  convertKelvinToFahrenheit,
  convertKelvinToNewton,
  convertKelvinToRankine,
  convertKelvinToReaumur,
  convertKelvinToRomer,
  convertNewtonToKelvin,
  convertRankineToKelvin,
  convertReaumurToKelvin,
  convertRomerToKelvin
} from "./temperature-converter.models";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

type TemperatureScale = "kelvin" | "celsius" | "fahrenheit" | "rankine" | "delisle" | "newton" | "reaumur" | "romer";

// 分离数据和翻译，数据部分使用 reactive
const unitsData = reactive<
  Record<
    string | TemperatureScale,
    {
      ref: number;
      toKelvin: (v: number) => number;
      fromKelvin: (v: number) => number;
    }
  >
>({
  kelvin: {
    ref: 0,
    toKelvin: _.identity,
    fromKelvin: _.identity
  },
  celsius: {
    ref: 0,
    toKelvin: convertCelsiusToKelvin,
    fromKelvin: convertKelvinToCelsius
  },
  fahrenheit: {
    ref: 0,
    toKelvin: convertFahrenheitToKelvin,
    fromKelvin: convertKelvinToFahrenheit
  },
  rankine: {
    ref: 0,
    toKelvin: convertRankineToKelvin,
    fromKelvin: convertKelvinToRankine
  },
  delisle: {
    ref: 0,
    toKelvin: convertDelisleToKelvin,
    fromKelvin: convertKelvinToDelisle
  },
  newton: {
    ref: 0,
    toKelvin: convertNewtonToKelvin,
    fromKelvin: convertKelvinToNewton
  },
  reaumur: {
    ref: 0,
    toKelvin: convertReaumurToKelvin,
    fromKelvin: convertKelvinToReaumur
  },
  romer: {
    ref: 0,
    toKelvin: convertRomerToKelvin,
    fromKelvin: convertKelvinToRomer
  }
});

// 翻译部分使用 computed，确保语言切换时能响应式更新
const units = computed(() => ({
  kelvin: {
    title: t("tools.temperature.kelvin"),
    unit: t("tools.temperature.kelvinUnit"),
    ...unitsData.kelvin
  },
  celsius: {
    title: t("tools.temperature.celsius"),
    unit: t("tools.temperature.celsiusUnit"),
    ...unitsData.celsius
  },
  fahrenheit: {
    title: t("tools.temperature.fahrenheit"),
    unit: t("tools.temperature.fahrenheitUnit"),
    ...unitsData.fahrenheit
  },
  rankine: {
    title: t("tools.temperature.rankine"),
    unit: t("tools.temperature.rankineUnit"),
    ...unitsData.rankine
  },
  delisle: {
    title: t("tools.temperature.delisle"),
    unit: t("tools.temperature.delisleUnit"),
    ...unitsData.delisle
  },
  newton: {
    title: t("tools.temperature.newton"),
    unit: t("tools.temperature.newtonUnit"),
    ...unitsData.newton
  },
  reaumur: {
    title: t("tools.temperature.reaumur"),
    unit: t("tools.temperature.reaumurUnit"),
    ...unitsData.reaumur
  },
  romer: {
    title: t("tools.temperature.romer"),
    unit: t("tools.temperature.romerUnit"),
    ...unitsData.romer
  }
}));

function update(key: TemperatureScale): void {
  const { ref: value, toKelvin } = units.value[key];

  const kelvins = toKelvin(value) ?? 0;

  Object.entries(units.value)
    .filter(([unitKey]) => unitKey !== key)
    .forEach(([unitKey, { fromKelvin }]) => {
      unitsData[unitKey as TemperatureScale].ref = Math.floor((fromKelvin(kelvins) ?? 0) * 100) / 100;
    });
}

update("kelvin");
</script>

<template>
  <div class="temperature-converter-container">
    <div class="head">
      {{ t("tools.temperature.title") }}
      <div class="sub-head">{{ t("tools.temperature.description") }}</div>
    </div>
    <div class="base-converter">
      <div v-for="[key, { title, unit }] in Object.entries(units)" :key="key" class="temperature-input-row">
        <div class="temperature-label">{{ title }}</div>
        <el-input-number
          v-model="unitsData[key].ref"
          :precision="2"
          controls-position="right"
          class="temperature-input"
          @change="() => update(key as TemperatureScale)"
        />
        <div class="temperature-unit">{{ unit }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "@/styles/utils";
.temperature-converter-container {
  width: 100%;
  height: 100%;
}
.head {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.base-converter {
  @include flex(column, flex-start, stretch);
  gap: 12px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 4%);
}

.temperature-input-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.temperature-label {
  width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  text-align: right;
}

.temperature-input {
  flex: 1;

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary);
    }
  }

  :deep(.el-input__inner) {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }

  :deep(.el-input-number__increase),
  :deep(.el-input-number__decrease) {
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.temperature-unit {
  width: 40px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  text-align: left;
}
</style>
