import { SysConfig, SysOrg } from "@/api/interface";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/");

const commonApi = {
  /** 获取系统配置 */
  sysInfo() {
    return http.get<SysConfig.ConfigInfo[]>("sysInfo", {}, { loading: false, cancel: false });
  },
  /** 获取登录策略 */
  loginPolicy() {
    return http.get<SysConfig.ConfigInfo[]>("loginPolicy", {}, { loading: false, cancel: false });
  },
  /** 获取租户列表 */
  tenantList() {
    return http.get<SysOrg.SysOrgInfo[]>("tenantList", {}, { loading: false, cancel: false });
  }
};

export { commonApi };
