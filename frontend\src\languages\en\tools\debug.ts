export default {
  search: {
    placeholder: "Search device",
    button: "Search",
    success: "Search successful"
  },
  device2: {
    search: {
      placeholder: "Search device",
      add: "Add Device",
      duplicate: "This IP and port already exist, please do not add again"
    },
    list: {
      empty: "No device found",
      unnamed: "Unnamed Device",
      status: {
        connected: "Connected",
        disconnected: "Disconnected"
      },
      contextMenu: {
        connect: "Connect",
        edit: "Edit",
        disconnect: "Disconnect",
        remove: "Delete"
      },
      message: {
        disconnectFirst: "Please disconnect before editing",
        disconnectFirstDelete: "Please disconnect before deleting",
        connectSuccess: "Device {name}: Connected successfully",
        connectExists: "Device {name}: Connection already exists",
        connectFailed: "Device {name}: Connection failed",
        connectFailedReason: "Device connection failed reason: {reason}",
        disconnected: "Device {name}: Disconnected",
        operationFailed: "Device {name}: Operation failed"
      }
    },
    report: {
      group: {
        openWaveConfirm: "Open wave file with third-party tool?",
        tips: "Reminder",
        noWaveTool: "Wave tool not configured"
      },
      common: {
        selectRow: "Please select the row to operate"
      }
    },
    backup: {
      savePath: "Save Path",
      setPath: "Please set backup path",
      setPathTitle: "Set backup path",
      locateFolder: "Locate Folder",
      startBackup: "Start Backup",
      cancelBackup: "Cancel Backup",
      backup: "Backup",
      sequence: "Sequence",
      backupType: "Backup Type",
      backupDesc: "Backup Description",
      progress: "Progress",
      status: "Status",
      noTypeSelected: "Please select backup type",
      backupSuccess: "Backup successful",
      backupFailed: "Backup failed",
      openFolderFailed: "Failed to open folder",
      backupTypes: {
        paramValue: "Parameter Values",
        faultInfo: "Fault Information",
        cidConfigPrjLog: "CID Config Project Log",
        waveReport: "Wave Report"
      },
      backupDescTypes: {
        paramValue: "Backup all parameter setting values of the device",
        faultInfo: "Backup fault recording information of the device",
        cidConfigPrjLog: "Backup CID configuration files and project logs",
        waveReport: "Backup waveform analysis report files"
      },
      backupStatus: {
        userCancelled: "User Cancelled",
        transferring: "Transferring"
      },
      console: {
        pathNotSet: "Backup path not set, unable to start backup",
        noTypeSelected: "No backup type selected, unable to start backup",
        startBackup: "Start backup, types: {types}, path: {path}",
        backupException: "Backup exception: {error}",
        pathSelected: "Backup path selected: {path}",
        pathNotSelected: "No backup path selected",
        pathNotSetForLocate: "Backup path not set, unable to locate folder",
        folderOpened: "Backup folder opened: {path}",
        openFolderFailed: "Failed to open backup folder: {error}",
        taskCompleted: "Task completed",
        taskCancelled: "Task cancelled",
        typeError: "Type [{type}] error: {error}",
        typeCompleted: "Type [{type}] backup completed",
        typeCancelled: "Type [{type}] cancelled",
        typeFailed: "Type [{type}] failed"
      }
    },
    remoteControl: {
      directControl: "Direct Control",
      selectControl: "Select Control"
    },
    messageMonitor: {
      title: "Message Monitor",
      start: "Start Monitoring",
      stop: "Stop Monitoring",
      clear: "Clear",
      export: "Export",
      expand: "Expand",
      collapse: "Collapse",
      close: "Close",
      messageType: "Message",
      noMessages: "No message data",
      noMessagesToExport: "No message data to export",
      startSuccess: "Started monitoring messages",
      stopSuccess: "Stopped monitoring messages",
      clearSuccess: "Messages cleared successfully",
      exportSuccess: "Messages exported successfully",
      exportFailed: "Failed to export messages",
      toggleFailed: "Failed to toggle monitoring status"
    }
  }
};
