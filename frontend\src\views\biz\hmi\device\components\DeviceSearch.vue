<template>
  <div class="header-bar">
    <!-- 搜索 -->
    <div class="flx-justify-between">
      <el-input
        v-model="searchInput"
        :placeholder="t('hmi.device.deviceSearch.searchPlaceholder')"
        style="width: 100%; margin-right: 5px"
        :prefix-icon="Search"
        @input="debounceSearch"
        clearable
      >
      </el-input>
      <div class="flx-center">
        <el-button plain :icon="Plus" @click="showForm = true" />
      </div>
    </div>
  </div>
  <DeviceForm v-if="showForm" :visible="showForm" @update:visible="val => (showForm = val)" @submit="handleSubmit" @cancel="showForm = false" />
</template>

<script setup lang="ts">
import { Search, Plus } from "@element-plus/icons-vue";
import { ref } from "vue";
import { debounce } from "lodash";
import { useDebugStore } from "@/stores/modules/debug";
import DeviceForm from "../../../debug/device/dialog/DeviceFormDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const searchInput = ref<string>("");
const debugStore = useDebugStore();
const { setSearchDevice, addDevice } = debugStore;
const showForm = ref(false);

defineOptions({
  name: "Search"
});

const debounceSearch = debounce(() => {
  setSearchDevice(searchInput.value);
}, 200);

const handleSubmit = (device: any) => {
  showForm.value = false;
  console.log("deviceId:" + device.id);
  // 添加新设备
  addDevice({
    ...device,
    id: "",
    prjType: 1,
    deviceType: 1,
    isConnect: false,
    isActive: false,
    connectTime: ""
  });
};
</script>

<style lang="scss" scoped>
.header-bar {
  width: 100%;
  height: auto;
  margin-bottom: 5px;
  .suffix {
    position: absolute;
    right: 10px;
    z-index: 5;
    font-size: 12px;
    pointer-events: none;
  }
}
.header-search-add {
  min-width: 32px;
  height: 32px;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  background: #54b4ef;
  border-radius: 2px;
}
.el-button:focus {
  outline: none;
}
</style>
