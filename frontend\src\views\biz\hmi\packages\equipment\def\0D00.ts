import { calculateEllipse, calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "0D00",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.5)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 15.86, 23, 22.5)
      }
    },
    {
      tagName: "polygon",
      groupSelector: "polygon",
      attrs: {
        points: calculateTriangle(6.67, 4.67, 9.33, 7.33)
      }
    },
    {
      tagName: "polygon",
      groupSelector: "polygon",
      attrs: {
        points: calculateTriangle(6.67, 25.33, 9.33, 7.33)
      }
    }
  ],
  attrs: {
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    },
    polygon: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
