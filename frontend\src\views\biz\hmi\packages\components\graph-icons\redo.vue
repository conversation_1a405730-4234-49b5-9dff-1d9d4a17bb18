<template>
  <svg
    t="1742950726965"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="8107"
    :width="props.width"
    :height="props.height"
  >
    <path
      d="M493.056 347.733333c-142.421333 0-262.656 96.853333-301.013333 229.589334l0.170666-0.469334 6.186667-8.832c51.498667-70.4 133.717333-115.925333 226.346667-115.925333 90.197333 0 170.496 43.178667 222.165333 110.378667l-99.072 99.242666a8.533333 8.533333 0 0 0 6.016 14.549334H832V397.909333a8.533333 8.533333 0 0 0-14.592-6.058666l-77.824 77.952c-57.472-74.368-146.517333-122.069333-246.485333-122.069334z"
      :fill="props.color"
      p-id="8108"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";
const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
