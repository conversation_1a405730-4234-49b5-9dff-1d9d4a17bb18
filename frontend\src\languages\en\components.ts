export default {
  checkCard: {
    default: "Default"
  },
  chooseModule: {
    title: "Select Application",
    noModule: "No module found!",
    setDefault: "Set as Default",
    cancel: "Cancel",
    confirm: "Confirm"
  },
  closer: {
    title: "Exit Confirmation",
    message: "Are you sure you want to exit?",
    confirm: "Confirm",
    minimize: "Minimize to Tray",
    cancel: "Cancel"
  },
  codeHighLight: {
    noCode: "None"
  },
  cropUpload: {
    title: "Image Crop",
    zoomIn: "Zoom In",
    zoomOut: "Zoom Out",
    rotateLeft: "Rotate Left",
    rotateRight: "Rotate Right",
    uploadImage: "Click to Upload Image",
    uploadTip: "Please upload an image file, recommended not to exceed 2M",
    cancel: "Cancel",
    confirm: "Confirm"
  },
  error: {
    forbidden: "Sorry, you do not have permission to access this page~🙅‍♂️🙅‍♀️",
    notFound: "Sorry, the page you visited does not exist~🤷‍♂️🤷‍♀️",
    serverError: "Sorry, your network is lost~🤦‍♂️🤦‍♀️",
    back: "Back to Previous Page"
  },
  form: {
    input: {
      placeholder: "Please fill in {label}"
    },
    select: {
      placeholder: "Please select {label}"
    },
    button: {
      add: "Add",
      edit: "Edit",
      delete: "Delete",
      view: "View"
    },
    search: {
      inputPlaceholder: "Please enter",
      selectPlaceholder: "Please select",
      rangeSeparator: "to",
      startPlaceholder: "Start time",
      endPlaceholder: "End time"
    }
  },
  selectIcon: {
    title: "Select Icon",
    placeholder: "Please select an icon",
    searchPlaceholder: "Search icon",
    noSearchResult: "No icon found~",
    moreIcons: "More icons",
    enterIconifyCode: "Please enter the iconify code you want, e.g. mdi:home-variant",
    iconifyAddress: "Iconify Address",
    localIcons: "Local Icons"
  },
  selector: {
    add: "Add",
    addCurrent: "Add Current",
    addSelected: "Add Selected",
    delete: "Delete",
    deleteCurrent: "Delete Current",
    deleteSelected: "Delete Selected",
    cancel: "Cancel",
    confirm: "Confirm",
    selected: "Selected",
    maxSelect: "Max Select",
    singleSelectOnly: "Only one can be selected",
    maxSelectLimit: "Up to {count} can be selected",
    person: "Person"
  },
  upload: {
    view: "View",
    edit: "Edit",
    delete: "Delete",
    uploadImage: "Please upload an image",
    uploadSuccess: "Image uploaded successfully!",
    uploadFailed: "Image upload failed, please re-upload!",
    invalidFormat: "The uploaded image does not meet the required format!",
    fileSizeExceeded: "The uploaded image size cannot exceed {size}M!",
    maxFilesExceeded: "You can upload up to {limit} images, please remove some before uploading!",
    fileSizeZero: "File {fileName} size is 0, cannot upload!",
    tips: "Tips"
  },
  treeFilter: {
    searchPlaceholder: "Enter keyword to filter",
    expandAll: "Expand All",
    collapseAll: "Collapse All",
    all: "All"
  },
  proTable: {
    search: {
      reset: "Reset",
      search: "Search",
      expand: "Expand",
      collapse: "Collapse"
    },
    pagination: {
      total: "Total {total}",
      pageSize: "per page",
      goto: "Go to",
      page: "Page"
    },
    colSetting: {
      title: "Column Settings",
      fixedLeft: "Show",
      fixedRight: "Sortable",
      cancelFixed: "Cancel Fixed",
      reset: "Restore Default",
      confirm: "Confirm",
      cancel: "Cancel"
    },
    table: {
      empty: "No data"
    }
  },
  basicComponent: {
    title: "Basic Components",
    line: "Line",
    text: "Text",
    rect: "Rectangle",
    circle: "Circle",
    ellipse: "Ellipse",
    triangle: "Triangle",
    arc: "Arc"
  }
};
