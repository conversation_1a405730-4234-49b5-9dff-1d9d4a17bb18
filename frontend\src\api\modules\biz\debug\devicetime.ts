import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/devicetime/");

const devicetimeApi = {
  /** 获取装置时间（显式 deviceId） */
  getDeviceTimeByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("getDeviceTime", undefined, deviceId);
  },
  /** 写入装置时间（显式 deviceId） */
  writeDeviceTimeByDevice(deviceId: string, time: string) {
    return ipc.iecInvokeWithDevice<{}>("writeDeviceTime", time, deviceId);
  }
};

export { devicetimeApi };
