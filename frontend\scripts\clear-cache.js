#!/usr/bin/env node

/**
 * Vite缓存清理脚本
 * 用于清理开发和构建过程中的各种缓存文件
 */

import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync, rmSync } from "fs";
import { execSync } from "child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");

// 需要清理的缓存目录和文件
const cacheTargets = [
  // Vite缓存
  "node_modules/.vite",

  // 构建输出
  "dist",

  // TypeScript缓存
  "node_modules/.cache/typescript",

  // ESLint缓存
  "node_modules/.cache/.eslintcache",
  ".eslintcache",

  // Stylelint缓存
  "node_modules/.cache/stylelint",

  // Rollup缓存
  "node_modules/.cache/rollup",

  // 其他可能的缓存
  ".temp",
  ".cache",
  "stats.html",

  // 自动生成的类型文件（会重新生成）
  "auto-imports.d.ts",
  "components.d.ts",
  "src/auto-import.d.ts",
  "src/components.d.ts"
];

/**
 * 清理指定的缓存目录或文件
 * @param {string} target 目标路径
 */
function clearTarget(target) {
  const fullPath = join(projectRoot, target);

  if (existsSync(fullPath)) {
    try {
      rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ 已清理: ${target}`);
      return true;
    } catch (error) {
      console.error(`❌ 清理失败: ${target} - ${error.message}`);
      return false;
    }
  } else {
    console.log(`⏭️  跳过: ${target} (不存在)`);
    return true;
  }
}

/**
 * 清理npm/pnpm缓存
 */
function clearPackageManagerCache() {
  try {
    console.log("\n🧹 清理包管理器缓存...");

    // 检测使用的包管理器
    if (existsSync(join(projectRoot, "pnpm-lock.yaml"))) {
      execSync("pnpm store prune", { stdio: "inherit", cwd: projectRoot });
      console.log("✅ 已清理 pnpm 缓存");
    } else if (existsSync(join(projectRoot, "yarn.lock"))) {
      execSync("yarn cache clean", { stdio: "inherit", cwd: projectRoot });
      console.log("✅ 已清理 yarn 缓存");
    } else {
      execSync("npm cache clean --force", { stdio: "inherit", cwd: projectRoot });
      console.log("✅ 已清理 npm 缓存");
    }
  } catch (error) {
    console.error(`❌ 清理包管理器缓存失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log("🚀 开始清理 Vite 项目缓存...\n");

  let successCount = 0;
  let totalCount = cacheTargets.length;

  // 清理文件和目录缓存
  console.log("📁 清理文件和目录缓存...");
  for (const target of cacheTargets) {
    if (clearTarget(target)) {
      successCount++;
    }
  }

  // 清理包管理器缓存
  clearPackageManagerCache();

  console.log(`\n📊 清理完成: ${successCount}/${totalCount} 项成功`);
  console.log("\n💡 建议操作:");
  console.log("   1. 重新安装依赖: npm install / yarn / pnpm install");
  console.log("   2. 重新启动开发服务器: npm run dev");
  console.log("   3. 如果问题仍然存在，尝试删除 node_modules 后重新安装");

  if (successCount === totalCount) {
    console.log("\n✨ 所有缓存清理完成！");
    process.exit(0);
  } else {
    console.log("\n⚠️  部分缓存清理失败，请检查权限或手动删除");
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes("--help") || args.includes("-h")) {
  console.log(`
Vite 缓存清理工具

用法:
  node scripts/clear-cache.js [选项]

选项:
  --help, -h     显示帮助信息
  --all, -a      清理所有缓存（包括 node_modules）
  --dev, -d      仅清理开发缓存
  --build, -b    仅清理构建缓存

示例:
  node scripts/clear-cache.js          # 清理所有缓存
  node scripts/clear-cache.js --dev    # 仅清理开发缓存
  node scripts/clear-cache.js --build  # 仅清理构建缓存
`);
  process.exit(0);
}

// 根据参数调整清理目标
if (args.includes("--dev") || args.includes("-d")) {
  // 仅清理开发相关缓存
  const devTargets = cacheTargets.filter(target => !target.includes("dist") && !target.includes("stats.html"));
  cacheTargets.length = 0;
  cacheTargets.push(...devTargets);
  console.log("🔧 开发模式: 仅清理开发相关缓存");
} else if (args.includes("--build") || args.includes("-b")) {
  // 仅清理构建相关缓存
  const buildTargets = ["dist", "stats.html", "node_modules/.cache/rollup"];
  cacheTargets.length = 0;
  cacheTargets.push(...buildTargets);
  console.log("🏗️  构建模式: 仅清理构建相关缓存");
}

if (args.includes("--all") || args.includes("-a")) {
  // 添加 node_modules 到清理目标
  cacheTargets.push("node_modules");
  console.log("🗑️  完全清理模式: 包括 node_modules");
}

// 执行清理
main();
