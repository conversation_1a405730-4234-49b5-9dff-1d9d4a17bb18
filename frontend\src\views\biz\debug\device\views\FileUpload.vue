<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :data="tableData"
      highlight-current-row
      :request-auto="false"
      :init-param="initParam"
      :pagination="false"
      :data-callback="dataCallback"
      row-key="fileName"
      table-key="fileUpload"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("device.fileUpload.deviceDirectory") }}：</el-text>
          <el-select v-model="selectType" filterable allow-create @change="handleChange" style="width: 120px">
            <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-text class="mx-1">{{ t("device.fileUpload.savePath") }}：</el-text>
          <el-input v-model="filePath" :placeholder="t('device.fileUpload.savePath')" style="width: 200px" readonly> </el-input>
          <el-button type="primary" plain @click="openFileDialog" :icon="Setting" :title="t('device.fileUpload.setPath')"></el-button>
          <el-button type="primary" :icon="Tickets" :disabled="uploading" plain @click="getDeviceFile">{{
            t("device.fileUpload.getFiles")
          }}</el-button>
          <el-button type="primary" :icon="Upload" :disabled="uploading || scope.selectedList.length == 0" @click="batchUpload(scope.selectedList)">
            {{ t("device.fileUpload.uploadFiles") }}
          </el-button>
          <el-button
            type="warning"
            :icon="Dish"
            :disabled="!uploading || scope.selectedList.length === 0"
            plain
            @click="batchCancel(scope.selectedList)"
          >
            {{ t("device.fileUpload.cancelUpload") }}
          </el-button>
          <el-button
            type="danger"
            :icon="Delete"
            :disabled="uploading"
            plain
            @click="clearDeviceFile"
            :title="t('device.fileUpload.clearList')"
          ></el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button
          v-if="!scope.row.hasTask"
          :disabled="scope.row.status === t('device.fileUpload.status.waiting')"
          type="primary"
          link
          :icon="Upload"
          @click="uploadSingFile(scope.row)"
        >
          {{ t("device.fileUpload.upload") }}
        </el-button>
        <el-button type="primary" link v-if="scope.row.hasTask" :icon="Dish" @click="cancelUploadSingFile(scope.row)">{{
          t("device.fileUpload.cancelUpload")
        }}</el-button>
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { FileItem, UrpcFileItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Tickets, Upload, Dish, Delete, Setting } from "@element-plus/icons-vue";
import { devicefileApi } from "@/api/modules/biz/debug/devicefile";
import { osControlApi } from "@/api/modules/biz/os";
import { MathUtils } from "@/utils/mathUtils";
// import { variableApi } from "@/api/modules/biz/debug/variable";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { ipc } from "@/api/request/ipcRenderer";
import { IECNotify } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
const { currDevice } = useDebugStore();
const { addConsole } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const uploading = ref(false);

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const filePath = ref("");
const tableData = ref<FileItem[]>([]);
const selectType = ref("/shr");
const selectOptions = [
  {
    value: "/shr",
    label: "/shr"
  },
  {
    value: "/shr/configuration",
    label: "/shr/configuration"
  },
  {
    value: "/wave",
    label: "/wave"
  },
  {
    value: "/wave/comtrade",
    label: "/wave/comtrade"
  },
  {
    value: "/wave/log",
    label: "/wave/log"
  },
  {
    value: "/log",
    label: "/log"
  }
];
const handleChange = value => {
  if (!selectOptions.some(item => item.value === value)) {
    selectOptions.push({
      value,
      label: value
    });
  }
};
const { t } = useI18n();

const uploadSingFile = async (row: FileItem) => {
  console.log("uploadSingFile", row);
  if (String(row.fileName).endsWith("\\")) {
    ElMessageBox.alert(t("device.fileUpload.errors.invalidFile"), t("common.error"), {
      type: "error"
    });
    return;
  }
  if (row.fileSize === 0) {
    ElMessageBox.alert(t("device.fileUpload.errors.fileSizeZero", { fileName: row.fileName }), t("common.error"), {
      type: "error"
    });
    return;
  }
  if (filePath.value == "" || filePath.value == null) {
    ElMessageBox.alert(t("device.fileUpload.errors.emptySavePath"), t("common.error"), {
      type: "error"
    });
    return;
  }
  const result = await devicefileApi.uploadDeviceFileByDevice(props.deviceId, {
    savePath: filePath.value,
    fileItems: [
      {
        fileName: row.fileName,
        fileSize: row.fileSize,
        lastModified: row.lastModified,
        checkSum: row.checkSum,
        fileParentPath: row.fileParentPath
      }
    ]
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileUpload.messages.uploadCompleted"));
    addConsole(t("device.fileUpload.messages.uploadCompleted") + row.fileName);
  }
};

const cancelUploadSingFile = async (row: any) => {
  console.log("uploadSingFile", row);
  if (row.taskid == "") {
    ElMessageBox.alert(t("device.fileUpload.errors.noUploadTask"), t("common.error"), {
      type: "error"
    });
    return;
  }
  const result = await devicefileApi.cancelUploadDeviceFIleByDevice(props.deviceId, {
    taskids: [row.taskid]
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileUpload.messages.uploadCancelled"));
    addConsole(t("device.fileUpload.messages.uploadCancelled") + row.fileName);
    const items = Array.isArray(result.data) ? result.data : [];
    const targetNames = new Set(items.map(item => item.fileName));
    proTable.value?.tableData.forEach(row => {
      if (row.status === t("device.fileUpload.status.waiting") && targetNames.has(row.fileName)) {
        row.status = t("device.fileUpload.status.cancelled");
        row.taskid = "";
      }
    });
  }
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getUploadList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.filePath = selectType.value;
  delete newParams.createTime;
  const result = await getFileList(newParams);
  const items = Array.isArray(result) ? result : [];
  items.forEach(item => {
    item.fileSizeAs = MathUtils.formatBytes(item.fileSize);
  });
  tableData.value = items;
  proTable.value?.refresh();
};

// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("device.fileUpload.serialNumber"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("device.fileUpload.fileName"),
    width: 240,
    sortable: true
  },
  {
    prop: "fileSize",
    label: t("device.fileUpload.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // a、b 是整行数据，这里按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: scope => {
      // 显示格式化后的文件大小
      return <span>{scope.row.fileSizeAs}</span>;
    }
  },
  {
    prop: "lastModified",
    label: t("device.fileUpload.lastModified"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified).getTime() || 0;
      const timeB = new Date(b.lastModified).getTime() || 0;
      return timeA - timeB;
    }
  },
  {
    prop: "percent",
    label: t("device.fileUpload.progress"),
    render: scope => {
      return (
        <div>
          <el-progress percentage={scope.row.percent} text-inside={true} stroke-width={16} status={scope.row.percentType}></el-progress>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: t("device.fileUpload.statusTitle")
  },
  { prop: "operation", label: t("device.fileUpload.operation"), fixed: "right", width: 150 }
]);

const openFileDialog = async () => {
  // console.log("打开文件选择框");
  const savePath = await osControlApi.selectFolder();
  if (String(savePath) !== "") {
    filePath.value = String(savePath);
    // localStorage.setItem("fileupload.savePath", String(savePath));
  }
};

// 批量删除用户信息
const batchCancel = async (rows: any[]) => {
  const orderMap = new Map();
  // 获取表格当前显示的排序后数据
  const tableElement = proTable.value?.element;
  const sortedData = tableElement?.store?.states?.data?.value || proTable.value?.tableData || tableData.value;
  sortedData.map((item, index) => {
    orderMap.set(item.fileName, index);
  });
  const arrayRows = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.fileName);
    const bIndex = orderMap.get(b.fileName);
    return aIndex - bIndex;
  });

  console.log(arrayRows);
  const taskids = arrayRows.map(item => item.taskid);
  if (taskids.length == 0) {
    ElMessageBox.alert(t("device.fileUpload.errors.noUploadTask"), t("common.error"), {
      type: "error"
    });
    return;
  }
  const result = await devicefileApi.cancelUploadDeviceFIleByDevice(props.deviceId, {
    taskids
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileUpload.messages.uploadCancelled"));
    addConsole(t("device.fileUpload.messages.uploadCancelled"));
    const items = Array.isArray(result.data) ? result.data : [];
    const targetNames = new Set(items.map(item => item.fileName));
    proTable.value?.tableData.forEach(row => {
      if (row.status === t("device.fileUpload.status.waiting") && targetNames.has(row.fileName)) {
        row.status = t("device.fileUpload.status.cancelled");
        row.taskid = "";
      }
    });
  }
};

const batchUpload = async (rows: any[]) => {
  const orderMap = new Map();
  // 获取表格当前显示的排序后数据
  const tableElement = proTable.value?.element;
  const sortedData = tableElement?.store?.states?.data?.value || proTable.value?.tableData || tableData.value;
  sortedData.map((item, index) => {
    orderMap.set(item.fileName, index);
  });
  const arrayRows = [...rows].sort((a, b) => {
    const aIndex = orderMap.get(a.fileName);
    const bIndex = orderMap.get(b.fileName);
    return aIndex - bIndex;
  });
  const dirRows = arrayRows.filter(item => String(item.fileName).endsWith("\\"));
  if (dirRows.length > 0) {
    ElMessageBox.alert(t("device.fileUpload.errors.invalidFile"), t("common.error"), {
      type: "error"
    });
    return;
  }
  const zeroSizeRows = arrayRows.filter(item => item.fileSize === 0);
  if (zeroSizeRows.length > 0) {
    const fileNames = zeroSizeRows.map(item => item.fileName).join(", ");
    ElMessageBox.alert(t("device.fileUpload.errors.fileSizeZero", { fileName: fileNames }), t("common.error"), {
      type: "error"
    });
    return;
  }
  if (filePath.value == "" || filePath.value == null) {
    ElMessageBox.alert(t("device.fileUpload.errors.emptySavePath"), t("common.error"), {
      type: "error"
    });
    return;
  }
  const fileItems: UrpcFileItem[] = [];
  arrayRows.forEach(row => {
    fileItems.push({
      fileName: row.fileName,
      fileSize: row.fileSize,
      lastModified: row.lastModified,
      checkSum: row.checkSum,
      fileParentPath: row.fileParentPath
    });
    row.percent = 0;
    row.status = t("device.fileUpload.status.waiting");
  });
  const result = await devicefileApi.uploadDeviceFileByDevice(props.deviceId, {
    savePath: filePath.value,
    fileItems: fileItems
  });
  if (Number(result.code) == 0) {
    console.log(t("device.fileUpload.messages.uploadCompleted"));
  }
};
const clearDeviceFile = () => {
  console.log("clearDeviceFile");
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  tableData.value = [];
  addConsole(t("device.fileUpload.messages.clearListSuccess"));
};
const getDeviceFile = () => {
  console.log("getDeviceFile");
  getUploadList({});
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
};

const getFileList = async (newParams: any) => {
  console.log(newParams);
  progressDialog.value.show();
  try {
    const response = await devicefileApi.getDeviceFileByDevice(props.deviceId, newParams);
    if (Number(response.code) == 0) {
      return response.data || [];
    } else {
      ElMessageBox.alert(t("device.fileUpload.errors.getFilesFailed"), t("common.error"), {
        type: "error"
      });
      return [];
    }
  } finally {
    progressDialog.value.hide();
  }
};

const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const notifyData = notify.data as any;
  const fileItem = notifyData.fileItem as FileItem;
  const status = notifyData.status;
  const progress = notifyData.progress;
  const taskid = notifyData.taskid;
  const errorMsg = notifyData.errorMsg;
  const row = proTable.value?.tableData.find(row => row.fileName === fileItem.fileName);
  row.taskid = taskid;
  let msg = "";
  if (status === "INIT") {
    msg = t("device.fileUpload.status.preparing");
    row.percentType = "";
    row.hasTask = true;
  } else if (status === "DATA_TRANSFER") {
    msg = t("device.fileUpload.status.uploading");
    row.percentType = "";
    row.hasTask = true;
  } else if (status === "SINGLE_FILE_FINISH") {
    msg = t("device.fileUpload.status.completed");
    row.percentType = "success";
    row.taskid = "";
    row.hasTask = false;
    // 上传完成后自动取消勾选，方便下次只上传未完成的文件
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "ERROR") {
    msg = t("device.fileUpload.status.error") + errorMsg;
    row.percentType = "exception";
    row.taskid = "";
    row.hasTask = false;
    // 上传失败后也自动取消勾选，方便下次只上传未完成的文件
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "USER_CANCLE") {
    msg = t("device.fileUpload.status.cancelled");
    row.percentType = "warning";
    row.taskid = "";
    row.hasTask = false;
    row.percent = 0; // 取消时重置进度
    // 用户取消后也自动取消勾选
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  } else if (status === "ALL_FILE_FINISH") {
    msg = t("device.fileUpload.status.completed");
    row.percentType = "success";
    row.taskid = "";
    row.hasTask = false;
    // 所有文件完成后自动取消勾选
    nextTick(() => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  }
  if (notify.type == "fileUpload") {
    row.percent = progress;
    row.status = msg;
    return;
  }
};

onMounted(() => {
  loadData();
  addAllListeners();
});
onBeforeUnmount(() => {
  saveData();
  removeAllListeners();
});

function removeAllListeners() {
  window.removeEventListener("beforeunload", saveData);
  ipc.removeAllListeners("fileupload_notify");
}

function addAllListeners() {
  window.addEventListener("beforeunload", saveData);
  ipc.on("fileupload_notify", notifyMethod);
}

function saveData() {
  console.log(currDevice.id);
  localStorage.setItem(currDevice.id + ".uploadPath", JSON.stringify(filePath.value));
}

const loadData = async () => {
  console.log(currDevice.id);
  setTimeout(() => {
    const filePathValue = localStorage.getItem(currDevice.id + ".uploadPath");
    if (filePathValue) {
      filePath.value = JSON.parse(filePathValue);
    }
  }, 100);
};

const uploadingItem = computed(() => {
  return tableData.value.filter(item => item.hasTask);
});

watch(
  () => uploadingItem.value.length,
  (newVal, oldVal) => {
    console.log(oldVal, "=>", newVal);
    if (newVal > 0) {
      uploading.value = true;
    } else {
      uploading.value = false;
    }
  }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
