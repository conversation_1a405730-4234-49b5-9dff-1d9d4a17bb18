<script lang="ts" setup>
import type { RouteLocationRaw } from "vue-router";

const props = withDefaults(
  defineProps<{
    type?: "default" | "primary" | "warning" | "error";
    variant?: "basic" | "text";
    disabled?: boolean;
    round?: boolean;
    circle?: boolean;
    href?: string;
    to?: RouteLocationRaw;
    size?: "small" | "medium" | "large";
  }>(),
  {
    type: "default",
    variant: "basic",
    disabled: false,
    round: false,
    circle: false,
    href: undefined,
    to: undefined,
    size: "medium"
  }
);
const emits = defineEmits(["click"]);

const { disabled, round, circle, href, to } = toRefs(props);

function handleClick(event: MouseEvent): void {
  if (!disabled.value) {
    emits("click", event);
  }
}

const tag = computed(() => {
  if (href.value) {
    return "a";
  }
  if (to.value) {
    return "router-link";
  }
  return "button";
});
</script>

<template>
  <component :is="tag" :href="href ?? to" class="c-button" :class="{ disabled, round, circle }" :to="to" @click="handleClick">
    <slot />
  </component>
</template>

<style lang="scss" scoped>
.c-button {
  line-height: 1;
  font-family: inherit;
  font-size: 15px;
  border: none;
  text-align: center;
  cursor: pointer;
  text-decoration: none;
  height: 30px;
  font-weight: 400;
  color: var(--el-color-primary);
  padding: 0 14px;
  transition: background-color cubic-bezier(0.4, 0, 0.2, 1) 0.3s;

  background-color: var(--el-bg-color);
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  // outline-offset: 1px;
  &.round {
    border-radius: 10px;
  }

  &.circle {
    border-radius: 10px;
    width: 100%;
    padding: 0;
  }

  &:not(.disabled) {
    &:hover {
      background-color: var(--el-bg-color);
    }

    &:active {
      background-color: var(--el-bg-color);
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>
