import { moduleRequest } from "@/api/request";
import { ReqId, ResPage, Spa } from "@/api/interface";
const http = moduleRequest("/sys/limit/module/");

const moduleApi = {
  /** 获取模块分页 */
  page(params: Spa.Page) {
    return http.get<ResPage<Spa.SpaInfo>>("page", params);
  },
  /** 获取模块详情 */
  detail(params: ReqId) {
    return http.get<Spa.SpaInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除模块 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 获取模块列表 */
  list() {
    return http.get<Spa.SpaInfo[]>("list");
  }
};

export { moduleApi };
