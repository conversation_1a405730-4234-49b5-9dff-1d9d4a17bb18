import { FileItem, UpadRpcFileDownloadItem, UrpcFileItem } from "@/api/interface/biz/debug/fileitem";
import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/devicefile/");

const devicefileApi = {
  // 获取装置文件（指定 deviceId）
  getDeviceFileByDevice(deviceId: string, params: any) {
    return ipc.iecInvokeWithDevice<{}>("getDeviceFile", params, deviceId);
  },
  // 上传装置文件（指定 deviceId）
  uploadDeviceFileByDevice(deviceId: string, param: { savePath: string; fileItems: UrpcFileItem[] }) {
    return ipc.iecInvokeWithDevice<{}>("uploadDeviceFile", param, deviceId);
  },
  // 取消文件上传（指定 deviceId）
  cancelUploadDeviceFIleByDevice(deviceId: string, param: { taskids: any[] }) {
    return ipc.iecInvokeWithDevice<{}>("cancelUploadDeviceFIle", param, deviceId);
  },
  // 下载文件（指定 deviceId）
  downloadDeviceFileByDevice(deviceId: string, param: { remoteParentPath: string; fileItems: UpadRpcFileDownloadItem[] }) {
    return ipc.iecInvokeWithDevice<{}>("downloadDeviceFile", param, deviceId);
  },
  // 取消下载（指定 deviceId）
  cancelDownloadDeviceFileByDevice(deviceId: string, param: { taskids: any[] }) {
    return ipc.iecInvokeWithDevice<{}>("cancelDownloadDeviceFile", param, deviceId);
  },
  // 导入下载列表（指定 deviceId）
  importDownloadDeviceFile(deviceId: string, param: { path: string }) {
    console.log("devicefileApi.importDownloadDeviceFile");
    return ipc.iecInvokeWithDevice<{}>("importDownloadDeviceFile", param, deviceId);
  },
  // 导出下载列表（指定 deviceId）
  exportDownloadDeviceFile(deviceId: string, param: { path: string; data: FileItem[] }) {
    console.log("devicefileApi.exportDownloadDeviceFile");
    return ipc.iecInvokeWithDevice<{}>("exportDownloadDeviceFile", param, deviceId);
  }
};

export { devicefileApi };
