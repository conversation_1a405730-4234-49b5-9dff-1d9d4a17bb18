import { DeviceItem } from "@/stores/modules/deviceItem";
import { FileItem, UpadRpcFileDownloadItem } from "../debug/fileitem";
import { ParamItem } from "../debug/diffitem";

/**
 * 批量任务
 */
export interface MatrixStatus {
  devicelist: DeviceItem[];
  selectDeviceIds: string[];
  downlist: FileItem[];
  selectDownIds: string[];
  paramList: ParamItem[];
  selectParamIds: string[];
}
/**
 * 批量任务
 */
export interface MatrixTaskItem {
  device: DeviceItem;
  downlist: UpadRpcFileDownloadItem[];
  paramList: any[];
  isReboot: boolean;
}
/**
 * 批量任务
 */
export interface FucntionItem {
  key: string;
  name: string;
  icon: string;
  desc: string;
  component: string;
}
