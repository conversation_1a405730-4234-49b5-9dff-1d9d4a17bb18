/**
 * @description 权限判断 hooks
 * @license Apache License Version 2.0
 */

import { useAuthStore } from "@/stores/modules";
import { isArray } from "@/utils/is";

export const useAuthButtons = () => {
  const authStore = useAuthStore();
  const authButtons = authStore.authButtonListGet; // 权限按钮列表

  /**
   * @func hasPermission
   * @desc   权限判断是否能看到这个按钮，同时后端也做了校验，前端只是显示与不显示
   * @param {}   permission 按钮的权限码，可以是单个字符串，也可以是数组
   * @param {}   and 是否完全匹配，默认false，即只要有一个权限码通过则通过，如果为 true，则必须每个权限码都通过
   * 使用方法：
   * 例如 buttonCodeList 的数据为： ['button1', 'button2', 'button3']
   * 想要判断 button1 的权限，可以写成：hasPerm('button1')
   * 想要判断 button1 或 button2 的权限，可以写成：hasPerm(['button1', 'button2' ])
   * 想要判断 button1 与 button2 的权限，可以写成：hasPerm(['button1', 'button2' ], true)
   * @return {}
   */
  const hasPerm = (permission: string[] | string, and: boolean = false) => {
    //如果是数组，则需要判断数组中的每一项权限
    if (isArray(permission)) {
      const fn = and ? "every" : "some"; //some表示只要有一个权限通过即可，every表示必须每个权限都通过
      return permission[fn](item => authButtons.includes(item)); //多个权限
    } else {
      return authButtons.includes(permission); //单个权限
    }
  };

  return { hasPerm };
};
