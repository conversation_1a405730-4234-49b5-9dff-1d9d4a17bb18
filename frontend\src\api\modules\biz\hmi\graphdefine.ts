import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/hmi/graphdefine/");

const graphDefineApi = {
  // 获取组态文件
  get() {
    return ipc.invoke<any>("get");
  },
  // 添加组态
  save(equipmentData: string) {
    return ipc.invoke<any>("save", equipmentData);
  },
  // 重命名组态
  delete(param: string) {
    return ipc.invoke<any>("delete", param);
  }
};

export { graphDefineApi };
