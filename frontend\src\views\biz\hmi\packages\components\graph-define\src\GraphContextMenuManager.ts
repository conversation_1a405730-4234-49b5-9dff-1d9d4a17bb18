import { Graph } from "@antv/x6";
import { ContextMenuItem, ContextMenuItemType } from "../../../graph/Graph";
import { getSelectedCells, isGroup } from "../../..//graph/GraphUtil";
import GrapgGroup from "../../../graph/GraphGroup";

class GraphContextMenuManager {
  private t: (key: string) => string;

  constructor(t: (key: string) => string) {
    this.t = t;
  }

  createContextMenu(graph: Graph): ContextMenuItem[] {
    // 获取当前选中的节点
    const cells = getSelectedCells(graph);
    if (!cells || cells.length == 0) {
      return [];
    }
    const items: ContextMenuItem[] = [];
    let canGroup = false;
    if (cells.length > 1) {
      canGroup = true;
    }
    let canUngroup = false;
    // 只有选择单个时才可解组
    if (cells.length == 1) {
      for (const cell of cells) {
        if (isGroup(cell.value)) {
          canUngroup = true;
          break;
        }
      }
    }
    items.push({
      title: this.t("graphDefine.contextMenu.group"),
      type: ContextMenuItemType.GROUP,
      enable: canGroup
    });
    items.push({
      title: this.t("graphDefine.contextMenu.ungroup"),
      type: ContextMenuItemType.UNGROUP,
      enable: canUngroup
    });
    items.push({
      title: this.t("graphDefine.contextMenu.setStatus"),
      type: ContextMenuItemType.EQUIPMENT_STATUS,
      enable: true
    });

    return items;
  }
  trigger(item: ContextMenuItem, graph: Graph, graphGroup: GrapgGroup) {
    switch (item.type) {
      case ContextMenuItemType.GROUP:
        graphGroup.group();
        break;
      case ContextMenuItemType.UNGROUP:
        graphGroup.ungroup();
        break;
    }
  }
}

export default GraphContextMenuManager;
