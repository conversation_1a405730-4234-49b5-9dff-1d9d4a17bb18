/**
 * @description 菜单选择器接口
 * @license Apache License Version 2.0
 */

/** 菜单选择器属性 */
export interface MenuSelectProps {
  /** 菜单对应的值 */
  menuValue: any;
  /** 是否可清除 */
  clearable?: boolean;
  /** 菜单选择器占位符 */
  placeholder?: string;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否严格模式,为true则只能选择子节点 */
  checkStrictly?: boolean;
  /** 是否显示全部选项,默认为 false */
  showAll?: boolean;
  /** 组织树api */
  menuTreeApi: (data?: any) => Promise<any>;
}
