import { ReqId, ReqPage, ResPage, SysConfig } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/ops/config/");

const sysConfigApi = {
  /** 获取系统配置 */
  sysBaseList() {
    return http.get<SysConfig.ConfigInfo[]>("sysBaseList", {}, { loading: false });
  },
  /** 获取配置列表 */
  list() {
    return http.get<SysConfig.ConfigInfo[]>("list", {}, { loading: false, cancel: false });
  },
  // 配置批量更新
  configEditForm(params: SysConfig.ConfigInfo[]) {
    return http.post("editBatch", params);
  },
  /** 分页获取其他配置 */
  page(params: ReqPage) {
    return http.get<ResPage<SysConfig.ConfigInfo>>("page", params);
  },
  /** 删除配置 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  }
};

export { sysConfigApi };
