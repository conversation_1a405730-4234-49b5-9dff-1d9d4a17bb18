import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0A00",
  width: 17,
  height: 30,
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "e",
      attrs: {
        ...calculateEllipse(0, 0, 17, 17)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "e",
      attrs: {
        ...calculateEllipse(0, 12, 17, 17)
      }
    }
  ],
  attrs: {
    e: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
