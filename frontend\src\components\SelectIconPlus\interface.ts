/**
 * @description 接口
 * @license Apache License Version 2.0
 */
export type IconPrefixType = "ep" | "local" | "ant-design" | "et" | "flat-color-icons" | "eva" | "zondicons" | "uiw" | "line-md";

/** 图标选择器属性 */
export interface SelectIconProps {
  /** 图标对应的值 */
  iconValue: string;
  /** 图标选择器标题 */
  title?: string;
  /** 是否可清除 */
  clearable?: boolean;
  /** 图标选择器占位符 */
  placeholder?: string;
}

/** 图标列表信息 */
export interface SelectIconIconsInfo {
  /** 图标前缀 */
  prefix: string;
  /** 图标名称列表 */
  icons: string[];
}

/** 图标选择器tabs信息 */
export interface SelectIconTabs {
  /** tab标题 */
  label: string;
  /** 图标前缀 */
  prefix: IconPrefixType;
}
