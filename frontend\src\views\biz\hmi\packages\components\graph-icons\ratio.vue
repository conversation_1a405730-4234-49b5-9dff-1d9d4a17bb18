<template>
  <svg
    t="1743042984494"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="12500"
    :width="props.width"
    :height="props.height"
  >
    <path
      :fill="props.color"
      d="M166.528 193.6L292.864 192v640h-63.616V254.4L128 256zM742.528 192h126.336v640h-63.616V254.4H704z"
      p-id="12501"
    ></path>
    <path :fill="props.color" d="M512 320h64v64H512zM512 640h64v64H512z" p-id="12502"></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";
const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
