<template>
  <div class="item-main" @click="openDrawer">
    <div class="icon-container">
      <svg-icon icon="eva:color-palette-fill"></svg-icon>
      <span v-if="globalStore.checkColumnLayout()" class="icon-text">{{ t("layout.header.themeSetting.title") }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import mittBus from "@/utils/mittBus";
import { useGlobalStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const globalStore = useGlobalStore();
// const place = defineModel<toolTipTypes>();
const openDrawer = () => {
  mittBus.emit("openThemeDrawer");
};
</script>

<style scoped lang="scss">
.item-main {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  cursor: pointer;
}
.icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.icon-text {
  margin-top: 6px;
  font-size: 12px;
}
</style>
