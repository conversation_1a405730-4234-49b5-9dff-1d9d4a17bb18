import {
  CellData,
  CellEquipmentData,
  EquipmentConfig,
  EquipmentData,
  EquipmentStatus,
  EquipmentType,
  FontAttrValue,
  GraphConstants,
  GraphJsonData,
  NodeAttrValue,
  TriggerEventType
} from "./Graph";
import { Cell, Graph, Node, Rectangle } from "@antv/x6";
import { CommonPlugin } from "./graphplugin";
import GrapgGroup from "./GraphGroup";

export const isTextShape = (cell: Cell) => {
  return cell.shape == "text-block";
};
export const getNodeAttrValue = (node: Node): NodeAttrValue => {
  let bgColor = node.getAttrByPath("body/fill") as string;
  if (!bgColor || bgColor == "none") {
    bgColor = "";
  }
  let borderColor = node.getAttrByPath("body/stroke") as string;
  if (!borderColor || borderColor == "none") {
    borderColor = "";
  }
  let borderWidth = node.getAttrByPath("body/strokeWidth");
  const size = node.getSize();
  const position = node.getPosition();
  // 判断是否还有字体
  let text;
  let fontValue: FontAttrValue;
  text = node.getAttrByPath("label/text");
  fontValue = {
    text: text ? (text as string) : "",
    fontSize: toNumber(node.getAttrByPath("text/fontSize"), 12),
    fontColor: node.getAttrByPath("text/fill"),
    fontFamily: node.getAttrByPath("text/fontFamily")
  };

  const value: NodeAttrValue = {
    bgColor: bgColor,
    borderWidth: toInt(toNumber(borderWidth, 1)),
    borderColor: borderColor,
    borderDasharray: node.getAttrByPath("body/strokeDasharray"),
    width: toInt(size.width),
    height: toInt(size.height),
    x: toInt(position.x),
    y: toInt(position.y),
    rx: toNumber(node.getAttrByPath("body/rx"), 0),
    ry: toNumber(node.getAttrByPath("body/ry"), 0),
    angle: node.getAngle(),
    zIndex: toInt(toNumber(node.getZIndex(), 0)),
    fontValue: fontValue,
    lineHeight: toInt(toNumber(node.getAttrByPath("body/strokeWidth"), 1)),
    lineColor: node.getAttrByPath("body/stroke")
  };
  return value;
};
const toInt = (value: number): number => {
  return parseInt(value + "");
};
const toNumber = (value: unknown, dvalue: number): number => {
  if (value == undefined || value == null) {
    return dvalue;
  }
  try {
    return Number(value);
  } catch (e) {
    return dvalue;
  }
};

const normalizeAngle = (angle: number) => {
  return ((angle % 360) + 360) % 360;
};

export const calculateRect = (x: number, y: number, width: number, height: number) => {
  return { x: x, y: y, width: width, height: height };
};
/**
 *
 * @param x
 * @param y
 * @param width
 * @param height
 * @param startAngle
 * @param endAngle 跨度的角度，非end角度
 * @param sweep 1=顺时针，0=逆时针画
 * @returns
 */
export const calculateArc = (x: number, y: number, width: number, height: number, startAngle: number, arcAngle: number, sweep?: number) => {
  let endAngle = arcAngle + startAngle;
  startAngle = normalizeAngle(startAngle);
  endAngle = normalizeAngle(endAngle);
  // 计算椭圆的半径
  const rx = width / 2;
  const ry = height / 2;
  // 计算弧的起点和终点
  const startRadians = (Math.PI / 180) * startAngle;
  const endRadians = (Math.PI / 180) * endAngle;
  const startX = x + rx + rx * Math.cos(startRadians);
  const startY = y + ry - ry * Math.sin(startRadians);
  const endX = x + rx + rx * Math.cos(endRadians);
  const endY = y + ry - ry * Math.sin(endRadians);
  // 计算角度跨度
  let angleDiff = endAngle - startAngle;
  if (angleDiff < 0) {
    angleDiff += 360;
  }
  // 确定 large-arc-flag(大弧，小弧) 和 sweep-flag(顺时针，逆时针)
  const largeArcFlag = angleDiff > 180 ? 1 : 0;
  let sweepFlag = endAngle > startAngle ? 1 : 0;
  if (sweep != undefined) {
    sweepFlag = sweep;
  }
  return `M ${startX} ${startY} A ${rx} ${ry} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`;
};
export const calculateTriangle = (x: number, y: number, width: number, height: number, direction?: "north" | "south" | "east" | "west") => {
  if (direction == "south") {
    return calculateTriangleSouth(x, y, width, height);
  } else if (direction == "east") {
    return calculateTriangleEast(x, y, width, height);
  } else if (direction == "west") {
    return calculateTriangleWest(x, y, width, height);
  }
  return calculateTriangleNorth(x, y, width, height);
};
const calculateTriangleNorth = (cx: number, cy: number, width: number, height: number) => {
  // 计算顶点坐标
  const x1 = cx + width / 2;
  const y1 = cy; // 顶部顶点
  const x2 = cx;
  const y2 = cy + height; // 左下顶点
  const x3 = cx + width;
  const y3 = cy + height; // 右下顶点
  return `${x1},${y1} ${x2},${y2} ${x3},${y3}`;
};
const calculateTriangleSouth = (cx: number, cy: number, width: number, height: number) => {
  // 计算顶点坐标
  const x1 = cx + width / 2;
  const y1 = cy + height / 2; // 顶部顶点
  const x2 = cx;
  const y2 = cy; // 左上顶点
  const x3 = cx + width;
  const y3 = cy; // 右上顶点
  return `${x1},${y1} ${x2},${y2} ${x3},${y3}`;
};

const calculateTriangleEast = (cx: number, cy: number, width: number, height: number) => {
  // 计算顶点坐标
  const x1 = cx;
  const y1 = cy + height / 2; // 顶部顶点
  const x2 = cx + width;
  const y2 = cy; // 左上顶点
  const x3 = cx + width;
  const y3 = cy + height; // 右下顶点
  return `${x1},${y1} ${x2},${y2} ${x3},${y3}`;
};

const calculateTriangleWest = (cx: number, cy: number, width: number, height: number) => {
  // 计算顶点坐标
  const x1 = cx + width;
  const y1 = cy + height / 2; // 顶部顶点
  const x2 = cx;
  const y2 = cy; // 左上顶点
  const x3 = cx;
  const y3 = cy + height; // 右下顶点
  return `${x1},${y1} ${x2},${y2} ${x3},${y3}`;
};

export const calculateEllipse = (x: number, y: number, width: number, height: number) => {
  const rx = width / 2;
  const ry = height / 2;
  const centerX = x + rx;
  const centerY = y + ry;
  return { cx: centerX, cy: centerY, rx: rx, ry: ry };
};

export const setCellVisible = (cell: Cell, visible: boolean) => {
  cell.setVisible(visible);
  if (isGroup(cell)) {
    const childs = cell.getChildren();
    if (childs) {
      childs.forEach(item => {
        item.setVisible(visible);
      });
    }
  }
};
export const isCbrDis = (type: EquipmentType) => {
  return EquipmentType.DIS == type || EquipmentType.GDIS == type || EquipmentType.CBR == type || EquipmentType.Fuse == type;
};

export const isGroup = (cell: Cell) => {
  return cell && cell.data && cell.data.type === "group";
};

export const setGroupChildData = (groupNode: Cell, childIds: string[]) => {
  groupNode.setData({ children: childIds });
};

export const renderGroupCells = (graph: Graph, childCells: Cell[], refCell: Cell, visible: boolean): Cell[] => {
  const showCells: Cell[] = [];
  // 按层次排序
  childCells.sort((c1, c2) => {
    const z1 = c1.getZIndex() ? c1.getZIndex()! : 0;
    const z2 = c2.getZIndex() ? c2.getZIndex()! : 0;
    if (z1 > z2) {
      return 1;
    } else if (z1 < z2) {
      return -1;
    }
    return 0;
  });
  // 添加子组件
  const childIds: string[] = [];
  let lastZIndex = 0;
  for (let x = 0; x < childCells.length; x++) {
    // 添加子组件
    const cell = childCells[x];
    const cloneCell = cell.clone() as Node;
    cloneCell.setPosition({ x: 0, y: 0 });
    // 重置zindex
    cloneCell.removeZIndex();
    const childNode = graph.addNode(cloneCell);
    refCell.addChild(childNode);
    childIds.push(childNode.id);
    lastZIndex = childNode.getZIndex() as number;
    showCells.push(childNode);
    if (!visible) {
      childNode.setVisible(false);
    }
  }
  // 设置新的组合关系
  setGroupChildData(refCell, childIds);
  // 置顶父组件
  refCell.setZIndex(lastZIndex + 1);
  const groupOp = new GrapgGroup(graph);
  groupOp.resize(refCell);
  return showCells;
};

export const setCellStatus = (cell: Cell, status: EquipmentStatus) => {
  cell.setData({ equipmentStatus: status });
};
export const getCellStatus = (cell: Cell): EquipmentStatus => {
  const data = cell.getData();
  if (data && data.equipmentStatus) {
    return data.equipmentStatus as EquipmentStatus;
  }
  return EquipmentStatus.NONE;
};

/**
 * 获取选中单元格，分组只统计父元素
 * @param graph
 * @returns
 */
export const getSelectedMainCells = (graph: Graph): Cell[] => {
  const selectedCells: Cell[] = [];
  const cells = graph.getSelectedCells();
  for (const cell of cells) {
    // 只算父元素
    if (cell.getParentId() != undefined) {
      continue;
    }
    selectedCells.push(cell);
  }
  return selectedCells;
};

/**
 * 获取选中单元格，分组只统计父元素
 * @param graph
 * @returns
 */
export const getSelectedCells = (graph: Graph): CellData[] => {
  const selectedCells: CellData[] = [];
  const cells = graph.getSelectedCells();
  for (const cell of cells) {
    // 只算父元素
    if (cell.getParentId() != undefined) {
      continue;
    }
    const scell: CellData = {
      value: cell,
      descendantCells: []
    };
    selectedCells.push(scell);
    const children = cell.getDescendants();
    if (children) {
      scell.descendantCells = children;
    }
  }
  return selectedCells;
};
/**
 * 自定义图符转cell数据
 * @param data
 * @param graph
 * @returns Set<Cell[]>集合，每个元素可包括多个cell
 */
export const equipmentDataToCell = (data: EquipmentData, graph: Graph): Set<Cell[]> => {
  const cellSet = new Set<Cell[]>();
  const components = data.components;
  if (!components) {
    return cellSet;
  }
  for (const item of components) {
    if (item.data) {
      const celldatas = cellDataToCells(item.data);
      const cells = graph.parseJSON(celldatas);
      cellSet.add(cells);
    }
  }
  return cellSet;
};

export const cellDataToCells = (selectCell: CellData): Cell[] => {
  const cells: Cell[] = [];
  cells.push(selectCell.value);
  if (selectCell.descendantCells) {
    selectCell.descendantCells.forEach(item => {
      cells.push(item);
    });
  }
  return cells;
};

export const getCellDataEquipmentConfig = (cell: Cell): EquipmentConfig | null => {
  const data = cell.getData();
  if (data && data.equipmentConfig) {
    return data.equipmentConfig;
  }
  return null;
};
export const setCellDataEquipmentData = (cell: Cell, cellEquipmentData: CellEquipmentData): void => {
  cell.setData({ equipmentData: cellEquipmentData });
};

export const getCellDataEquipmentData = (cell: Cell): CellEquipmentData | null => {
  const data = cell.getData();
  if (data && data.equipmentData) {
    return data.equipmentData;
  }
  return null;
};

export const initGraphData = (graph: Graph, jsonData: GraphJsonData) => {
  if (!graph) {
    return;
  }
  if (jsonData.data) {
    graph.fromJSON(jsonData.data);
  } else {
    graph.fromJSON({});
    graph.clearBackground();
    return;
  }
  if (jsonData.graph) {
    if (jsonData.graph.background && jsonData.graph.background.trim().length > 0) {
      graph.drawBackground({
        color: jsonData.graph.background
      });
    } else {
      graph.clearBackground();
    }
    if (jsonData.graph.grid) {
      if (jsonData.graph.grid.show) {
        if (jsonData.graph.grid.size > 0) {
          graph.setGridSize(jsonData.graph.grid.size);
        }
        graph.showGrid();
      } else {
        graph.hideGrid();
      }
    }
  }
};

export const getGraphJsonData = (graph: Graph): GraphJsonData => {
  let bgColor: string = "";
  if (graph.options.background && graph.options.background.color) {
    bgColor = graph.options.background.color as string;
  }
  const jsonData: GraphJsonData = {
    data: graph.toJSON(),
    graph: {
      background: bgColor,
      grid: {
        show: graph.options.grid.visible,
        size: graph.options.grid.size
      }
    }
  };
  return jsonData;
};
export const initGraphSize = (graph: Graph): void => {
  // 记录画布的初始大小
  const graphArea = graph.getGraphArea();
  const plugin = graph.getPlugin(GraphConstants.COMMON_PLUGIN_NAME);
  if (plugin) {
    const dataPlugin = plugin as CommonPlugin;
    dataPlugin.getData().graphArea = graphArea;
  }
  resizeGrap(graph);
};
/**
 * 画布大小适合全部组件
 * @param graph
 */
export const resizeGrap = (graph: Graph): void => {
  const bounds = graph.getContentBBox(); // 获取内容的边界框
  const padding = 10; // 留一些边距
  let newWidth: number | undefined;
  let newHeight: number | undefined;
  const graphArea = graph.getGraphArea();
  const plugin = graph.getPlugin(GraphConstants.COMMON_PLUGIN_NAME);
  let graphAreaRecore: Rectangle | undefined;
  if (plugin) {
    const dataPlugin = plugin as CommonPlugin;
    if (dataPlugin.getData().graphArea) {
      graphAreaRecore = dataPlugin.getData().graphArea as Rectangle;
    }
  }
  const boundWidth = bounds.x + bounds.width + padding;
  const boundHeight = bounds.y + bounds.height + padding;
  // 扩展宽度
  if (boundWidth >= graphArea.x + graphArea.width) {
    newWidth = boundWidth;
  }
  // 扩展高度
  if (boundHeight > graphArea.y + graphArea.height) {
    newHeight = boundHeight;
  }
  // 判断是否需要减少宽度
  if (graphAreaRecore) {
    if (newWidth == undefined) {
      if (boundWidth < graphAreaRecore.x + graphAreaRecore.width) {
        newWidth = graphAreaRecore.width;
      }
    }
    if (newHeight == undefined) {
      if (boundHeight < graphAreaRecore.y + graphAreaRecore.height) {
        newHeight = graphAreaRecore.height;
      }
    }
  }
  // 设置引用次数加1
  if (plugin) {
    const dataPlugin = plugin as CommonPlugin;
    if (dataPlugin.getData().resizeRefCount) {
      dataPlugin.getData().resizeRefCount = 1 + (dataPlugin.getData().resizeRefCount as number);
    } else {
      dataPlugin.getData().resizeRefCount = 1;
    }
  }
  // 调整画布的高度
  graph.resize(newWidth, newHeight);
};

export const isLine = (cell: Cell): boolean => {
  if (!cell) {
    console.log("check isLine cell is null", cell);
    return false;
  }
  const data = cell.getData();
  if (data && data.isLine == true) {
    return true;
  }
  return false;
};

export const setPathLineWidth = (strokeWdith: number, node: Node) => {
  let preStrokeWidth: number = node.getAttrByPath("body/strokeWidth");
  if (!preStrokeWidth) {
    preStrokeWidth = 0;
  }
  node.setAttrByPath("body/strokeWidth", strokeWdith);
  // 设置背景占比
  const size = node.getSize();
  size.height = 10 + strokeWdith;
  node.setSize(size);
};

export const increaseInvalidTriggerEvent = (graph: Graph, eventType: TriggerEventType) => {
  const plugin = graph.getPlugin("common");
  if (plugin) {
    const commonPlugin = plugin as CommonPlugin;
    commonPlugin.getPluginImpl().increaseInvalidTrigger(eventType);
  }
};

export const validTriggerEvent = (graph: Graph, eventType: TriggerEventType) => {
  const plugin = graph.getPlugin("common");
  if (plugin) {
    const commonPlugin = plugin as CommonPlugin;
    return commonPlugin.getPluginImpl().validTrigger(eventType);
  }
  return true;
};
