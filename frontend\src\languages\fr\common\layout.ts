export default {
  language: {
    title: "Langue",
    zh: "Chinois simplifié",
    en: "Anglais",
    es: "Espagnol",
    fr: "Français",
    tooltip: "Multilingue"
  },
  about: {
    title: "À propos",
    introduction: "Introduction",
    description:
      "Outil de débogage de plateforme haute performance de nouvelle génération développé avec les dernières technologies : Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc.",
    versionInfo: "Informations de version",
    toolName: "Nom de l'outil",
    version: "Numéro de version",
    machineCode: "Code Machine",
    loading: "Chargement...",
    machineCodeError: "Échec de récupération",
    copySuccess: "Code machine copié dans le presse-papiers",
    copyError: "Échec de la copie",
    versionFeatures: "Caractéristiques de version",
    features: {
      visualTool:
        "Inclut les fonctions de connexion d'outils visuels, visualisation d'informations d'appareils, paramètres, valeurs analogiques, états, télécommunications, télémétrie, télécommande, rapports, synchronisation d'appareils, import/export de valeurs fixes, débogage de variables",
      configTool:
        "Inclut les fonctions d'aperçu d'outils de configuration, ajout, édition, icônes personnalisées, association d'informations d'appareils",
      themeTool: "Inclut les fonctions de personnalisation de thème, petits outils IT, import/export de configuration d'appareils"
    }
  },
  footer: {
    copyright: "{version}"
  },
  header: {
    minimize: "Minimiser",
    maximize: "Maximiser",
    restore: "Restaurer",
    close: "Fermer",
    company: {
      name: "Sieyuan Electric",
      englishName: "Sieyuan"
    },
    collapse: {
      expand: "Développer les appareils",
      fold: "Réduire les appareils",
      expandTool: "Développer la liste des outils",
      foldTool: "Réduire la liste des outils"
    },
    breadcrumb: {
      home: "Accueil"
    },
    assemblySize: {
      title: "Paramètres de taille",
      default: "Par défaut",
      large: "Grand",
      small: "Petit"
    },
    avatar: {
      profile: "Centre personnel",
      switchApp: "Changer d'application",
      logout: "Se déconnecter",
      logoutConfirm: {
        title: "Conseils",
        message: "Êtes-vous sûr de vouloir vous déconnecter ?",
        confirm: "Confirmer",
        cancel: "Annuler"
      },
      logoutSuccess: "Déconnexion réussie !"
    },
    changeModule: {
      title: "Changer de module"
    },
    enginConfig: {
      configType: "Type de configuration",
      openDirectory: "Ouvrir le répertoire de fichiers",
      cancel: "Annuler",
      confirm: "Confirmer",
      all: "Tout",
      deviceList: "Liste des appareils",
      configureList: "Liste de configuration",
      exportSuccess: "Export de configuration réussi",
      importSuccess: "Import de configuration réussi",
      disconnectDeviceFirst: "Veuillez d'abord déconnecter les appareils connectés",
      overrideConfirm: "La liste de configuration existe déjà, voulez-vous la remplacer ?",
      warmTips: "Conseils",
      importConfigFile: "Importer le fichier de configuration"
    },
    userInfo: {
      title: "Informations personnelles",
      cancel: "Annuler",
      confirm: "Confirmer"
    },
    password: {
      title: "Modifier le mot de passe",
      cancel: "Annuler",
      confirm: "Confirmer"
    },
    globalSetting: {
      title: "Paramètres",
      tooltip: "Paramètres"
    },
    moreInfo: {
      title: "Plus",
      tooltip: "Plus",
      items: {
        importConfig: "Importer Config",
        printScreen: "Capture",
        search: "Recherche Menu",
        exportConfig: "Exporter Config",
        about: "À propos",
        help: "Aide"
      },
      importConfig: {
        title: "Importer la configuration du projet",
        placeholder: "Veuillez sélectionner le chemin du fichier de configuration à importer"
      },
      exportConfig: {
        title: "Exporter la configuration du projet",
        placeholder: "Veuillez sélectionner le répertoire d'export"
      }
    },
    searchMenu: {
      placeholder: "Recherche de menu : supporte le nom et le chemin du menu",
      empty: "Aucun menu"
    },
    theme: {
      title: "Thème",
      tooltip: "Thème"
    }
  },
  main: {
    maximize: {
      exit: "Quitter la maximisation"
    }
  },
  theme: {
    title: "Paramètres de mise en page",
    quickTheme: {
      title: "Paramètres de thème"
    },
    layoutSettings: {
      title: "Paramètres de mise en page"
    },
    layout: {
      title: "Style de mise en page",
      columns: "Colonnes",
      classic: "Classique",
      transverse: "Transversal",
      vertical: "Vertical"
    },
    global: {
      title: "Thème global",
      primary: "Couleur du thème",
      dark: "Mode sombre",
      grey: "Mode gris",
      weak: "Mode daltonien",
      special: "Mode spécial"
    },
    mode: {
      light: "Clair",
      dark: "Sombre"
    },
    interface: {
      title: "Paramètres d'interface",
      watermark: "Filigrane",
      breadcrumb: "Fil d'Ariane",
      breadcrumbIcon: "Icône du fil d'Ariane",
      tabs: "Barre d'onglets",
      tabsIcon: "Icône de la barre d'onglets",
      footer: "Pied de page",
      drawerForm: "Formulaire en tiroir"
    },
    presetThemes: {
      title: "Thèmes prédéfinis",
      default: {
        name: "Thème par défaut",
        description: "Thème bleu classique"
      },
      dark: {
        name: "Thème sombre",
        description: "Mode sombre pour les yeux"
      },
      techBlue: {
        name: "Bleu Tech",
        description: "Bleu technologique moderne"
      },
      deepBlue: {
        name: "Bleu Profond",
        description: "Bleu océan profond"
      },
      nature: {
        name: "Thème naturel",
        description: "Système vert frais"
      },
      forestGreen: {
        name: "Vert Forêt",
        description: "Vert forêt profond"
      },
      warm: {
        name: "Thème chaleureux",
        description: "Système orange chaleureux"
      },
      sunsetOrange: {
        name: "Orange Coucher",
        description: "Orange coucher de soleil"
      },
      elegant: {
        name: "Thème élégant",
        description: "Système violet noble"
      },
      lavender: {
        name: "Lavande",
        description: "Violet lavande doux"
      },
      sakura: {
        name: "Rose Sakura",
        description: "Rose sakura romantique"
      },
      rose: {
        name: "Rouge Rose",
        description: "Rouge rose passionné"
      },
      lime: {
        name: "Vert Citron",
        description: "Vert citron vibrant"
      },
      skyBlue: {
        name: "Bleu Ciel",
        description: "Bleu ciel clair"
      },
      eyeCare: {
        name: "Mode soin des yeux",
        description: "Thème gris pour les yeux"
      }
    },
    colors: {
      techBlue: {
        name: "Bleu technologique",
        description: "Sensation technologique moderne"
      },
      natureGreen: {
        name: "Vert naturel",
        description: "Fraîcheur naturelle"
      },
      vibrantOrange: {
        name: "Orange vibrant",
        description: "Chaleur et vitalité"
      },
      elegantPurple: {
        name: "Violet élégant",
        description: "Noble et élégant"
      },
      romanticPink: {
        name: "Rose romantique",
        description: "Doux et romantique"
      },
      freshCyan: {
        name: "Cyan frais",
        description: "Fraîcheur et élégance"
      },
      brightYellow: {
        name: "Jaune vif",
        description: "Vif et dynamique"
      },
      warmOrange: {
        name: "Orange chaleureux",
        description: "Chaleureux et confortable"
      },
      limeGreen: {
        name: "Vert citron vert",
        description: "Citron vert frais"
      },
      deepBlue: {
        name: "Bleu profond",
        description: "Profond et stable"
      },
      golden: {
        name: "Doré",
        description: "Doré classique"
      },
      chinaRed: {
        name: "Rouge chinois",
        description: "Rouge traditionnel"
      }
    }
  },
  tabs: {
    moreButton: {
      refresh: "Actualiser",
      closeCurrent: "Fermer l'actuel",
      closeLeft: "Fermer à gauche",
      closeRight: "Fermer à droite",
      closeOthers: "Fermer les autres",
      closeAll: "Fermer tout"
    }
  }
};
