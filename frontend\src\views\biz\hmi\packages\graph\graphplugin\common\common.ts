import { Disposable, IDisablable, Graph } from "@antv/x6";
import { CommonPluginDataInfo, TriggerEventType } from "../../Graph";
/**
 * 数据插件，不同层次传递数据使用
 * <AUTHOR>
 * @version 1.0 2025-03-11
 */
export class CommonImpl extends Disposable implements IDisablable {
  data: CommonPluginDataInfo;

  constructor(private readonly options: CommonPluginInfo.Options & { graph: Graph }) {
    super();
    this.data = options.data;
    if (!this.data.invalidTriggerRef) {
      this.data.invalidTriggerRef = new Map();
    }
  }

  get disabled() {
    return this.options.enabled !== true;
  }

  increaseInvalidTrigger(eventType: TriggerEventType) {
    const count = this.data.invalidTriggerRef!.get(eventType);
    if (count) {
      this.data.invalidTriggerRef?.set(eventType, count + 1);
    } else {
      this.data.invalidTriggerRef?.set(eventType, 1);
    }
  }
  validTrigger(eventType: TriggerEventType) {
    const count = this.data.invalidTriggerRef?.get(eventType);
    if (count && count > 0) {
      this.data.invalidTriggerRef?.set(eventType, count - 1);
      return false;
    }
    return true;
  }

  enable() {
    if (this.disabled) {
      this.options.enabled = true;
    }
  }

  disable() {
    if (!this.disabled) {
      this.options.enabled = false;
    }
  }

  //@Disposable.dispose()
  dispose() {
    console.log("dispose");
  }
}

export namespace CommonPluginInfo {
  export interface Options {
    enabled?: boolean;
    data: CommonPluginDataInfo;
  }
}
