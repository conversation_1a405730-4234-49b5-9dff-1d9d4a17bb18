import { <PERSON>, <PERSON>, Graph, <PERSON><PERSON>, <PERSON>le } from "@antv/x6";
import { GraphData, TriggerEventType } from "./Graph";
import { getSelectedMainCells, increaseInvalidTriggerEvent, isGroup, setGroupChildData } from "./GraphUtil";
/**
 * 组合操作
 * <AUTHOR>
 * @version 1.0 2025-03-27
 */
class GrapgGroup {
  graph: Graph;
  groupAngle: number = 0;
  constructor(graph: Graph) {
    this.graph = graph;
  }
  canGroup(): boolean {
    const cells = this.graph.getSelectedCells();
    if (!cells || cells.length < 2) {
      return false;
    }
    // 判断是一个组合的重复组合
    const filter = cells.find(item => {
      // 父元素为空，说明可能是未组合状态
      if (item.getParentId() == undefined) {
        // 检查是否含有子元素（自身是组合）
        return item.getChildCount() == 0;
      }
      return false;
    });
    return filter != undefined;
  }
  group() {
    if (!this.canGroup()) {
      return;
    }
    //const cells = this.graph.getSelectedCells();
    const cells = getSelectedMainCells(this.graph);
    // 查找选中框的bbox
    const divs = document.getElementsByClassName("x6-widget-selection-inner");
    if (!divs || divs.length == 0) {
      return;
    }
    const selectionContainer = divs[0];
    const groupNode = this.graph.addNode({
      shape: "rect",
      x: Dom.computeStyleInt(selectionContainer, "left"),
      y: Dom.computeStyleInt(selectionContainer, "top"),
      width: Dom.computeStyleInt(selectionContainer, "width"),
      height: Dom.computeStyleInt(selectionContainer, "height"),
      //zIndex: GraphConstants.GroupZIndex,
      attrs: {
        body: {
          class: "graph-group",
          fill: "transparent",
          stroke: "none"
        }
      },
      data: {
        type: "group"
      }
    });
    const childIds: string[] = [];
    for (const child of cells) {
      groupNode.addChild(child);
      childIds.push(child.id);
    }
    // 记录组合关系，解组还原组使用
    setGroupChildData(groupNode, childIds);
    this.setChildInfo(groupNode);
    // 组合后取消框选，防止能点击内部节点
    increaseInvalidTriggerEvent(this.graph, TriggerEventType.UNSELECT);
    this.graph.unselect(cells);
    // 选中父节点
    increaseInvalidTriggerEvent(this.graph, TriggerEventType.SELECT);
    this.graph.select(groupNode);
  }
  ungroup() {
    const cells = getSelectedMainCells(this.graph);
    if (!cells || cells.length != 1) {
      return;
    }
    const cell = cells[0];
    if (!isGroup(cell)) {
      return;
    }
    // 需要还原原来的子组，因为addChild取消了原来的组
    const childCells = cell.getChildren();
    if (childCells) {
      for (const child of childCells) {
        child.setParent(null);
      }
      // 组合时只组合了父元素则此处可注释
      /*
      // 解析当前层是否还有组合，需要还原为组合
      const pCells = childCells.filter(item => {
        const data = item.getData();
        if (data && Array.isArray(data.children) && data.children.length > 0) {
          return true;
        }
        return false;
      });
      pCells.forEach(element => {
        const childs = childCells.filter(item => {
          return (element.getData().children as string[]).includes(item.id);
        });
        // 绑定数据
        element.setChildren(childs);
      });
      // 非组合的取消组合的属性
      */
    }
    cell.setChildren(null);
    this.graph.removeCells([cell]);
  }
  // 计算组内组件的位置
  setChildInfo(groupCell: Cell) {
    const childCells = groupCell.getChildren();
    if (!childCells) {
      return;
    }
    this.cellGroupStyle(groupCell);
  }
  // 计算组内组件的位置
  cellGroupStyle(groupCell: Cell) {
    const childCells = groupCell.getChildren();
    if (!childCells) {
      return;
    }
    const position = groupCell.getBBox();
    childCells.forEach(item => {
      const box = item.getBBox();
      let data = item.getData() as Partial<GraphData>;
      if (!data) {
        data = {};
      }
      data.groupStyle = {
        left: this.toPrecent((box.x - position.x) / position.width),
        top: this.toPrecent((box.top - position.top) / position.height),
        width: this.toPrecent(box.width / position.width),
        height: this.toPrecent(box.height / position.height)
      };
      item.setData(data, { silent: true });
    });
  }
  toPrecent(val: number) {
    return val * 100;
  }
  /**
   * 子组件改变大小
   * @param cell
   */
  resize(cell: Cell) {
    // 支持递归所有子组件
    const queue: { cells: Cell[]; parent: Cell }[] = [
      {
        cells: cell.getChildren() || [],
        parent: cell
      }
    ];
    while (queue.length > 0) {
      const item = queue.pop()!;
      const cells = item.cells;
      const pbbox = item.parent.getBBox();
      cells.forEach(item => {
        const node = item as Node;
        const data = item.getData();
        if (!data) {
          return;
        }
        const groupStyle = (data as GraphData).groupStyle;
        node.setSize((pbbox.width * groupStyle.width) / 100, (pbbox.height * groupStyle.height) / 100);
        node.setPosition((pbbox.width * groupStyle.left) / 100 + pbbox.x, (pbbox.height * groupStyle.top) / 100 + pbbox.y);
        const childCell = node.getChildren();
        // 遍历子组件的子组件
        if (childCell && childCell.length > 0 && isGroup(node)) {
          queue.push({
            cells: childCell,
            parent: node
          });
        }
      });
    }
  }

  /**
   * 子组件旋转
   * @param cell
   */
  rotate(cell: Cell) {
    this.groupAngle = (cell as Node).getAngle();
  }
  /**
   * 子组件旋转
   * @param cell
   */
  rotating(cell: Cell) {
    // 支持递归所有子组件
    const queue: { cells: Cell[]; parent: Cell; parentStartAngle: number }[] = [
      {
        cells: cell.getChildren() || [],
        parent: cell,
        parentStartAngle: 0
      }
    ];
    // 以顶层父类为旋转点
    const bbox = cell.getBBox();
    // 本次旋转后的角度
    const angleEnd = (cell as Node).getAngle();
    // 本次旋转了多少角度
    const diffAngle = angleEnd - this.groupAngle;
    //console.log("开始旋转，旋转前{},旋转后{}", this.groupAngle, angleEnd);
    this.groupAngle = angleEnd;
    while (queue.length > 0) {
      const item = queue.pop()!;
      item.cells.forEach(itemCell => {
        const node = itemCell as Node;
        // 子组件的子组件
        const appendItem = {
          cells: node.getChildren() || [],
          parent: node,
          parentStartAngle: 0
        };
        queue.push(appendItem);
        const data = itemCell.getData();
        if (!data) {
          return;
        }
        let theta = diffAngle;
        // 原角度+本次旋转的角度，必须使用absoulte：true
        theta = Angle.normalize(theta + node.getAngle());
        node.rotate(theta, {
          absolute: true,
          center: { x: bbox.center.x, y: bbox.center.y }
        });
      });
    }
  }
}

export default GrapgGroup;
