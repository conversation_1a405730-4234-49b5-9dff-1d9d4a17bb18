<script setup lang="ts">
import { AES, RC4, Rabbit, TripleDES, enc } from "crypto-js";
import { getTextMaxHeight } from "@/utils/index";
import { computedCatch } from "@/composable/computed/catchedComputed";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const algos = { AES, TripleDES, Rabbit, RC4 };
const cypherInput = ref("test");
const cypherAlgo = ref<keyof typeof algos>("AES");
const cypherSecret = ref("key");
const cypherOutput = computed(() => algos[cypherAlgo.value].encrypt(cypherInput.value, cypherSecret.value).toString());
const decryptInput = ref("U2FsdGVkX1/1qGLMmZxS10MyYK8itsnP8JFnb5NVZ1o=");
const decryptAlgo = ref<keyof typeof algos>("AES");
const decryptSecret = ref("key");
const [decryptOutput, decryptError] = computedCatch(
  () => algos[decryptAlgo.value].decrypt(decryptInput.value, decryptSecret.value).toString(enc.Utf8),
  {
    defaultValue: "",
    defaultErrorMessage: t("tools.encryption.decryptError")
  }
);
</script>

<template>
  <div class="encryption-container">
    <div class="head">
      {{ t("tools.encryption.title") }}
      <div class="sub-head">{{ t("tools.encryption.description") }}</div>
    </div>
    <div class="main-page" :style="getTextMaxHeight('--heightLine', 523, true)">
      <c-card :title="t('tools.encryption.encrypt')" style="width: 100%; margin-right: 10px">
        <div class="encrypt-page">
          <c-input-text v-model:value="cypherInput" :label="t('tools.encryption.inputText')" rows="4" multiline raw-text monospace autosize flex-1 />
          <div class="select-page">
            <c-input-text v-model:value="cypherSecret" :label="t('tools.encryption.key')" clearable multiline />
            <p style="width: 100%; text-align: left">{{ t("tools.encryption.algorithm") }}</p>
            <el-select v-model="cypherAlgo" style="width: 100%">
              <el-option
                v-for="item in Object.keys(algos).map(label => ({ label, value: label }))"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <c-input-text :label="t('tools.encryption.outputText')" :value="cypherOutput" rows="3" multiline monospace readonly autosize mt-5 />
      </c-card>
      <c-card :title="t('tools.encryption.decrypt')" style="width: 100%">
        <div class="decrypt-page">
          <c-input-text
            v-model:value="decryptInput"
            :label="t('tools.encryption.decryptInputText')"
            rows="3"
            multiline
            raw-text
            monospace
            autosize
            flex-1
          />
          <div class="select-page">
            <c-input-text v-model:value="decryptSecret" :label="t('tools.encryption.decryptKey')" clearable multiline />
            <p style="width: 100%; text-align: left">{{ t("tools.encryption.decryptAlgorithm") }}</p>
            <el-select v-model="decryptAlgo" style="width: 100%">
              <el-option
                v-for="item in Object.keys(algos).map(label => ({ label, value: label }))"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <div v-if="decryptError" style="color: red">
          {{ t("tools.encryption.decryptError") }}
        </div>
        <c-input-text
          v-else
          :label="t('tools.encryption.decryptOutputText')"
          :value="decryptOutput"
          rows="3"
          multiline
          monospace
          readonly
          autosize
          mt-5
        />
      </c-card>
    </div>
  </div>
</template>
<style scoped lang="scss">
@import "@/styles/utils";
.encryption-container {
  width: 100%;
  height: 100%;
}
.head {
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.main-page {
  @include flex(row, flex-start, flex-start);

  gap: 20px;
  min-height: 600px;
  :deep(.c-card) {
    padding: 20px;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transition: box-shadow 0.3s ease;
    &:hover {
      box-shadow: 0 6px 20px rgb(0 0 0 / 8%);
    }
    .c-card-header {
      padding: 16px 20px 12px;
      padding-bottom: 12px;
      margin: -20px -20px 16px;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-color-primary);
      background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
      border-bottom: 1px solid var(--el-border-color-lighter);
      border-radius: 12px 12px 0 0;
    }
  }

  // 文本输入框样式优化，增加背景色
  :deep(.c-input-text) {
    margin-bottom: 16px;
    .input-wrapper {
      min-height: 120px;
      background: var(--el-fill-color-extra-light);
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      transition: all 0.2s ease;
      &:hover {
        background: var(--el-fill-color-light);
        border-color: var(--el-border-color-hover);
        box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
      }
      &:focus-within {
        background: var(--el-bg-color);
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 3px var(--el-color-primary-light-9);
      }
      textarea,
      input {
        font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
        font-size: 13px;
        line-height: 1.5;
        color: var(--el-text-color-primary);
        background: transparent;
        &::placeholder {
          color: var(--el-text-color-placeholder);
        }
        &[readonly] {
          color: var(--el-text-color-regular);
          background: var(--el-fill-color-lighter);
        }
      }
    }
    .label {
      margin-bottom: 6px;
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-regular);
    }
  }
  .encrypt-page,
  .decrypt-page {
    @include flex(row, flex-start, flex-start);

    gap: 16px;
    margin-bottom: 20px;
    .select-page {
      @include flex(column, flex-start, stretch);

      gap: 12px;
      min-width: 200px;
      padding: 16px;
      background: var(--el-fill-color-lighter);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      p {
        margin: 0;
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
      :deep(.el-select) {
        .el-input__wrapper {
          background: var(--el-bg-color);
          border-radius: 6px;
          transition: all 0.2s ease;
          &:hover {
            box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
          }
        }
      }
      :deep(.c-input-text .input-wrapper) {
        min-height: 80px;
        background: var(--el-bg-color);
      }
    }
  }

  // 错误提示样式
  div[style*="color: red"] {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-error) !important;
    background: var(--el-color-error-light-9);
    border: 1px solid var(--el-color-error-light-7);
    border-radius: 8px;
    &::before {
      font-size: 16px;
      content: "⚠️";
    }
  }
}
.main-page-tabs {
  @include flex(row, flex-start, flex-start);

  gap: 20px;
  min-height: 600px;
}

// 响应式设计
@media (width <= 1200px) {
  .main-page {
    @include flex(column, flex-start, stretch);

    gap: 20px;
    :deep(.c-card) {
      width: 100% !important;
      margin-right: 0 !important;
    }
    .encrypt-page,
    .decrypt-page {
      @include flex(column, flex-start, stretch);
      .select-page {
        flex-direction: row;
        gap: 12px;
        align-items: end;
        width: 100%;
        min-width: auto;
        > div {
          flex: 1;
        }
      }
    }
  }
}

@media (width <= 768px) {
  .main-page {
    gap: 16px;
    :deep(.c-card) {
      padding: 16px;
      .c-card-header {
        padding: 12px 16px 8px;
        margin: -16px -16px 12px;
      }
    }
    .encrypt-page,
    .decrypt-page {
      .select-page {
        flex-direction: column;
        align-items: stretch;
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .main-page {
    :deep(.c-card) {
      box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
      &:hover {
        box-shadow: 0 6px 20px rgb(0 0 0 / 30%);
      }
      .c-card-header {
        background: linear-gradient(135deg, var(--el-color-primary-dark-2) 0%, var(--el-color-primary) 100%);
      }
    }
  }
}
</style>
