<template>
  <div class="table-box">
    <!-- 差异对比弹窗 -->

    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getYcList"
      :init-param="initParam"
      :request-auto="false"
      highlight-current-row
      :data-callback="dataCallback"
      row-key="name"
      :pagination="true"
      table-key="remoteTelemetry"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="">
        <el-checkbox v-model="refreshCheck" :label="t('device.remoteTelemetry.autoRefresh')" size="large" />
        <el-button type="primary" plain :icon="Refresh" @click="refreshYcList">{{ t("device.remoteTelemetry.refresh") }}</el-button>
        <el-button type="success" :icon="Download" @click="exportYcList">{{ t("device.remoteTelemetry.export") }}</el-button>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive, onMounted, onBeforeUnmount, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Download, Refresh } from "@element-plus/icons-vue";
import { osControlApi } from "@/api/modules/biz/os";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { ResultData } from "@/api";
import { remoteSigalApi } from "@/api/modules/biz/debug/remoteoperate";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const { debugIndex } = useDebugStore();
import { useConfigStore } from "@/stores/modules";
const { paramInfo } = useConfigStore();
const progressDialog = ref();
const { addConsole } = useDebugStore();
// ProTable 实例
const proTable = ref<ProTableInstance>();

const refreshCheck = ref(false);
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const { t } = useI18n();

// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  try {
    hideLoading();
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }
    console.log("dataCallback", data.list);

    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  }
};

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};

const props = defineProps<{ deviceId: string }>();

const getYcList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  const comp = debugIndex.compData.get(props.deviceId);
  newParams.names = [comp?.pname, comp?.name];
  newParams.type = "submenu";
  newParams.id = props.deviceId;
  delete newParams.createTime;
  console.log(newParams);
  try {
    const result: ResultData<DebugInfoItem[]> = await remoteSigalApi.remoteSigalGroupValByDevice(props.deviceId, newParams);
    if (result.code != 0) {
      ElMessage.error(result.msg);
      return { list: [], total: 0 };
    }
    return result || { list: [], total: 0 };
  } catch (error) {
    console.error("Error fetching data:", error);
    return { list: [], total: 0 };
  }
};

const refreshYcList = async () => {
  proTable.value?.getTableList();
};

const exportYcList = async () => {
  const comp = debugIndex.compData.get(props.deviceId);
  const defaultPath = t("device.remoteTelemetry.exportFileName") + "_" + (comp?.label || "") + ".xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.remoteTelemetry.exportTitle"),
    defaultPath,
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] }
    ]
  });
  if (!selectPath) return;
  const path = String(selectPath);
  console.log(t("device.remoteTelemetry.selectPathLog"), selectPath);

  showLoading();

  try {
    const comp = debugIndex.compData.get(props.deviceId);
    const names = [comp?.pname, comp?.name];
    const result = await remoteSigalApi.exporrAllGroupValByDevice(props.deviceId, {
      path,
      names
    });
    if (Number(result.code) === 0) {
      addConsole(t("device.remoteTelemetry.exportSuccessWithPath") + path);
      ElMessageBox.alert(t("device.remoteTelemetry.exportSuccess"), t("device.remoteTelemetry.tip"), {
        confirmButtonText: t("device.remoteTelemetry.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("device.remoteTelemetry.exportFailed"));
      ElMessageBox.alert(t("device.remoteTelemetry.exportFailed"), t("device.remoteTelemetry.tip"), {
        confirmButtonText: t("device.remoteTelemetry.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.remoteTelemetry.exportFailedWithError"), error);
    ElMessageBox.alert(t("device.remoteTelemetry.exportFailed"), t("device.remoteTelemetry.tip"), {
      confirmButtonText: t("device.remoteTelemetry.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { prop: "index", label: t("device.remoteTelemetry.sequence"), fixed: "left", width: 70 },
  {
    prop: "name",
    label: t("device.remoteTelemetry.name"),
    search: {
      el: "input",
      tooltip: t("device.remoteTelemetry.searchName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    label: t("device.remoteTelemetry.description"),
    search: {
      el: "input",
      tooltip: t("device.remoteTelemetry.searchDesc"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "value",
    width: 200,
    label: t("device.remoteTelemetry.value"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 将值转换为数字进行比较
      const numA = parseFloat(a.value) || 0;
      const numB = parseFloat(b.value) || 0;
      return numA - numB;
    },
    render: scope => {
      return (
        <div>
          <el-text type="primary">{scope.row.value}</el-text>
        </div>
      );
    },
    search: {
      el: "input",
      tooltip: t("device.remoteTelemetry.searchValue"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "unit",
    width: 120,
    label: t("device.remoteTelemetry.unit"),
    isShow: false,
    render: scope => {
      return (
        <div>
          <el-text>{scope.row.unit || "-"}</el-text>
        </div>
      );
    }
  },
  {
    prop: "quality",
    width: 200,
    label: t("device.remoteTelemetry.quality")
  }
]);

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, paramInfo.STATE_REFRESH_TIME); // 每 5 秒刷新一次
};
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};
onMounted(() => {
  proTable.value!.search();
});
onBeforeUnmount(() => {
  stopRefreshTimer();
});
watch(refreshCheck, newValue => {
  if (newValue) {
    // 自动刷新被取消时，停止定时器
    startRefreshTimer();
  } else {
    stopRefreshTimer();
  }
});
watch(
  () => debugIndex.compData.get(props.deviceId),
  newValue => {
    if (newValue) {
      proTable.value?.reset();
    }
  }
);
</script>
<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.pro-table td,
.pro-table th {
  padding: 5px 0;
  line-height: 8px;
}
</style>
