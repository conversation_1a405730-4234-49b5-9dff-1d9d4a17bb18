import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "1500",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(21.33, 6, 16.67, 17)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 6, 16.67, 17)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 18.67,
        y1: 36,
        x2: 18.67,
        y2: 28.83
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(10.67, 0, 16.67, 17)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(10.67, 12, 16.67, 17)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
