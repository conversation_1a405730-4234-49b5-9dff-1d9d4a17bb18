import { DebugInfoItem } from "../debug/debuginfo";
import { SelectRequestData } from "../debug/remote";

/**
 * 定义组件项
 */
export interface EquipmentItem {
  data: CellData;
  img: string;
  equipmentStatus: EquipmentStatus;
}

/**
 * 定义组件信息
 */
export interface EquipmentInfo {
  name: string;
  type: string;
  components: EquipmentItem[];
}

export interface EquipmentData extends EquipmentInfo {
  id: string;
}
export interface Cell {
  [key: string]: any;
}
export interface CellData {
  /** ancestor cell */
  value: Cell;
  /** all children cells */
  descendantCells: Cell[];
}
export enum EquipmentStatus {
  OPEN = "open",
  CLOSE = "close",
  NONE = ""
}

// 项目相关接口
export interface GraphRegisterInfo {
  /** 每个短地址对应的数据项 */
  saddrMap: Map<string, DebugInfoItem>;
  /** 短地址对应的vkey */
  vkeys: string[];
}

export interface GraphRequest {
  deviceId: string;
}
export interface GraphRegisterRequest extends GraphRequest {
  hmiId: string;
  saddrs: string[];
}

export interface GraphRegisterResponse {
  registerId: string;
  notFoundSaddrs: string[];
}

export interface GraphGetDataRequest extends GraphRequest {
  registerId: string;
}

export interface GraphRemoteControlRequest extends GraphRequest {
  data: SelectRequestData;
}

export interface GraphRemoteSetRequest extends GraphRequest {
  data: SelectRequestData;
}
