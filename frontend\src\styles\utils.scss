@import "var";

@mixin box($w: 100%, $h: 100%, $minW: null, $maxW: null, $minH: null, $maxH: null) {
  @if $w {
    width: $w;
  }

  @if $h {
    height: $h;
  }

  @if $minW {
    min-width: $minW;
  }

  @if $maxW {
    max-width: $maxW;
  }

  @if $minH {
    min-height: $minH;
  }

  @if $maxH {
    max-height: $maxH;
  }
}

@mixin font($fz, $fw: null, $fm: null) {
  font-size: $fz;

  @if $fw {
    font-weight: $fw;
  }

  @if $fm {
    font-family: $fm;
  }
}

@mixin border($borderWidth, $borderColor, $radio: 0) {
  border: $borderWidth solid;
  border-radius: $radio;

  @if $borderColor {
    border-color: $borderColor;
  }
}

@mixin flex($dir: row, $justifyContent: flex-start, $alignItem: stretch) {
  display: flex;
  flex-direction: $dir;
  align-items: $alignItem;
  justify-content: $justifyContent;
}

@mixin absolute($top: null, $right: null, $bottom: null, $left: null) {
  position: absolute;

  @if $top {
    top: $top;
  }

  @if $right {
    right: $right;
  }

  @if $bottom {
    bottom: $bottom;
  }

  @if $left {
    left: $left;
  }
}

@mixin ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin color-logo($colorA, $colorB) {
  margin-top: 14px;
  font-family: current, sans-serif;
  color: transparent;
  letter-spacing: 1px;
  background: linear-gradient(90deg, $colorA, $colorB, $colorA);
  background-size: 300%;
  transition: 1.5s;
  animation: glow 10s linear infinite;

  @keyframes glow {
    to {
      background-position: -300%;
    }
  }
}

@mixin theme-shadow($light-color: 0 0 12px 0 #7c7c7c, $dark-color: 0 0 12px 0 #000000) {
  box-shadow: $light-color;
  [class="dark"] & {
    box-shadow: $dark-color;
  }
}

@mixin theme-text($light-color: 0 0 12px 0 #7c7c7c, $dark-color: 0 0 12px 0 #000000) {
  text-shadow: $light-color;
  [class="dark"] & {
    text-shadow: $dark-color;
  }
}

@mixin theme-color($light-color: #ffffff, $dark-color: #000000) {
  color: $light-color;
  [class="dark"] & {
    color: $dark-color;
  }
}

@mixin theme-bg($light-color: #ffffff, $dark-color: #000000) {
  background: $light-color;
  [class="dark"] & {
    background: $dark-color;
  }
}

@mixin theme-border($w, $light-color: #ffffff, $dark-color: #000000, $location: "around", $radius: "0", $style: solid) {
  @if str_index($location, "top") {
    border-top: $w $style;
  }

  @if str_index($location, "right") {
    border-right: $w $style;
  }

  @if str_index($location, "bottom") {
    border-bottom: $w $style;
  }

  @if str_index($location, "left") {
    border-left: $w $style;
  }

  @if str_index($location, "around") {
    border: $w $style;
  }

  border-color: $light-color;
  [class="dark"] & {
    border-color: $dark-color;
  }

  border-radius: $radius;
}

@mixin theme-brightness($light-brightness: 100%, $dark-brightness: 80%) {
  filter: brightness($light-brightness);
  [class="dark"] & {
    filter: brightness($dark-brightness);
  }
}

@mixin theme-filter($light-filter: null, $dark-filter: null) {
  @if $light-filter {
    filter: $light-filter;
  }
  [class="dark"] & {
    @if $dark-filter {
      filter: $dark-filter;
    }
  }
}
