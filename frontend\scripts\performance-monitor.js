#!/usr/bin/env node

/**
 * Vite 性能监控脚本
 * 用于监控开发和构建性能，分析缓存效果
 */

import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync, statSync, readdirSync } from "fs";
import { execSync } from "child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, "..");

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
function formatSize(bytes) {
  const units = ["B", "KB", "MB", "GB"];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 获取目录大小
 * @param {string} dirPath 目录路径
 * @returns {number} 目录大小（字节）
 */
function getDirSize(dirPath) {
  if (!existsSync(dirPath)) return 0;

  let totalSize = 0;

  try {
    const items = readdirSync(dirPath);

    for (const item of items) {
      const itemPath = join(dirPath, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory()) {
        totalSize += getDirSize(itemPath);
      } else {
        totalSize += stats.size;
      }
    }
  } catch (error) {
    console.warn(`无法读取目录 ${dirPath}: ${error.message}`);
  }

  return totalSize;
}

/**
 * 分析缓存状态
 */
function analyzeCacheStatus() {
  console.log("📊 缓存状态分析\n");

  const cacheInfo = [
    {
      name: "Vite 预构建缓存",
      path: "node_modules/.vite",
      description: "依赖预构建缓存，影响开发启动速度"
    },
    {
      name: "TypeScript 缓存",
      path: "node_modules/.cache/typescript",
      description: "TypeScript 编译缓存"
    },
    {
      name: "ESLint 缓存",
      path: "node_modules/.cache/.eslintcache",
      description: "ESLint 检查缓存"
    },
    {
      name: "Stylelint 缓存",
      path: "node_modules/.cache/stylelint",
      description: "Stylelint 检查缓存"
    },
    {
      name: "构建输出",
      path: "dist",
      description: "生产构建输出目录"
    }
  ];

  let totalCacheSize = 0;

  for (const cache of cacheInfo) {
    const fullPath = join(projectRoot, cache.path);
    const size = getDirSize(fullPath);
    const exists = existsSync(fullPath);

    totalCacheSize += size;

    console.log(`${exists ? "✅" : "❌"} ${cache.name}`);
    console.log(`   路径: ${cache.path}`);
    console.log(`   大小: ${exists ? formatSize(size) : "不存在"}`);
    console.log(`   说明: ${cache.description}\n`);
  }

  console.log(`📦 总缓存大小: ${formatSize(totalCacheSize)}\n`);
}

/**
 * 分析依赖预构建状态
 */
function analyzePreBuildStatus() {
  console.log("🔧 依赖预构建分析\n");

  const viteMetaPath = join(projectRoot, "node_modules/.vite/_metadata.json");

  if (existsSync(viteMetaPath)) {
    try {
      const metadata = JSON.parse(require("fs").readFileSync(viteMetaPath, "utf8"));

      console.log("✅ 预构建元数据存在");
      console.log(`   Vite 版本: ${metadata.config?.viteVersion || "未知"}`);
      console.log(`   配置哈希: ${metadata.config?.configHash || "未知"}`);
      console.log(`   依赖数量: ${Object.keys(metadata.optimized || {}).length}`);

      if (metadata.optimized) {
        console.log("\n📋 已预构建的依赖:");
        Object.keys(metadata.optimized).forEach(dep => {
          console.log(`   - ${dep}`);
        });
      }
    } catch (error) {
      console.error("❌ 读取预构建元数据失败:", error.message);
    }
  } else {
    console.log("❌ 预构建元数据不存在，可能需要重新预构建");
  }

  console.log();
}

/**
 * 分析构建产物
 */
function analyzeBuildOutput() {
  console.log("🏗️  构建产物分析\n");

  const distPath = join(projectRoot, "dist");

  if (!existsSync(distPath)) {
    console.log("❌ 构建产物不存在，请先执行构建命令\n");
    return;
  }

  const distSize = getDirSize(distPath);
  console.log(`📦 构建产物总大小: ${formatSize(distSize)}`);

  // 分析不同类型的文件
  const fileTypes = {
    js: { pattern: /\.js$/, size: 0, count: 0 },
    css: { pattern: /\.css$/, size: 0, count: 0 },
    html: { pattern: /\.html$/, size: 0, count: 0 },
    images: { pattern: /\.(png|jpg|jpeg|gif|svg|webp)$/, size: 0, count: 0 },
    fonts: { pattern: /\.(woff|woff2|ttf|eot)$/, size: 0, count: 0 },
    other: { size: 0, count: 0 }
  };

  function analyzeDir(dirPath) {
    const items = readdirSync(dirPath);

    for (const item of items) {
      const itemPath = join(dirPath, item);
      const stats = statSync(itemPath);

      if (stats.isDirectory()) {
        analyzeDir(itemPath);
      } else {
        let matched = false;
        for (const [type, info] of Object.entries(fileTypes)) {
          if (type !== "other" && info.pattern.test(item)) {
            info.size += stats.size;
            info.count++;
            matched = true;
            break;
          }
        }

        if (!matched) {
          fileTypes.other.size += stats.size;
          fileTypes.other.count++;
        }
      }
    }
  }

  analyzeDir(distPath);

  console.log("\n📊 文件类型分布:");
  for (const [type, info] of Object.entries(fileTypes)) {
    if (info.count > 0) {
      const percentage = ((info.size / distSize) * 100).toFixed(1);
      console.log(`   ${type.toUpperCase()}: ${formatSize(info.size)} (${info.count} 文件, ${percentage}%)`);
    }
  }

  console.log();
}

/**
 * 性能建议
 */
function performanceRecommendations() {
  console.log("💡 性能优化建议\n");

  const recommendations = [];

  // 检查缓存大小
  const viteCacheSize = getDirSize(join(projectRoot, "node_modules/.vite"));
  if (viteCacheSize > 100 * 1024 * 1024) {
    // 100MB
    recommendations.push("Vite 缓存过大，建议清理: npm run cache:clear:dev");
  }

  // 检查构建产物
  const distSize = getDirSize(join(projectRoot, "dist"));
  if (distSize > 50 * 1024 * 1024) {
    // 50MB
    recommendations.push("构建产物较大，建议检查代码分割策略和资源优化");
  }

  // 检查 node_modules
  const nodeModulesSize = getDirSize(join(projectRoot, "node_modules"));
  if (nodeModulesSize > 1024 * 1024 * 1024) {
    // 1GB
    recommendations.push("node_modules 过大，建议清理未使用的依赖");
  }

  if (recommendations.length === 0) {
    console.log("✅ 当前性能状态良好，无特殊建议");
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }

  console.log("\n🔗 更多优化建议请参考: docs/vite-cache-optimization.md\n");
}

/**
 * 主函数
 */
function main() {
  console.log("🚀 Vite 性能监控报告\n");
  console.log(`📅 生成时间: ${new Date().toLocaleString()}`);
  console.log(`📁 项目路径: ${projectRoot}\n`);
  console.log("=".repeat(60) + "\n");

  analyzeCacheStatus();
  analyzePreBuildStatus();
  analyzeBuildOutput();
  performanceRecommendations();

  console.log("=".repeat(60));
  console.log("📋 快捷命令:");
  console.log("   npm run cache:clear     - 清理所有缓存");
  console.log("   npm run dev:force       - 强制重新预构建");
  console.log("   npm run build:analyze   - 分析构建产物");
  console.log("   npm run deps:check      - 检查依赖更新");
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes("--help") || args.includes("-h")) {
  console.log(`
Vite 性能监控工具

用法:
  node scripts/performance-monitor.js [选项]

选项:
  --help, -h     显示帮助信息
  --cache        仅显示缓存状态
  --build        仅显示构建分析
  --deps         仅显示依赖分析

示例:
  node scripts/performance-monitor.js          # 完整报告
  node scripts/performance-monitor.js --cache  # 仅缓存状态
`);
  process.exit(0);
}

// 根据参数执行特定分析
if (args.includes("--cache")) {
  analyzeCacheStatus();
} else if (args.includes("--build")) {
  analyzeBuildOutput();
} else if (args.includes("--deps")) {
  analyzePreBuildStatus();
} else {
  main();
}
