<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :pagination="false"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      highlight-current-row
      @search="getCustomGroupInfo"
      row-key="uuid"
      table-key="customGroupInfo"
    >
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { CustomGroup } from "@/api/modules/biz/debug/custominfo";
import { customInfoApi } from "@/api/modules/biz/debug/custominfo";
import { useDebugStore } from "@/stores/modules/debug";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const progressDialog = ref();
const { t } = useI18n();
const proTable = ref<ProTableInstance>();
const initParam = reactive({ type: 1 });
const tableData = ref<CustomGroup[]>([]);
const { debugIndex, currDevice } = useDebugStore();

const getCustomGroupInfo = async () => {
  try {
    progressDialog.value?.show();
    const allGroupsRes = await customInfoApi.getAllGroupsByDevice(currDevice.id);
    if (allGroupsRes.code === 0 && Array.isArray(allGroupsRes.data?.menus)) {
      // 只显示当前选中的自定义组下的报告
      const compData = debugIndex.compData.get(currDevice.id);
      const groupName = compData?.name;
      const group = allGroupsRes.data.menus.find(group => group.name === groupName);
      tableData.value = Array.isArray(group?.reports) ? group.reports : [];
    } else {
      tableData.value = [];
    }
    proTable.value?.refresh();
  } catch (error) {
    console.error("获取自定义组信息失败", error);
  } finally {
    progressDialog.value?.hide();
  }
};

onMounted(() => {
  getCustomGroupInfo();
});

const columns = reactive<ColumnProps<any>[]>([
  { type: "index", label: t("device.backup.sequence"), fixed: "left", width: 60 },
  {
    prop: "name",
    label: t("device.groupInfo.table.name"),
    width: 200
  },
  {
    prop: "desc",
    label: t("device.groupInfo.table.desc"),
    width: 200
  },
  {
    prop: "fc",
    label: t("device.groupInfo.table.fc"),
    width: 200
  },
  {
    prop: "method",
    label: "Method",
    width: 200
  },
  {
    prop: "keyword",
    label: "关键字",
    width: 200
  },
  {
    prop: "inherit",
    label: "继承",
    width: 200
  }
]);

watch(
  debugIndex.compData,
  newValue => {
    if (newValue) {
      proTable.value?.reset();
      getCustomGroupInfo();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
