<template>
  <el-tooltip
    ref="tooltipRef"
    effect="dark"
    :content="globalStore.isDeviceList ? t('layout.header.collapse.fold') : t('layout.header.collapse.expand')"
    placement="bottom"
  >
    <el-icon class="collapse-icon" @click="changeCollapse">
      <Fold v-if="globalStore.isDeviceList" />
      <Expand v-else />
    </el-icon>
  </el-tooltip>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules/global";
import { Fold, Expand } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { ref } from "vue";

const globalStore = useGlobalStore();
const { t } = useI18n();
const tooltipRef = ref();

const changeCollapse = () => {
  // 立即隐藏tooltip
  if (tooltipRef.value) {
    tooltipRef.value.hide();
  }
  globalStore.isDeviceList = !globalStore.isDeviceList;
};
</script>

<style scoped lang="scss">
.collapse-icon {
  margin-right: 20px;
  margin-left: 5px;
  font-size: 22px;
  color: var(--el-header-text-color);
  cursor: pointer;
}
</style>
