import { defineConfig, loadEnv, ConfigEnv, UserConfig } from "vite";
import { resolve } from "path";
import { wrapperEnv } from "./build/getEnv";
import { createProxy } from "./build/proxy";
import { createVitePlugins } from "./build/plugins";
import { visualizer } from "rollup-plugin-visualizer";
import pkg from "./package.json";
import dayjs from "dayjs";
import { getElementPlusIncludes, isElementPlusModule, getElementPlusChunkName } from "./build/element-plus-config";

const { dependencies, devDependencies, name } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};

// @see: https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);

  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve("node_modules/async-validator/dist-node/index.js"),
        mousetrap: "mousetrap/mousetrap.js"
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler" // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心框架依赖 - 高优先级预构建
        "vue",
        "vue-router",
        "pinia",
        "pinia-plugin-persistedstate",

        // UI组件库 - 使用配置文件管理
        ...getElementPlusIncludes(false), // false表示不包含样式路径，因为已导入完整样式

        // 工具库 - 稳定依赖预构建
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash-es",
        "@vueuse/core",
        "mitt",
        "nprogress",
        "qs",

        // 加密和工具
        "crypto-js",
        "md5",

        // 国际化
        "vue-i18n",

        // 数学计算
        "decimal.js",

        // 打印功能
        "print-js",

        // 拖拽排序
        "sortablejs",

        // 分割面板
        "split.js",

        // 字符串格式化
        "sprintf-js",

        // 键盘快捷键库
        "mousetrap"
      ],

      // 排除大型库和动态导入的依赖
      exclude: [
        // 图标库 - 按需加载
        "@iconify/json",
        "@iconify/vue",

        // 图表库 - 延迟加载
        "echarts",
        "@antv/g2plot",
        "@antv/x6",
        "@antv/x6-plugin-clipboard",
        "@antv/x6-plugin-dnd",
        "@antv/x6-plugin-export",
        "@antv/x6-plugin-history",
        "@antv/x6-plugin-keyboard",
        "@antv/x6-plugin-scroller",
        "@antv/x6-plugin-selection",
        "@antv/x6-plugin-snapline",
        "@antv/x6-plugin-transform",

        // 代码高亮 - 按需加载
        "highlight.js",
        "@highlightjs/vue-plugin",

        // Markdown处理 - 按需加载
        "markdown-it",

        // 图片裁剪 - 按需加载
        "vue-cropper",

        // 右键菜单 - 按需加载
        "v-contextmenu",

        // 实体编码 - 小型库
        "entities"
      ],

      // 开发环境强制重新预构建（生产环境设为false）
      force: process.env.NODE_ENV === "development" ? false : false,

      // 预构建入口
      entries: ["src/main.ts", "src/App.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,

      // 开发服务器缓存配置
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },

      // 预热常用文件，提升开发体验
      warmup: {
        clientFiles: ["src/main.ts", "src/App.vue", "src/layouts/index.vue", "src/routers/index.ts", "src/stores/index.ts"]
      },

      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // Element Plus 配置已在 build/plugins.ts 中处理
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      }),
      // 过滤 sourcemap 警告的插件
      {
        name: "suppress-sourcemap-warnings",
        buildStart() {
          // 在构建开始时重写 console.warn
          const originalWarn = console.warn;
          console.warn = (...args: any[]) => {
            const message = args.join(" ");
            // 过滤掉 sourcemap 相关的警告
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            // 过滤掉 entities 相关的警告
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        },
        configureServer() {
          // 在开发服务器启动时也重写
          const originalWarn = console.warn;
          console.warn = (...args: any[]) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        }
      }
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,

      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,

      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2000,

      // 构建缓存配置
      emptyOutDir: true,

      // 静态资源处理
      assetsInlineLimit: 4096, // 小于4kb的资源内联为base64

      rollupOptions: {
        // 忽略有问题的 sourcemap 警告
        onwarn(warning, warn) {
          // 忽略 sourcemap 相关警告
          if (warning.code === "SOURCEMAP_ERROR") return;
          // 忽略 markdown-it entities 的 sourcemap 警告
          if (warning.message?.includes("entities/lib/esm") && warning.message?.includes("points to missing source files")) {
            return;
          }
          warn(warning);
        },
        // 缓存优化
        cache: true,

        // 外部依赖（如果需要CDN加载）
        external: [],

        output: {
          // 优化代码分割策略 - 基于缓存友好的文件名
          chunkFileNames: chunkInfo => {
            // 为不同类型的chunk使用不同的命名策略
            if (chunkInfo.name?.includes("vendor")) {
              return "assets/vendor/[name]-[hash].js";
            }
            if (chunkInfo.name?.includes("async")) {
              return "assets/async/[name]-[hash].js";
            }
            return "assets/chunks/[name]-[hash].js";
          },
          entryFileNames: "assets/entry/[name]-[hash].js",
          assetFileNames: assetInfo => {
            // 根据文件类型分类存放
            const extType = assetInfo.name?.split(".").pop() || "";
            if (["png", "jpg", "jpeg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/images/[name]-[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/fonts/[name]-[hash].[ext]";
            }
            if (["css"].includes(extType)) {
              return "assets/styles/[name]-[hash].[ext]";
            }
            return "assets/[ext]/[name]-[hash].[ext]";
          },

          // 手动分割代码块，优化缓存策略
          manualChunks: id => {
            // 核心框架 - 变化频率低，单独打包利于长期缓存
            if (id.includes("vue") && !id.includes("node_modules")) {
              return "vue-vendor";
            }
            if (id.includes("vue-router")) {
              return "vue-vendor";
            }
            if (id.includes("pinia")) {
              return "vue-vendor";
            }

            // UI组件库 - 使用专门的Element Plus处理逻辑
            if (isElementPlusModule(id)) {
              return getElementPlusChunkName(id);
            }

            // 图标库 - 按需加载，单独打包
            if (id.includes("@iconify")) {
              return "icons-vendor";
            }

            // 工具库 - 相对稳定
            if (id.includes("axios") || id.includes("dayjs") || id.includes("lodash")) {
              return "utils-vendor";
            }
            if (id.includes("crypto-js") || id.includes("md5") || id.includes("qs")) {
              return "utils-vendor";
            }

            // 图表库 - 大型库，延迟加载
            if (id.includes("echarts") || id.includes("@antv")) {
              return "charts-vendor";
            }

            // 代码高亮 - 按需加载
            if (id.includes("highlight.js") || id.includes("@highlightjs")) {
              return "highlight-vendor";
            }

            // 国际化相关
            if (id.includes("vue-i18n")) {
              return "i18n-vendor";
            }

            // 其他第三方库
            if (id.includes("node_modules")) {
              return "vendor";
            }

            // 业务代码按模块分割
            if (id.includes("src/views/")) {
              const match = id.match(/src\/views\/([^\/]+)/);
              if (match) {
                return `views-${match[1]}`;
              }
            }

            // 组件库
            if (id.includes("src/components/")) {
              return "components";
            }

            // 工具函数
            if (id.includes("src/utils/") || id.includes("src/hooks/")) {
              return "utils";
            }
          }
        }
      }
    }
  };
});
