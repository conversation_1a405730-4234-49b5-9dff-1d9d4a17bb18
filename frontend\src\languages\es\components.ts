export default {
  checkCard: {
    default: "Predeterminado"
  },
  chooseModule: {
    title: "Seleccionar Aplicación",
    noModule: "¡No se encontró módulo!",
    setDefault: "Establecer como Predeterminado",
    cancel: "Cancelar",
    confirm: "Confirmar"
  },
  closer: {
    title: "Confirmación de Salida",
    message: "¿Está seguro de que desea salir?",
    confirm: "Confirmar",
    minimize: "Minimizar a la Bandeja",
    cancel: "Cancelar"
  },
  codeHighLight: {
    noCode: "Ninguno"
  },
  cropUpload: {
    title: "Recorte de Imagen",
    zoomIn: "Acercar",
    zoomOut: "Alejar",
    rotateLeft: "Girar a la Izquierda",
    rotateRight: "Girar a la Derecha",
    uploadImage: "Haga clic para Subir Imagen",
    uploadTip: "Por favor suba un archivo de imagen, se recomienda que no exceda 2M",
    cancel: "Cancelar",
    confirm: "Confirmar"
  },
  error: {
    forbidden: "Lo siento, no tiene permiso para acceder a esta página~🙅‍♂️🙅‍♀️",
    notFound: "Lo siento, la página que visitó no existe~🤷‍♂️🤷‍♀️",
    serverError: "Lo siento, su red se ha perdido~🤦‍♂️🤦‍♀️",
    back: "Volver a la Página Anterior"
  },
  form: {
    input: {
      placeholder: "Por favor complete {label}"
    },
    select: {
      placeholder: "Por favor seleccione {label}"
    },
    button: {
      add: "Agregar",
      edit: "Editar",
      delete: "Eliminar",
      view: "Ver"
    },
    search: {
      inputPlaceholder: "Por favor ingrese",
      selectPlaceholder: "Por favor seleccione",
      rangeSeparator: "a",
      startPlaceholder: "Hora de inicio",
      endPlaceholder: "Hora de fin"
    }
  },
  selectIcon: {
    title: "Seleccionar Icono",
    placeholder: "Por favor seleccione un icono",
    searchPlaceholder: "Buscar icono",
    noSearchResult: "No se encontró el icono~",
    moreIcons: "Más iconos",
    enterIconifyCode: "Por favor ingrese el código iconify que desea, por ejemplo mdi:home-variant",
    iconifyAddress: "Dirección Iconify",
    localIcons: "Iconos Locales"
  },
  selector: {
    add: "Agregar",
    addCurrent: "Agregar Actual",
    addSelected: "Agregar Seleccionado",
    delete: "Eliminar",
    deleteCurrent: "Eliminar Actual",
    deleteSelected: "Eliminar Seleccionado",
    cancel: "Cancelar",
    confirm: "Confirmar",
    selected: "Seleccionado",
    maxSelect: "Selección Máxima",
    singleSelectOnly: "Solo se puede seleccionar uno",
    maxSelectLimit: "Se pueden seleccionar hasta {count}",
    person: "Persona"
  },
  upload: {
    view: "Ver",
    edit: "Editar",
    delete: "Eliminar",
    uploadImage: "Por favor suba una imagen",
    uploadSuccess: "¡Imagen subida exitosamente!",
    uploadFailed: "Error al subir la imagen, por favor vuelva a subirla!",
    invalidFormat: "¡La imagen subida no cumple con el formato requerido!",
    fileSizeExceeded: "¡El tamaño de la imagen subida no puede exceder {size}M!",
    maxFilesExceeded: "Solo puede subir hasta {limit} imágenes, por favor elimine algunas antes de subir!",
    fileSizeZero: "El archivo {fileName} tiene tamaño 0, ¡no se puede subir!",
    tips: "Consejos"
  },
  treeFilter: {
    searchPlaceholder: "Ingrese palabra clave para filtrar",
    expandAll: "Expandir Todo",
    collapseAll: "Colapsar Todo",
    all: "Todo"
  },
  proTable: {
    search: {
      reset: "Restablecer",
      search: "Buscar",
      expand: "Expandir",
      collapse: "Colapsar"
    },
    pagination: {
      total: "Total {total}",
      pageSize: "por página",
      goto: "Ir a",
      page: "Página"
    },
    colSetting: {
      title: "Configuración de Columnas",
      fixedLeft: "Mostrar",
      fixedRight: "Ordenable",
      cancelFixed: "Cancelar Fijación",
      reset: "Restaurar Predeterminado",
      confirm: "Confirmar",
      cancel: "Cancelar"
    },
    table: {
      empty: "Sin datos"
    }
  },
  basicComponent: {
    title: "Componentes Básicos",
    line: "Línea",
    text: "Texto",
    rect: "Rectángulo",
    circle: "Círculo",
    ellipse: "Elipse",
    triangle: "Triángulo",
    arc: "Arco"
  }
};
