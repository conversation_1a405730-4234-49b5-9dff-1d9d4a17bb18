import { ReqPage } from "@/api/interface";
import { MenuTypeDictEnum } from "@/enums";

export namespace Spa {
  /** 单页类型 */
  export type SpaType = MenuTypeDictEnum.MENU | MenuTypeDictEnum.LINK;

  /**单页分页查询 */
  export interface Page extends ReqPage {
    menuType: SpaType;
  }

  /** 单页信息 */
  export interface SpaInfo {
    /** id */
    id: number | string;
    /** 菜单名称 */
    title: string;
    /** 组件名称 */
    name: string;
    /** 菜单描述 */
    description: string;
    /** 菜单类型 */
    menuType: string;
    /** 菜单图标 */
    icon: string;
    /** 菜单路径 */
    path: string;
    /** 状态 */
    status: string;
    /** 菜单组件 */
    component: string;
    /** 需要高亮的 path (通常用作详情页高亮父级菜单) */
    activeMenu: string;
    /** 是否首页 */
    isHome: boolean;
    /** 排序 */
    sortCode: number;
    /** 是否隐藏 */
    isHide: boolean;
    /** 是否缓存 */
    isKeepAlive: boolean;
    /** 是否全屏 */
    isAffix: boolean;
    /** 是否全屏 */
    isFull: boolean;
    /** 外链地址 */
    isLink: string;
  }
}
