<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <ScreenControl />
      <!--      <AssemblySize id="assemblySize"
/>-->
      <!--      <SearchMenu id="searchMenu" />-->
      <!--      <ThemeSetting id="themeSetting" />-->
      <!--      <Fullscreen id="fullscreen" />-->
      <!--      <ChangeModule id="changeModule" />-->
    </div>
  </div>
</template>

<script setup lang="ts">
import ScreenControl from "@/layouts/components/Header/components/ScreenControl.vue";
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 0;
  .header-icon {
    display: flex;
    align-items: center;
    margin-left: 20px;
    :deep(.el-button) {
      .el-icon {
        font-size: 20px;
      }
    }
    & > * {
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    color: var(--el-header-text-color);
  }
}
</style>
