<template>
  <el-form>
    <el-form-item :label="t('device.remoteSet.inputValue')">
      <el-input-number v-model="form.value"></el-input-number>
    </el-form-item>
  </el-form>
  <div class="setSaddr-btn-right">
    <el-button :loading="form.loading" type="primary" class="setSaddr-btn" @click="onConfirm">{{ t("hmi.device.remoteSet.write") }}</el-button>
    <el-button type="default" class="setSaddr-btn" @click="onCancel">{{ t("hmi.device.remoteSet.cancel") }}</el-button>
  </div>
</template>
<script setup lang="ts">
import { GraphRemoteControlRequest } from "@/api/interface/biz/hmi";
import { graphViewApi } from "@/api/modules/biz/hmi";
import { EquipmentConfig, SelectControlType } from "../../packages/graph";
import { CtlMode } from "@/api/interface/biz/debug/remote";
import { ResultData } from "@/api";
import Message from "@/scripts/message";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const prop = defineProps<{
  deviceId: string;
  equipmentConfig: unknown;
}>();
const emit = defineEmits<{
  (e: "end"): void;
}>();
const form = ref({
  loading: false,
  value: 0
});
const onConfirm = async () => {
  form.value.loading = true;
  try {
    await remoteSet();
  } finally {
    form.value.loading = false;
  }
};
const remoteSet = async () => {
  const equipmentDataConfig = prop.equipmentConfig as EquipmentConfig;
  const requestData: GraphRemoteControlRequest = {
    deviceId: prop.deviceId,
    data: {
      ctlObj: equipmentDataConfig.setSAddr,
      ctlModel: equipmentDataConfig.setType == SelectControlType.DIRECT ? CtlMode.DIRECT : CtlMode.SBO,
      ctlVal: form.value.value + "",
      check: "no_check",
      test: "0",
      opemTm: new Date().toISOString()
    }
  };
  // 判断是直控还是选控
  if (equipmentDataConfig.setType == SelectControlType.DIRECT || equipmentDataConfig.setType == SelectControlType.SELECT) {
    const result: ResultData = await graphViewApi.remoteSet(requestData);
    if (result.code != 0) {
      Message.info(t("hmi.device.remoteSet.setFailed") + result.msg);
      return;
    }
    Message.success(t("hmi.device.remoteSet.operateSuccess"));
    emit("end");
  } else {
    Message.warning(t("hmi.device.remoteSet.noSetType"));
  }
};
const onCancel = () => {
  emit("end");
};
</script>
<style>
.setSaddr-btn {
  padding: 5px 15px;
}
.setSaddr-btn-right {
  padding: 15px 10px 0;
  text-align: right;
}
</style>
