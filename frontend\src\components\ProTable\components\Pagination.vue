<template>
  <!-- 分页组件 -->
  <el-pagination
    :background="true"
    :current-page="pageable.pageNum"
    :page-size="pageable.pageSize"
    :pager-count="pagerCount"
    :page-sizes="[15, 30, 50, 100]"
    :total="pageable.total"
    :size="globalStore?.assemblySize ?? 'default'"
    :layout="layout"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <template #total>
      {{ t("components.proTable.pagination.total", { total: pageable.total }) }}
    </template>
    <template #sizes>
      {{ t("components.proTable.pagination.pageSize") }}
    </template>
    <template #jumper>
      {{ t("components.proTable.pagination.goto") }}
      <el-input v-model="jumperValue" class="jumper-input" @keyup.enter="handleJumper" />
      {{ t("components.proTable.pagination.page") }}
    </template>
  </el-pagination>
</template>

<script setup lang="ts" name="Pagination">
import { ref } from "vue";
import { useGlobalStore } from "@/stores/modules/global";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const globalStore = useGlobalStore();

interface Pageable {
  pageNum: number;
  pageSize: number;
  total: number;
}

interface PaginationProps {
  pageable: Pageable;
  pagerCount: number;
  handleSizeChange: (size: number) => void;
  handleCurrentChange: (currentPage: number) => void;
}

const props = defineProps<PaginationProps>();

const layout = "total, sizes, prev, pager, next, jumper";
const jumperValue = ref("");

const handleJumper = () => {
  const page = parseInt(jumperValue.value);
  if (page && page > 0) {
    props.handleCurrentChange(page);
  }
  jumperValue.value = "";
};
</script>

<style scoped lang="scss">
.jumper-input {
  width: 50px;
  margin: 0 8px;
}
</style>
