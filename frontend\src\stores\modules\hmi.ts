import { defineStore } from "pinia";
import { Configure } from "@/api/interface/biz/hmi/configureInfo";

const name = "simple-hmi"; // 定义模块名称
/**  DictState */
export interface HmiState {
  /** 组态全局信息 */
  hmiInfo: Configure.ConfigureStore;
}

/** 配置模块 */
export const useHmiStore = defineStore({
  id: name,
  state: (): HmiState => {
    return {
      hmiInfo: {
        configureList: [],
        currConfigure: undefined as unknown as Configure.ConfigureInfo
      }
    };
  },
  actions: {
    /**  设置当前组态信息 */
    async setCurrentHmi(configure: Configure.ConfigureInfo) {
      this.hmiInfo.currConfigure = configure;
    }
  }
});
