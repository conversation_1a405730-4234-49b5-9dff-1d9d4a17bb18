<template>
  <el-row>
    <el-col :span="12">
      <el-table :data="prop.data" style="height: 300px" :stripe="true">
        <el-table-column type="index" :label="t('graphDefine.equipmentList.sequence')" width="60"></el-table-column>
        <el-table-column prop="name" :label="t('graphDefine.equipmentList.name')"></el-table-column>
        <el-table-column prop="type" :label="t('graphDefine.equipmentList.type')"></el-table-column>

        <el-table-column :label="t('graphDefine.equipmentList.operation')">
          <template #default="scope">
            <el-space>
              <el-icon :size="20" class="equipment-icon" @click="onPreview(scope.row)" :title="t('graphDefine.equipmentList.preview')">
                <Picture></Picture>
              </el-icon>
              <el-icon :size="20" class="equipment-icon" @click="onCopy(scope.row)" :title="t('graphDefine.equipmentList.copy')">
                <copy-document></copy-document>
              </el-icon>
              <el-icon :size="20" class="equipment-icon" @click="onDelete(scope.row)" :title="t('graphDefine.equipmentList.delete')">
                <Delete></Delete>
              </el-icon>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
    <el-col :span="12">
      <el-text>{{ t("graphDefine.equipmentList.preview") }}</el-text>
      <el-divider></el-divider>
      <el-space>
        <template v-for="item in form.data">
          <el-image v-for="(c, index) in item.components" :src="c.img" :key="index"></el-image>
        </template>
      </el-space>
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
import { Picture, Delete, CopyDocument } from "@element-plus/icons-vue";
import { EquipmentData } from "../../../graph/Graph";
import { ref } from "vue";
import { ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const prop = defineProps<{
  data: unknown[];
}>();
const emit = defineEmits<{
  (e: "copy", data: EquipmentData): void;
  (e: "delete", data: EquipmentData): void;
}>();
const form = ref<{
  data: EquipmentData[];
}>({
  data: []
});

const onPreview = (data: EquipmentData) => {
  form.value.data = [data];
};
const onCopy = (data: EquipmentData) => {
  emit("copy", data);
};
const onDelete = (data: EquipmentData) => {
  ElMessageBox.confirm(t("graphDefine.equipmentList.confirmDelete"), {
    title: t("graphDefine.equipmentList.tip"),
    type: "info"
  })
    .then(() => {
      emit("delete", data);
    })
    .catch(() => {
      console.log(t("graphDefine.equipmentList.error"));
    });
};
</script>
<style></style>
