#!/usr/bin/env node

/**
 * 测试ESLint配置是否正常工作
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🔍 测试ESLint配置...\n');

try {
  // 切换到前端目录
  process.chdir(path.join(__dirname, '../frontend'));
  
  console.log('📁 当前工作目录:', process.cwd());
  
  // 测试ESLint是否能正常运行
  console.log('🧪 运行ESLint检查...');
  const result = execSync('npx eslint --version', { encoding: 'utf8' });
  console.log('✅ ESLint版本:', result.trim());
  
  // 检查特定文件
  const testFile = 'src/views/biz/debug/device/views/ReportAuditLog.vue';
  console.log(`🔍 检查文件: ${testFile}`);
  
  try {
    execSync(`npx eslint "${testFile}" --format=compact`, { 
      encoding: 'utf8',
      stdio: 'inherit'
    });
    console.log('✅ ESLint检查完成');
  } catch (error) {
    console.log('⚠️  发现ESLint问题，但这是正常的');
  }
  
  console.log('\n🎉 ESLint配置测试完成！');
  console.log('\n📝 使用说明:');
  console.log('1. 在VSCode中打开Vue文件');
  console.log('2. 按 Ctrl+S 保存文件');
  console.log('3. ESLint会自动修复可修复的问题');
  console.log('4. 手动运行: npm run lint:eslint');
  
} catch (error) {
  console.error('❌ ESLint配置测试失败:', error.message);
  process.exit(1);
}
