/**
 * @description 字典枚举
 * @license Apache License Version 2.0
 */
export enum SysDictEnum {
  /** 菜单类型 */
  MENU_TYPE = "MENU_TYPE",
  /** 用户性别类型 */
  GENDER = "GENDER",
  /** 系统通用状态 */
  COMMON_STATUS = "COMMON_STATUS",
  /** 系统角色分类 */
  ROLE_CATEGORY = "ROLE_CATEGORY",
  /** 系统机构分类	 */
  ORG_CATEGORY = "ORG_CATEGORY",
  /** 系统职位分类	 */
  POSITION_CATEGORY = "POSITION_CATEGORY",
  /** 用户民族类型	 */
  NATION = "NATION",
  /** 登录设备类型		 */
  AUTH_DEVICE_TYPE = "AUTH_DEVICE_TYPE",
  /** 系统字典分类	 */
  DICT_CATEGORY = "DICT_CATEGORY",
  /** 文件上传引擎	 */
  FILE_ENGINE = "FILE_ENGINE",
  /** 系统通用开关		 */
  COMMON_SWITCH = "COMMON_SWITCH",
  /** 用户证件类型		 */
  IDCARD_TYPE = "IDCARD_TYPE",
  /** 通用文化程度		 */
  CULTURE_LEVEL = "CULTURE_LEVEL",
  /** 系统消息类型			 */
  MESSAGE_CATEGORY = "MESSAGE_CATEGORY",
  /** 系统消息接受者类型			 */
  RECEIVER_TYPE = "RECEIVER_TYPE",
  /** 系统消息接受者类型			 */
  MESSAGE_WAY = "MESSAGE_WAY",
  /** 系统消息接受者类型			 */
  MESSAGE_STATUS = "MESSAGE_STATUS",
  /** 用户在线状态		 */
  ONLINE_STATUS = "ONLINE_STATUS",
  /** 是否		 */
  YES_NO = "YES_NO",
  /** 多租户选项 */
  TENANT_OPTIONS = "TENANT_OPTIONS",
  /** 验证码类型 */
  CAPTCHA_TYPE = "CAPTCHA_TYPE"
}

/** 菜单类型 */
export enum MenuTypeDictEnum {
  /** 目录 */
  CATALOG = "CATALOG",
  /** 菜单 */
  MENU = "MENU",
  /** 按钮 */
  BUTTON = "BUTTON",
  /** 子页 */
  SUBSET = "SUBSET",
  /** 外链 */
  LINK = "LINK"
}

export enum CommonStatusEnum {
  /** 正常 */
  ENABLE = "ENABLE",
  /** 禁用 */
  DISABLE = "DISABLED"
}

/** 字典类型枚举 */
export enum DictCategoryEnum {
  /** 系统 */
  FRM = "FRM",
  /** 业务 */
  BIZ = "BIZ"
}

/** 角色类型枚举 */
export enum OrgCategoryEnum {
  /** 全局 */
  GLOBAL = "GLOBAL",
  /** 机构 */
  ORG = "ORG"
}

/** 多租户选项枚举 */
export enum TenantEnum {
  /** 手动 */
  CHOSE = "CHOSE",
  /** 关闭 */
  CLOSE = "CLOSE",
  /** 根据域名 */
  DOMAIN = "DOMAIN"
}

/** 消息类型 */
export enum MessageTypeDictEnum {
  /** 通知 */
  INFORM = "INFORM",
  /** 公告 */
  NOTICE = "NOTICE",
  /** 消息 */
  MESSAGE = "MESSAGE"
}

/** 消息通知者类型 */
export enum MessageReceiverTypeDictEnum {
  /** 全部 */
  ALL = "ALL",
  /** 角色 */
  ROLE = "ROLE",
  /** 指定 */
  APPOINT = "APPOINT"
}

/** 消息发送方式 */
export enum MessageSendWayDictEnum {
  /** 立即 */
  NOW = "NOW",
  /** 延迟 */
  DELAY = "DELAY",
  /** 指定 */
  SCHEDULE = "SCHEDULE"
}

/**消息状态 */
export enum MessageStatusDictEnum {
  /** 等待 */
  READY = "READY",
  /** 延迟 */
  ALREADY = "ALREADY"
}
