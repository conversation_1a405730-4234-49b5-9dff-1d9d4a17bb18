import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0801",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(28, 0.67, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 35.67,
        y1: 2.33,
        x2: 31.67,
        y2: 2.33
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(4, 0.67, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 2.33,
        x2: 4,
        y2: 2.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 19,
        y1: 0,
        x2: 14,
        y2: 5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 19.17,
        y1: 4.83,
        x2: 13.83,
        y2: 0.5
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
