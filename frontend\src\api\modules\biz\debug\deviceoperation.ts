import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/deviceoperation/");

const deviceoperationApi = {
  // ===== 显式 deviceId 版本 =====
  manualWaveByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("manualWave", undefined, deviceId);
  },
  clearWaveByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("clearWave", undefined, deviceId);
  },
  resetDeviceByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("resetDevice", undefined, deviceId);
  },
  clearReportByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("clearReport", undefined, deviceId);
  },
  rebootDeviceByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("rebootDevice", undefined, deviceId);
  }
};

export { deviceoperationApi };
