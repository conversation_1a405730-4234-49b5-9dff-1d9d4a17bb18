package main

import (
	"embed"
	"log"
	"time"

	"github.com/wallace5303/ee-go/eboot"

	"electron-egg/demo"
	"electron-egg/router"
)

var (
	//go:embed public/**
	staticFS embed.FS
)

func main() {
	startTime := time.Now()
	log.Printf("Go服务启动开始...")

	// Initialize ee-go with optimized settings
	ego := eboot.New(staticFS)

	// 异步初始化非关键组件
	go func() {
		// demo initialization can be delayed
		demo.Index()
		log.Printf("Demo模块初始化完成，耗时: %v", time.Since(startTime))
	}()

	// User business logic - 优先初始化API路由
	router.Api()
	log.Printf("API路由初始化完成，耗时: %v", time.Since(startTime))

	// ee-go runtime
	log.Printf("Go服务启动完成，总耗时: %v", time.Since(startTime))
	ego.Run()
}
