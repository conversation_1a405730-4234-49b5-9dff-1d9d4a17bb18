/**
 * 性能监控工具
 * 用于监控报告查询和界面刷新的性能
 */

import { logger } from "ee-core/log";

export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  dataCount?: number;
  callbackCount?: number;
  phase: string;
  deviceId?: string;
  reportType?: string;
}

export class PerformanceMonitor {
  private static metrics: Map<string, PerformanceMetrics> = new Map();
  
  /**
   * 开始监控
   * @param key 监控键值
   * @param phase 阶段名称
   * @param deviceId 设备ID
   * @param reportType 报告类型
   */
  static start(key: string, phase: string, deviceId?: string, reportType?: string): void {
    const startTime = Date.now();
    this.metrics.set(key, {
      startTime,
      phase,
      deviceId,
      reportType,
      callbackCount: 0
    });
    
    logger.info(`[Performance] ${phase} 开始 - Key: ${key}, 设备ID: ${deviceId}, 报告类型: ${reportType}, 时间: ${new Date(startTime).toISOString()}`);
  }
  
  /**
   * 记录回调
   * @param key 监控键值
   * @param dataCount 数据条数
   */
  static recordCallback(key: string, dataCount: number): void {
    const metric = this.metrics.get(key);
    if (!metric) return;
    
    metric.callbackCount = (metric.callbackCount || 0) + 1;
    metric.dataCount = dataCount;
    
    const elapsed = Date.now() - metric.startTime;
    logger.info(`[Performance] ${metric.phase} 回调${metric.callbackCount} - Key: ${key}, 累计数据: ${dataCount}条, 已耗时: ${elapsed}ms`);
  }
  
  /**
   * 结束监控
   * @param key 监控键值
   * @param finalDataCount 最终数据条数
   */
  static end(key: string, finalDataCount?: number): PerformanceMetrics | null {
    const metric = this.metrics.get(key);
    if (!metric) return null;
    
    const endTime = Date.now();
    metric.endTime = endTime;
    metric.duration = endTime - metric.startTime;
    if (finalDataCount !== undefined) {
      metric.dataCount = finalDataCount;
    }
    
    // 计算性能指标
    const avgTimePerCallback = metric.callbackCount ? metric.duration / metric.callbackCount : 0;
    const avgDataPerCallback = metric.callbackCount && metric.dataCount ? metric.dataCount / metric.callbackCount : 0;
    const dataPerSecond = metric.duration ? (metric.dataCount || 0) / (metric.duration / 1000) : 0;
    
    logger.info(`[Performance] ${metric.phase} 完成 - Key: ${key}, 总耗时: ${metric.duration}ms, 总回调: ${metric.callbackCount}次, 总数据: ${metric.dataCount}条`);
    logger.info(`[Performance] ${metric.phase} 性能指标 - 平均每次回调耗时: ${avgTimePerCallback.toFixed(2)}ms, 平均每次回调数据: ${avgDataPerCallback.toFixed(2)}条, 数据处理速度: ${dataPerSecond.toFixed(2)}条/秒`);
    
    // 性能警告
    if (metric.duration > 10000) {
      logger.warn(`[Performance] ${metric.phase} 性能警告 - 查询耗时超过10秒: ${metric.duration}ms`);
    }
    if (avgTimePerCallback > 1000) {
      logger.warn(`[Performance] ${metric.phase} 性能警告 - 平均回调耗时超过1秒: ${avgTimePerCallback.toFixed(2)}ms`);
    }
    if (dataPerSecond < 10 && (metric.dataCount || 0) > 100) {
      logger.warn(`[Performance] ${metric.phase} 性能警告 - 数据处理速度过慢: ${dataPerSecond.toFixed(2)}条/秒`);
    }
    
    this.metrics.delete(key);
    return metric;
  }
  
  /**
   * 记录错误
   * @param key 监控键值
   * @param error 错误信息
   */
  static recordError(key: string, error: string): void {
    const metric = this.metrics.get(key);
    if (!metric) return;
    
    const elapsed = Date.now() - metric.startTime;
    logger.error(`[Performance] ${metric.phase} 错误 - Key: ${key}, 耗时: ${elapsed}ms, 错误: ${error}`);
    
    this.metrics.delete(key);
  }
  
  /**
   * 获取当前所有监控指标
   */
  static getAllMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics);
  }
  
  /**
   * 清理所有监控指标
   */
  static clear(): void {
    this.metrics.clear();
  }
  
  /**
   * 生成性能报告
   */
  static generateReport(): string {
    const activeMetrics = Array.from(this.metrics.values());
    if (activeMetrics.length === 0) {
      return "当前没有活跃的性能监控";
    }
    
    let report = "=== 性能监控报告 ===\n";
    activeMetrics.forEach((metric, index) => {
      const elapsed = Date.now() - metric.startTime;
      report += `${index + 1}. ${metric.phase}\n`;
      report += `   设备ID: ${metric.deviceId || 'N/A'}\n`;
      report += `   报告类型: ${metric.reportType || 'N/A'}\n`;
      report += `   已运行: ${elapsed}ms\n`;
      report += `   回调次数: ${metric.callbackCount || 0}\n`;
      report += `   数据条数: ${metric.dataCount || 0}\n`;
      report += `   开始时间: ${new Date(metric.startTime).toISOString()}\n\n`;
    });
    
    return report;
  }
}

/**
 * 前端性能监控工具
 */
export class FrontendPerformanceMonitor {
  private static metrics: Map<string, any> = new Map();
  
  static startUIUpdate(key: string, isPartial: boolean, dataCount: number): void {
    const startTime = Date.now();
    this.metrics.set(key, {
      startTime,
      isPartial,
      dataCount,
      phases: {}
    });
    
    console.log(`[UI Performance] 开始UI更新 - Key: ${key}, 部分数据: ${isPartial}, 数据量: ${dataCount}条, 时间: ${new Date(startTime).toISOString()}`);
  }
  
  static recordPhase(key: string, phase: string, duration: number): void {
    const metric = this.metrics.get(key);
    if (!metric) return;
    
    metric.phases[phase] = duration;
    console.log(`[UI Performance] ${phase}阶段完成 - Key: ${key}, 耗时: ${duration}ms`);
  }
  
  static endUIUpdate(key: string): void {
    const metric = this.metrics.get(key);
    if (!metric) return;
    
    const totalTime = Date.now() - metric.startTime;
    const phases = Object.entries(metric.phases).map(([name, time]) => `${name}: ${time}ms`).join(', ');
    
    console.log(`[UI Performance] UI更新完成 - Key: ${key}, 总耗时: ${totalTime}ms, 阶段耗时: {${phases}}`);
    
    // 性能警告
    if (totalTime > 500) {
      console.warn(`[UI Performance] UI更新性能警告 - 耗时超过500ms: ${totalTime}ms`);
    }
    
    this.metrics.delete(key);
  }
}
