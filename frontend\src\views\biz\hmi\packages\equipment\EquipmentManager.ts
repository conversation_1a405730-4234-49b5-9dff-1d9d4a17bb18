import { Graph } from "@antv/x6";
import DevEquipment from "./DevEquipment.json";
import { DevCfgMap } from "./DevLoad";
import { EquipmentData } from "../graph";
import i18n from "@/languages";
/**
 * 设备管理
 * <AUTHOR>
 * @version 1.0
 */
class EquipmentManager {
  registered: boolean;
  equipmentList: EquipmentData[];
  constructor() {
    this.equipmentList = [];
    this.registered = false;
  }
  /**
   * 加载设备图符
   */
  async loadEquipment() {
    if (this.registered) {
      return;
    }
    const t: any = i18n.global.t as any;
    console.log("loadEquipment");
    const data = DevEquipment.data;
    for (const item of data) {
      // 01的过滤
      if (item.id.endsWith("01")) {
        continue;
      }
      const dataItem: EquipmentData = {
        ...item,
        components: []
      };
      dataItem.name = t(item.name);
      await this.setCfgData(dataItem);
      this.equipmentList.push(dataItem);
    }
  }
  async setCfgData(item: EquipmentData) {
    const cfgData = DevCfgMap.get(item.id);
    if (cfgData) {
      item.components = cfgData;
    }
  }
  /**
   * 注册设备图符
   */
  registerNode() {
    if (this.registered) {
      return;
    }
    for (const item of this.equipmentList) {
      for (const c of item.components) {
        Graph.unregisterNode(c.data.value.shape);
        Graph.registerNode(c.data.value.shape, {
          ...c.data.value,
          width: 30,
          height: 30
        });
      }
    }
    this.registered = true;
  }
}
export default EquipmentManager;
