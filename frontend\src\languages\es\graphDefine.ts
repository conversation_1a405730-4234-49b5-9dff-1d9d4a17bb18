export default {
  equipmentList: {
    sequence: "<PERSON>ndice",
    name: "Nombre",
    type: "Tipo",
    operation: "Operación",
    preview: "Vista previa",
    copy: "<PERSON>pia<PERSON>",
    delete: "Eliminar",
    confirmDelete: "¿Confirmar eliminación?",
    tip: "Mensaje de sugerencia",
    error: "Error"
  },
  graphComponent: {
    deviceType: "Tipo de dispositivo",
    deviceName: "Nombre del dispositivo",
    save: "Guardar"
  },
  contextMenu: {
    group: "Grupo",
    ungroup: "Desagrupar",
    setStatus: "Establecer estado",
    copy: "Copiar",
    delete: "Eliminar",
    rename: "Renombrar"
  },
  graphCreate: {
    needTwoDevices: "Se necesita seleccionar dos gráficos de dispositivo para la operación de interruptor o cortacircuito",
    needCorrectStatus: "Por favor, establezca el atributo de estado correcto para el interruptor o cortacircuito",
    needOneDevice: "Por favor, seleccione un gráfico de dispositivo"
  },
  graphDefine: {
    waitCanvasInit: "Por favor, espere a que se complete la inicialización del lienzo",
    selectOneGraph: "Por favor, seleccione un símbolo",
    tip: "Sugerencia"
  },
  setStatus: {
    open: "Abrir",
    close: "Cerrar",
    none: "Ninguno"
  },
  graphTools: {
    undo: "Deshacer",
    redo: "Rehacer",
    front: "Frente",
    back: "Atrás",
    delete: "Eliminar",
    save: "Guardar",
    equipmentList: "Lista de Equipos"
  },
  graphEditor: {
    dataConfig: "Configuración de datos",
    loadEquipmentFailed: "Error al cargar gráfico"
  }
};
