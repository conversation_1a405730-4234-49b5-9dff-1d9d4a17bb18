export default {
  limit: {
    role: {
      title: "Role Management",
      form: {
        add: "Add Role",
        edit: "Edit Role",
        view: "View Role",
        name: "Role Name",
        code: "Role Code",
        category: "Role Category",
        org: "Organization",
        status: "Status",
        sort: "Sort",
        description: "Description",
        cancel: "Cancel",
        confirm: "Confirm",
        validation: {
          name: "Please enter role name",
          code: "Please enter role code",
          category: "Please select role category",
          org: "Please select organization",
          status: "Please select status"
        }
      },
      columns: {
        name: "Role Name",
        code: "Role Code",
        category: "Role Category",
        org: "Organization",
        status: "Status",
        sort: "Sort",
        operation: "Operation"
      },
      category: {
        system: "System Role",
        org: "Organization Role"
      },
      status: {
        enable: "Enable",
        disable: "Disable"
      },
      grantResource: {
        title: "Grant Resource",
        warning: "Please select the resource to grant",
        firstLevel: "First Level Menu",
        menu: "Menu",
        buttonAuth: "Button Permission",
        cancel: "Cancel",
        confirm: "Confirm",
        selectDataScope: "Select Data Scope",
        api: "API",
        dataScope: "Data Scope"
      },
      grantPermission: {
        title: "Grant Permission",
        warning: "Please select the permission to grant",
        api: "API",
        apiPlaceholder: "Please enter API name",
        dataScope: "Data Scope",
        cancel: "Cancel",
        confirm: "Confirm"
      },
      dataScope: {
        selectOrg: "Select Organization",
        orgList: "Organization List",
        cancel: "Cancel",
        confirm: "Confirm"
      }
    }
  }
};
