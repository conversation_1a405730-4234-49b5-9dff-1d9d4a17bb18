import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "0201",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 5,
        x2: 0,
        y2: 5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 35,
        y1: 5,
        x2: 31,
        y2: 5
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(5.5, 0, 25, 10)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "#000",
      stroke: "#000"
    }
  }
};

export default e;
