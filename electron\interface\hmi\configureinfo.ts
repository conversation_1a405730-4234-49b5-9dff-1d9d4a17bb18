export interface ConfigureInfo {
    id: string;
    pId?: string;
    path?: string;
    label?: string;
    svgIcon?: string;
    children?: ConfigureInfo[];
    type?: string;
}

export interface SaveConfigureInfo {
    id: string;
    path: string;
    label: string;
    data: string;
}

export interface LoadConfigureInfo {
    id: string;
    path: string;
    label: string;
}

export interface openConfigureInfo {
    path: string;
}