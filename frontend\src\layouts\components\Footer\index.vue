<template>
  <div class="footer flx-center">
    <span target="_blank" class="mx-1">{{ $t("layout.footer.copyright", { version: props.sysCopyright }) }}</span>
  </div>
</template>
<script setup lang="ts">
import { useConfigStore } from "@/stores/modules";
import packageJson from "./../../../../../package.json";
const configStore = useConfigStore();
interface Footer {
  /** 底部系统概述 */
  sysCopyright: string;
}

//默认值
const props = reactive<Footer>({
  sysCopyright: ""
});

onMounted(() => {
  // 获取系统配置,设置版权信息
  configStore.getSysBaseInfo().then(res => {
    // 提取版本号
    const version = packageJson.name + "-v" + packageJson.version;
    const newVersion = res.SYS_COPYRIGHT.replace("{}", `${version}`);
    props.sysCopyright = newVersion;
  });
});
</script>
<style scoped lang="scss">
@import "./index";
</style>
