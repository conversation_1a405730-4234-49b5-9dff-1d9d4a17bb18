import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "1100",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.47)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 15.86, 23, 22.47)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.33,
        y1: 4.63,
        x2: 12.57,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 17.33,
        y1: 4.33,
        x2: 11.47,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.83,
        y1: 8.75,
        x2: 11.83,
        y2: 14.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.33,
        y1: 25.3,
        x2: 12.38,
        y2: 30.17
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 17,
        y1: 25,
        x2: 11.31,
        y2: 30.17
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.67,
        y1: 29.56,
        x2: 11.67,
        y2: 35.33
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(15.67, 7.53, 23, 22.47)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 25.67,
        y1: 16,
        x2: 25,
        y2: 21.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 25,
        y1: 20.67,
        x2: 33,
        y2: 20.67
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 33.33,
        y1: 21,
        x2: 32.67,
        y2: 16.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    },
    triangle: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
