<template>
  <div class="card">
    <el-form ref="paramConfigFormRef" :model="paramInfo" label-width="auto" style="max-width: 600px" label-suffix=" :">
      <!-- <el-row :gutter="16">
        <el-col :span="8"> -->
      <el-form-item required :label="t('sys.config.paramRefreshTime')" prop="PARAM_REFRESH_TIME">
        <el-input-number
          style="width: 350px"
          minlength="4"
          maxlength="6"
          v-model="paramInfo.PARAM_REFRESH_TIME"
          :step="1000"
          :readonly="false"
        ></el-input-number>
      </el-form-item>
      <!-- </el-col>
        <el-col :span="8"> -->
      <el-form-item required :label="t('sys.config.reportRefreshTime')" prop="REPORT_REFRESH_TIME">
        <el-input-number
          style="width: 350px"
          minlength="4"
          maxlength="6"
          v-model="paramInfo.REPORT_REFRESH_TIME"
          :step="1000"
          :readonly="false"
        ></el-input-number>
      </el-form-item>
      <!-- </el-col>
      </el-row> -->
      <!-- <el-row :gutter="16">
        <el-col :span="16"> -->
      <el-form-item required :label="t('sys.config.stateRefreshTime')" prop="STATE_REFRESH_TIME">
        <el-input-number
          style="width: 350px"
          minlength="4"
          maxlength="6"
          v-model="paramInfo.STATE_REFRESH_TIME"
          :step="1000"
          :readonly="false"
        ></el-input-number>
      </el-form-item>
      <el-form-item required :label="t('sys.config.variRefreshTime')" prop="VARI_REFRESH_TIME">
        <el-input-number
          style="width: 350px"
          minlength="4"
          maxlength="6"
          v-model="paramInfo.VARI_REFRESH_TIME"
          :step="1000"
          :readonly="false"
        ></el-input-number>
      </el-form-item>
      <!-- </el-col>
      </el-row> -->
      <!-- <el-row :gutter="16">
        <el-col :span="24"> -->
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="onSubmit()">{{ t("sys.config.save") }}</el-button>
        <el-button style="margin-left: 10px" @click="resetForm">{{ t("sys.config.reset") }}</el-button>
      </el-form-item>
      <!-- </el-col>
      </el-row> -->
    </el-form>
  </div>
</template>

<script setup lang="ts">
import Message from "@/scripts/message";
import { useConfigStore } from "@/stores/modules/config";
import { FormInstance } from "element-plus";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const configStore = useConfigStore();
const { paramInfo } = storeToRefs(configStore);
const submitLoading = ref(false); //提交按钮loading
const paramConfigFormRef = ref<FormInstance>(); //表单实例
/** 提交表单 */
function onSubmit() {
  Message.success(t("sys.config.saveSuccess"));
}
/** 重置表单 */
function resetForm() {
  configStore.resetParamValues();
  paramConfigFormRef.value?.resetFields();
}
</script>

<style lang="scss" scoped></style>
