import { ReqPage } from "@/api";

export namespace Button {
  /**按钮分页查询 */
  export interface Page extends ReqPage {
    /** 菜单Id */
    parentId: number | string;
  }

  /** 按钮信息 */
  export interface ButtonInfo {
    /** id */
    id: number | string;
    /** 上级菜单 */
    parentId: number | string;
    /** 按钮名称 */
    title: string;
    /** 按钮码 */
    code: string;
    /** 按钮排序*/
    sortCode: number;
    /** 按钮描述 */
    description: string;
  }

  /** 按钮信息 */
  export interface Batch {
    /** 上级菜单 */
    parentId: number | string;
    /** 按钮名称 */
    title: string;
    /** 按钮码 */
    code: string;
  }
}
