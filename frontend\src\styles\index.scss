/* 全局下拉菜单和弹窗样式优化 */
.el-popper {
  &.language-dropdown,
  &.theme-dropdown {
    z-index: 3002 !important;
  }
}
.quick-theme-popover {
  z-index: 3003 !important;
}

/* 确保下拉菜单在分栏布局中正确显示 */
.layout .aside-split .other {
  .el-dropdown {
    .el-popper {
      z-index: 3002 !important;
    }
  }
}

/* 优化弹窗位置计算 */
.el-popover {
  &.quick-theme-popover {
    margin: 0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgb(0 0 0 / 15%) !important;
  }
}

/* 确保下拉菜单项样式正确 */
.el-dropdown-menu {
  .el-dropdown-menu__item {
    &:hover {
      background-color: var(--bl-hover-bg-color) !important;
    }
    &.is-active {
      color: var(--el-color-primary) !important;
      background-color: var(--el-color-primary-light-9) !important;
    }
  }
}
