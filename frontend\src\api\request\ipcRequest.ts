import { ipc } from "./ipcRenderer";
import { IECRpcReq, ResultData } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";

export function createIpcRequest(prefix: string) {
  const urlPrefix = prefix;
  /**
   * 通用ipc请求
   * @param url - 请求路径
   * @param params - 参数
   */
  async function invoke<T>(url: string, params: any = {}): Promise<ResultData<T>> {
    return ipc.invoke(urlPrefix + url, params);
  }

  /**
   * 装置ipc请求
   * @param url - 请求路径
   * @param params - 参数
   */
  async function iecInvoke<T>(url: string, params: any = {}): Promise<ResultData<T>> {
    const store = useDebugStore();
    const iecParams: IECRpcReq<T> = { head: { id: store.currDevice.id }, data: params };
    return ipc.invoke(urlPrefix + url, iecParams);
  }

  // 显式指定 deviceId 的装置 ipc 请求（用于多装置并行时解耦全局 currDevice）
  async function iecInvokeWithDevice<T>(url: string, params: any = {}, deviceId: string): Promise<ResultData<T>> {
    const iecParams: IECRpcReq<T> = { head: { id: deviceId }, data: params };
    return ipc.invoke(urlPrefix + url, iecParams);
  }

  return {
    invoke,
    iecInvoke,
    iecInvokeWithDevice
  };
}
