<template>
  <svg
    t="1742950501683"
    class="icon"
    viewBox="0 0 1137 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="9780"
    :width="props.width"
    :height="props.height"
  >
    <path
      :fill="props.color"
      d="M489.244444 568.888889l60.681482 75.851852H265.481481l64.474075-265.481482 60.681481 72.05926c34.133333-30.340741 109.985185-68.266667 238.933333-68.266667 201.007407 0 280.651852 204.8 280.651852 204.8S792.651852 455.111111 663.703704 455.111111c-98.607407 0-155.496296 75.851852-174.45926 113.777778z"
      p-id="9781"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";

const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
