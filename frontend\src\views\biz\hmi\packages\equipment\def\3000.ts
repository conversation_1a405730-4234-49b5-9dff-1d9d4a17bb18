import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "3000",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(15.09, 7.87, 23, 22.47)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 31,
        y1: 21.74,
        x2: 26.52,
        y2: 17.5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 23.09,
        y1: 22,
        x2: 27.31,
        y2: 17.5
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 27.05,
        y1: 18.03,
        x2: 27.05,
        y2: 13
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 13.67,
        y1: 12.41,
        x2: 8.81,
        y2: 8.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5.09,
        y1: 12.67,
        x2: 9.66,
        y2: 8.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 9.38,
        y1: 8.83,
        x2: 9.38,
        y2: 4
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.47)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0.33, 17.19, 23, 22.47)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8.25,
        y1: 26.1,
        x2: 8,
        y2: 35
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 7.92,
        y1: 34.86,
        x2: 14.29,
        y2: 31.05
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 8.08,
        y1: 26.23,
        x2: 14,
        y2: 31
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
