import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/variable/");

/**
 * 变量模块：改为按装置透传调用，统一支持多装置隔离。
 */
const variableApi = {
  /** 获取变量（指定 deviceId） */
  getVariableByDevice(deviceId: string, params: any) {
    return ipc.iecInvokeWithDevice<{}>("getVariable", params, deviceId);
  },
  // 添加变量（指定 deviceId）
  addVariableByDevice(deviceId: string, param: { name: string }) {
    return ipc.iecInvokeWithDevice<{}>("addVariable", param, deviceId);
  },
  // 修改变量（指定 deviceId）
  modifyVariableByDevice(deviceId: string, param: any[]) {
    return ipc.iecInvokeWithDevice<{}>("modifyVariable", param, deviceId);
  },
  // 删除变量（指定 deviceId）
  deleteVariableByDevice(deviceId: string, param: any[]) {
    return ipc.iecInvokeWithDevice<{}>("deleteVariable", param, deviceId);
  },
  // 导入变量（指定 deviceId）
  importVariableByDevice(deviceId: string, param: { path: string }) {
    return ipc.iecInvokeWithDevice<{}>("importVariable", param, deviceId);
  },
  // 导出变量（指定 deviceId）
  exportVariableByDevice(deviceId: string, params: { path: string }) {
    return ipc.iecInvokeWithDevice<{}>("exportVariable", params, deviceId);
  }
};

export { variableApi };
