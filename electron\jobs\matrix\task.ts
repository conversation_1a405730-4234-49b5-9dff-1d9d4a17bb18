import { logger } from "ee-core/log";
import { isChildJob, exit } from "ee-core/ps";
import { childMessage } from "ee-core/message";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { FileWriteError, FileWriteInfo, FileWriteRequestData, FileWriteStatus } from "iec-upadrpc/dist/src/data";
import { formatByStep, isValid, removeLastCarriage } from "../../utils/common";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { IpUtils } from "../../utils/ipUtils";
import { matrixConnectService } from "../../service/matrix/matrixconnect";
import { DeviceProgress, DeviceResult, MatrixTaskItem } from "../../interface/matrix/matrix";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import { t } from "../../data/i18n/i18n";

// 进度计算参数
const STEP_WEIGHTS = {
  connect: 10,
  download: 60,
  import: 30,
};

/**
 * example - TimerJob
 * @class
 */
class TaskJob {
  params: any;

  constructor(params: any) {
    this.params = params;
  }

  /**
   * handle() method is necessary and will be automatically called
   */
  async handle(): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.taskJobParams")}: `,
      this.params
    );
    const { jobId, deviceList } = this.params;
    // Execute the task
    const devices = JSON.parse(deviceList);
    if (Array.isArray(devices)) {
      devices.forEach(async (device) => {
        await this.doTask(jobId, device);
      });
    }
    // 结束
    // await this.updateResult("finish", {
    //   deviceId: "",
    //   totalProgress: 100,
    //   status: "success",
    //   message: "",
    // });

    if (isChildJob()) {
      exit();
    }
  }

  /**
   * Pause the job
   */
  async pause(jobId: string): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.pauseTaskJob")}: `,
      jobId
    );
  }

  /**
   * Resume the job
   */
  async resume(jobId: string, pid: number): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.resumeTaskJob")}: `,
      jobId,
      ", pid: ",
      pid
    );
    // this.doTask(jobId);
  }

  /**
   * Run the task
   */
  async doTask(jobId, data: MatrixTaskItem) {
    logger.info(
      `===============${t("matrixJob.logs.doTaskStart")}:`,
      data.device.ip
    );
    let totalProgress = 0;

    totalProgress += STEP_WEIGHTS.connect;
    // 步骤1: 连接设备 (5%)
    await this.updateProgress(jobId, {
      deviceId: data.device.id,
      currentStep: "connect",
      totalProgress: totalProgress,
      status: "success",
      message: t("matrixJob.messages.connectDevice"),
    });
    const connection = await matrixConnectService.connectDeviceByRpc(
      JSON.stringify(data.device)
    );
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      data.device.id
    );
    const client = device?.deviceClient;
    logger.info("connection", connection);
    if (connection.isSuccess()) {
      try {
        logger.info("=================", connection);
        if (data.device.downFile && data.downlist.length > 0) {
          await this.updateProgress(jobId, {
            deviceId: data.device.id,
            currentStep: "download",
            totalProgress: totalProgress,
            status: "success",
            message: t("matrixJob.messages.executeFileDownload"),
          });
          logger.info("=================downFile before");
          // 步骤2: 下载文件 (80%)
          const param: FileWriteRequestData = {
            fileItems: data.downlist,
            remoteParentPath: "/shr",
            verifyType: "None",
            cb: async (result: FileWriteInfo) => {
              // const data = { fileItem: result.currentfileItem, errorMsg: msg, status: result.status, progress: result.progress, taskid: uuid };
              totalProgress =
                STEP_WEIGHTS.connect +
                (STEP_WEIGHTS.download * result.progress.totalPercentage) / 100;
              await this.updateProgress(jobId, {
                deviceId: data.device.id,
                currentStep: "download",
                totalProgress: Number(totalProgress.toFixed(2)),
                status: "success",
                message:
                  t("matrixJob.messages.downloadingFile") +
                  "：" +
                  result?.currentfileItem?.filePath,
              });
            },
          };
          const result = await client?.writeFile(param);
          logger.info("=================downFile", result);
          logger.info("result", result);
          if (result?.status != FileWriteStatus.ALL_FILE_FINISH) {
            let msg = await this.getDownloadErrMsg(result, device);
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "error",
              message:
                t("matrixJob.messages.downloadFileFailed") +
                "：" +
                String(msg) +
                "，" +
                result?.currentfileItem?.filePath,
            });
            return;
          } else {
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "success",
              message: t("matrixJob.messages.fileDownloadCompleted"),
            });
          }
        } else {
          totalProgress += STEP_WEIGHTS.download;
          await this.updateResult(jobId, {
            deviceId: data.device.id,
            totalProgress: Number(totalProgress.toFixed(2)),
            status: "success",
            message: t("matrixJob.messages.fileDownloadCompleted"),
          });
        }
        logger.info("=================importParam before");
        // 步骤3: 导入定值 (5%)
        if (data.device.importParam && data.paramList.length > 0) {
          await this.updateProgress(jobId, {
            deviceId: data.device.id,
            currentStep: "import",
            totalProgress: totalProgress,
            status: "success",
            message: t("matrixJob.messages.executeParamImport"),
          });
          const objMap = device?.debugItemObjMap;

          let checkMsg = "";
          const reqestData = data.paramList;
          for (let element of reqestData) {
            logger.info(element.name, element.v);
            const item = objMap?.get(element.name);
            if (item) {
              if (
                !isValid(
                  String(element.v),
                  item?.type || "",
                  await this.getRealValue(
                    item?.type || "",
                    item?.s_min,
                    item?.step
                  ),
                  await this.getRealValue(
                    item?.type || "",
                    item?.s_max,
                    item?.step
                  ),
                  Number(item?.step)
                )
              ) {
                checkMsg +=
                  element.name +
                  ", " +
                  t("matrixJob.errors.description") +
                  item?.desc +
                  t("matrixJob.errors.errorReason") +
                  (await this.getRealValue(
                    String(item?.type),
                    element.v,
                    item?.step
                  )) +
                  t("matrixJob.errors.invalidValue") +
                  "\n";
              }
              element.v = String(
                await this.parseAddrToNum(item?.type || "", String(element.v))
              );
              element.grp = item?.grp || "";
              element.inf = item?.inf || "";
            } else {
              checkMsg +=
                element.name +
                ", " +
                t("matrixJob.errors.paramNotFound") +
                "\n";
            }

            // logger.info("element:", element);
          }
          // logger.info(reqestData);
          if (checkMsg.length > 0) {
            logger.info(checkMsg);
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "error",
              message:
                t("matrixJob.messages.paramValidationFailed") +
                "，" +
                removeLastCarriage(checkMsg),
            });
            return;
          }
          await client?.setRequestTimeout(data.device?.paramTimeout || 30000);
          logger.info("setEditSgValue:", data.paramList);
          const response = await client?.setEditSgValue(
            reqestData,
            true,
            IECCONSTANTS.BATCH_COUNT
          );
          // logger.info("response:", response);
          logger.info("=================importParam response", response);
          if (response?.isSuccess()) {
            let msg = "";
            if (response.data.error == "itemError") {
              msg += t("matrixJob.errors.paramItemModifyError");
              const errdata = response.data.data;
              if (errdata && Array.isArray(errdata)) {
                errdata.forEach((element, index) => {
                  msg +=
                    data.paramList[index].name +
                    t("matrixJob.errors.errorReason") +
                    device?.getServiceErrMsgByCode(String(element)) +
                    "；\n";
                });
              }
            } else if (response.data.error == "confirmError") {
              msg += t("matrixJob.errors.paramConfirmError");
              msg +=
                device?.getServiceErrMsgByCode(String(response.data.data)) +
                "\n";
            }
            if (msg.length > 0) {
              totalProgress += STEP_WEIGHTS.import;
              await this.updateResult(jobId, {
                deviceId: data.device.id,
                totalProgress: Number(totalProgress.toFixed(2)),
                status: "error",
                message:
                  t("matrixJob.messages.paramImportFailed") +
                  "：" +
                  String(msg),
              });
              return;
            }
          }
        } else {
          totalProgress += STEP_WEIGHTS.import;
          await this.updateResult(jobId, {
            deviceId: data.device.id,
            totalProgress: Number(totalProgress.toFixed(2)),
            status: "success",
            message: t("matrixJob.messages.paramImportCompleted"),
          });
        }

        if (data.isReboot) {
          const response = await client?.deviceReboot();
          if (response?.isSuccess()) {
            logger.info(
              data.device.ip + t("matrixJob.messages.deviceRebootSuccess")
            );
          }
        }
        totalProgress = 100;
        await this.updateResult(jobId, {
          deviceId: data.device.id,
          totalProgress: Number(totalProgress.toFixed(2)),
          status: "success",
          message: t("matrixJob.messages.taskCompleted"),
        });

        logger.info(`=================${t("matrixJob.logs.allFinished")}`);
      } finally {
        if (client) {
          // 断开连接
          matrixConnectService.disconnectDevice(data.device.id);
        }
      }
      // postMessage({ type: "result", success: true });
    } else {
      await this.updateResult(jobId, {
        deviceId: data.device.id,
        totalProgress: totalProgress,
        status: "error",
        message:
          t("matrixJob.messages.deviceConnectionFailed") +
          "，" +
          connection.msg,
      });
      // postMessage({ type: "result", success: false });
    }
  }

  // 数字转换为IP
  async getRealValue(type: string, value: any, step: any) {
    const result = type == "net" ? IpUtils.numberToIp(value) : value;
    return formatByStep(result, step);
  }

  // IP地址转换为数字
  async parseAddrToNum(type: string, value: string) {
    return type == "net" ? IpUtils.ipToNumber(value) : value;
  }

  async getDownloadErrMsg(
    result: FileWriteInfo | undefined,
    device: SingleGlobalDeviceInfo | undefined
  ) {
    let msg = "";
    if (result?.status == FileWriteStatus.ERROR) {
      if (result.errorCode === FileWriteError.SERVICE_ERROR) {
        const errdata = result?.errorData;
        logger.info(result);
        if (Number.isInteger(errdata)) {
          msg = device?.getServiceErrMsgByCode(String(errdata)) || "";
        }
      } else {
        const errdata = result?.errorData;
        if (errdata instanceof Error) {
          msg = errdata.message;
        }
      }
    }
    return msg;
  }
  // 更新进度工具函数
  async updateProgress(jobId, message: DeviceProgress) {
    const eventName = "job-progress-" + jobId;
    childMessage.send(eventName, {
      type: "progress",
      data: message,
    });
  }

  async updateResult(jobId, message: DeviceResult) {
    const eventName = "job-result-" + jobId;
    childMessage.send(eventName, {
      type: "result",
      data: message,
    });
  }
}
TaskJob.toString = () => "[class TaskJob]";

export default TaskJob;
