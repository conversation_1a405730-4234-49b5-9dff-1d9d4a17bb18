import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/deviceinfo/");

const deviceinfoApi = {
  /** 获取装置信息（指定 deviceId） */
  getDeviceInfoByDevice(deviceId: string, param: any[]) {
    return ipc.iecInvokeWithDevice<{}>("getDeviceInfo", param, deviceId);
  },
  /** 导出装置信息（指定 deviceId） */
  exportDeviceInfoByDevice(deviceId: string, param: { selectPath?: string; data?: any[] }) {
    return ipc.iecInvokeWithDevice<{}>("exportDeviceInfo", param, deviceId);
  }
};

export { deviceinfoApi };
