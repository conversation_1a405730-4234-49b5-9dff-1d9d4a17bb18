import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0E00",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.5)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 15.86, 23, 22.5)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.33,
        y1: 4.63,
        x2: 12.57,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 17.33,
        y1: 4.33,
        x2: 11.47,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.83,
        y1: 8.75,
        x2: 11.83,
        y2: 14.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.33,
        y1: 25.3,
        x2: 12.38,
        y2: 30.17
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 17,
        y1: 25,
        x2: 11.31,
        y2: 30.17
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.67,
        y1: 29.56,
        x2: 11.67,
        y2: 35.33
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
