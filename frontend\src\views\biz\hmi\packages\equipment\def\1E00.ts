import { calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "1E00",
  markup: [
    {
      tagName: "polygon",
      groupSelector: "triangle",
      attrs: {
        points: calculateTriangle(38.33, 0, 6, 6)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 41.67,
        y1: 0,
        x2: 41.67,
        y2: 40.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    triangle: {
      stroke: "#000",
      transform: "rotate(360)"
    }
  }
};

export default e;
