/* flex */
.flx-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-align-center {
  display: flex;
  align-items: center;
}
.flx-space-evenly {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.flx-space-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

/* clearfix */
.clearfix::after {
  display: block;
  height: 0;
  overflow: hidden;
  clear: both;
  content: "";
}

/* 文字单行省略号 */
.sle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}
.fade-transform-enter-from {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transition: all 0.2s;
  transform: translateX(30px);
}

/* breadcrumb-transform */
.breadcrumb-enter-active {
  transition: all 0.2s;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(10px);
}

/* scroll bar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-darker);
  border-radius: 20px;
}

/* nprogress */
#nprogress .bar {
  background: var(--el-color-primary) !important;
}
#nprogress .spinner-icon {
  border-top-color: var(--el-color-primary) !important;
  border-left-color: var(--el-color-primary) !important;
}
#nprogress .peg {
  box-shadow:
    0 0 10px var(--el-color-primary),
    0 0 5px var(--el-color-primary) !important;
}

/* 外边距、内边距全局样式 */
@for $i from 0 through 100 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}

/* 右键菜单全局样式 */
.v-contextmenu {
  z-index: 1000;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

  // 深色主题适配
  html.dark &,
  [class="dark"] & {
    background-color: #232324;
    border-color: #363636;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 60%);
  }
}
.v-contextmenu-item {
  display: flex;
  align-items: center;
  color: var(--el-text-color);
  cursor: pointer;
  &:hover {
    background-color: var(--el-color-primary-light-5);
  }
  .menu-svg {
    margin-right: 10px;
    font-size: 16px;
  }

  // 深色主题适配
  html.dark &,
  [class="dark"] & {
    color: #e5eaf3;
    &:hover {
      background-color: #303133;
    }
  }
}
.el-table__header-wrapper {
  --el-table-header-bg-color: var(--el-color-primary-light-9);
  .el-table__header {
    thead {
      color: var(--el-text-color-primary);
      tr {
        th {
          padding: 5px 0;
        }
      }
    }
  }
}
.el-table__body-wrapper {
  .el-table__body {
    tbody {
      tr {
        td {
          padding: 4px 0;
        }
      }
    }
  }
}
html,
body,
#app {
  font-family: "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
  font-size: 13px;
  font-weight: 480;
  color: var(--el-text-color-regular);
  letter-spacing: 0.02em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
