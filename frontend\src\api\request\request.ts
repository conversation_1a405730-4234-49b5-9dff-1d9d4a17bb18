import type { AxiosRequestConfig } from "axios";
import RequestHttp from "./instance";

export function createRequest(axiosConfig: AxiosRequestConfig) {
  const customInstance = new RequestHttp(axiosConfig);

  /**
   * get请求
   * @param url - 请求地址
   * @param config - axios配置
   */
  async function get<T>(url: string, params?: object, _object = {}) {
    return customInstance.get<T>(url, params, _object);
  }

  /**
   * post请求
   * @param url - 请求地址
   * @param data - 请求的body的data
   * @param config - axios配置
   */
  async function post<T>(url: string, params?: any, _object = {}) {
    return customInstance.post<T>(url, params, _object);
  }
  /**
   * put请求
   * @param url - 请求地址
   * @param data - 请求的body的data
   * @param config - axios配置
   */
  async function put<T>(url: string, params?: any, _object = {}) {
    return customInstance.put<T>(url, params, _object);
  }

  /**
   * delete请求
   * @param url - 请求地址
   * @param config - axios配置
   */
  async function handleDelete<T>(url: string, params?: any, _object = {}) {
    return customInstance.delete<T>(url, params, _object);
  }

  /**
   *
   * 下载
   * @param url - 请求地址
   * @param config - axios配置
   */
  async function download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return customInstance.download(url, params, { ..._object, responseType: "blob" });
  }

  return {
    get,
    post,
    put,
    delete: handleDelete,
    download
  };
}
