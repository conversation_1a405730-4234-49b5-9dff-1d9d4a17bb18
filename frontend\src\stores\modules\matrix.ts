import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { MatrixStatus } from "@/api/interface/biz/matrix";
const name = "simple-matrix"; // 定义模块名称

export const useMatrixStore = defineStore({
  id: name,
  state: (): MatrixStatus => {
    return {
      devicelist: [],
      selectDeviceIds: [],
      downlist: [],
      selectDownIds: [],
      paramList: [],
      selectParamIds: []
    };
  },
  actions: {
    async setDevicelist(newData) {
      this.devicelist = newData;
    },
    async setSelectDeviceIds(newData) {
      this.selectDeviceIds = newData;
    },
    async setDownlist(newData) {
      this.downlist = newData;
    },
    async setSelectDownIds(newData) {
      this.selecDownIds = newData;
    },
    async setParanlist(newData) {
      this.paramList = newData;
    },
    async setSelectParanIds(newData) {
      this.selecParamIds = newData;
    }
  },
  persist: piniaPersistConfig(name)
});
