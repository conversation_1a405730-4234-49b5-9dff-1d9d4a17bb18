export default {
  temperature: {
    title: "Convertidor de temperatura",
    description: "Convertir valores de temperatura entre Celsius, Fahrenheit y Kelvin",
    inputLabel: "Valor de entrada",
    inputPlaceholder: "Por favor ingrese el valor de temperatura a convertir",
    celsius: "Celsius",
    fahrenheit: "Fahrenheit",
    kelvin: "Kelvin",
    result: "Resultado",
    kelvinUnit: "K",
    celsiusUnit: "°C",
    fahrenheitUnit: "°F",
    rankine: "Rankine",
    rankineUnit: "°R",
    delisle: "Delisle",
    delisleUnit: "°De",
    newton: "Newton",
    newtonUnit: "°N",
    reaumur: "Réaumur",
    reaumurUnit: "°Ré",
    romer: "Rømer",
    romerUnit: "°Rø"
  }
};
