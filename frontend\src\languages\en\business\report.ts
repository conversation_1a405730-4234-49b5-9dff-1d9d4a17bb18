export default {
  common: {
    date: "Date",
    search: "Search",
    save: "Save",
    clear: "Clear",
    loading: "Loading...",
    reportNo: "Report Index",
    time: "Time",
    description: "Description",
    progress: "Progress",
    selectDateRange: "Please select date range",
    noData: "No data",
    saveSuccess: "Save successful",
    saveFailed: "Save failed"
  },
  date: "Date",
  search: "Search",
  filter: "Filter",
  save: "Save",
  clearList: "Clear List",
  loading: "Loading...",
  reportNumber: "Report Index",
  time: "Time",
  description: "Description",
  progress: "Progress",
  loadingText: "Loading...",
  querying: "Querying",
  selectCompleteTimeRange: "Please select complete time range",
  noDataToSave: "No data to save",
  saveSuccess: "Save successful",
  saveReport: "Save Report",
  fileUploading: "File uploading",
  fileUploadComplete: "File upload complete",
  autoRefresh: "Auto Refresh",
  showHiddenItems: "Show Hidden Items",
  name: "Name",
  operationAddress: "Operation Address",
  operationParams: "Operation Parameters",
  value: "Value",
  step: "Step",
  source: "Source",
  sourceType: "Source Type",
  result: "Result",
  searchType: "Search Type",
  total: "Total {num} items",
  sameSearch: "Same content search",
  sameFilter: "Same content filter",
  showHideTime: "Show/Hide Time Column",
  selectRowToOperate: "Please select the row to operate first",
  trip: {
    autoRefresh: "Auto Refresh"
  },
  operate: {
    name: "Name",
    operateAddress: "Operation Address",
    operateParam: "Operation Parameter",
    value: "Value",
    step: "Step",
    source: "Source",
    sourceType: "Source Type",
    result: "Result"
  },
  group: {
    uploadWave: "Upload Wave",
    searchHistory: "Search History",
    saveResult: "Save Result",
    clearContent: "Clear Content",
    contextMenu: {
      uploadWave: "Upload Wave",
      getHistoryReport: "Get History Report",
      saveResult: "Save Result",
      clearContent: "Clear Content"
    },
    date: "Date",
    search: "Search",
    save: "Save",
    clearList: "Clear List",
    loading: "Loading...",
    table: {
      reportId: "Report ID",
      time: "Time",
      description: "Description"
    },
    progress: {
      title: "Progress",
      searching: "Searching {type}",
      loading: "Loading..."
    },
    refresh: {
      start: "Start Refresh",
      stop: "Stop Refresh"
    },
    hiddenItems: {
      show: "Show Hidden Items"
    },
    messages: {
      noFileToUpload: "No file to upload",
      selectDateRange: "Please select date range",
      noDataToSave: "No data to save",
      saveReport: "Save Report",
      saveSuccess: "Save successful"
    }
  },
  entryID: "Index",
  module: "Module Name",
  msg: "Content",
  level: "Level",
  type: "Type",
  origin: "Origin",
  user: "User Name",
  exporting: "Exporting...",
  stopRefresh: "Stop Refresh",
  searchProgress: "Searching {type}",
  pleaseSelectSavePath: "Please select save path...",
  exportLogSuccess: "Export succeeded: {path}",
  exportLogFailed: "Export failed: {msg}",
  exportLogCancelled: "User cancelled export operation",
  items: "items"
};
