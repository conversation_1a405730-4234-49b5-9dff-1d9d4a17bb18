import { ResultData } from "@/api/interface";
import { SummaryInfo, ParamSummaryInfo } from "@/api/interface/biz/debug/devicesummary";
import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/devicesummary/");

const deviceSummaryApi = {
  /** 获取装置汇总信息（按 deviceId） */
  getSummaryInfoByDevice(deviceId: string): Promise<ResultData<SummaryInfo>> {
    return ipc.iecInvokeWithDevice<SummaryInfo>("getSummaryInfo", undefined, deviceId);
  },
  /** getParamSummary（按 deviceId） */
  getParamSummaryByDevice(deviceId: string): Promise<ResultData<ParamSummaryInfo[]>> {
    return ipc.iecInvokeWithDevice<ParamSummaryInfo[]>("getParamSummary", undefined, deviceId);
  },
  /** getSGCount（按 deviceId） */
  getSGCountByDevice(deviceId: string) {
    return ipc.iecInvokeWithDevice<{}>("getSGCount", undefined, deviceId);
  }
};

export { deviceSummaryApi };
