import { Cell, Graph, View, Node, Dom } from "@antv/x6";
import GrapgGroup from "./GraphGroup";
import GraphKeyboard from "./GraphKeyboard";
import { ContextMenu, ContextMenuItem, EventParams, EventType, EventTypeParams, GraphConstants, TriggerEventType } from "./Graph";
import { increaseInvalidTriggerEvent, initGraphSize, isGroup, renderGroupCells, resizeGrap, validTriggerEvent } from "./GraphUtil";
import { DataPlugin } from "./graphplugin/dataplugin";
import { CommonPlugin } from "./graphplugin";
/**
 * <AUTHOR>
 * @version 1.0 2025-03-10
 */
class GraphEvent {
  graph: Graph;
  contextMenuContainer!: Element;
  contextMenuParentContainer!: Element;
  graphGroup!: GrapgGroup;
  emit?: (args: EventTypeParams) => void;
  contextmenu: ContextMenu;
  constructor(graph: Graph, contextmenu: ContextMenu, emit?: (args: EventTypeParams) => void) {
    this.graph = graph;
    this.contextmenu = contextmenu;
    if (emit) {
      this.emit = emit;
    }
    if (graph.getPlugin("data")) {
      this.graphGroup = (graph.getPlugin("data") as DataPlugin).getData().group;
    }
    new GraphKeyboard(this.graph);
    this.bindEvent();
  }
  bindEvent() {
    this.graph.on("resize", () => {
      const plugin = this.graph.getPlugin(GraphConstants.COMMON_PLUGIN_NAME);
      if (plugin) {
        const dataPlugin = plugin as CommonPlugin;
        let refCount = dataPlugin.getData().resizeRefCount;
        if (refCount && (refCount as number) > 0) {
          dataPlugin.getData().resizeRefCount = refCount - 1;
          return;
        }
      }
      this.onResize();
    });
    this.graph.on("blank:click", (args: EventParams) => {
      this.onBlankClick();
      if (this.emit) {
        this.emit({
          type: EventType.BLANK_CLICK,
          eventParam: { ...args, graph: this.graph }
        });
      }
    });
    this.graph.on("cell:contextmenu", (args: { e: Event; x: number; y: number; cell: Cell; view: View }) => {
      this.onContextMenu(args);
    });
    this.graph.on("node:moved", () => {
      resizeGrap(this.graph);
    });
    this.graph.on("node:selected", (args: { node: Node }) => {
      //console.log("node:selected", args.node);
      // 检查是否触发事件
      if (!validTriggerEvent(this.graph, TriggerEventType.SELECT)) {
        return;
      }
      // 清空右键菜单
      this.removeContextMenu();
      // 判断选中节点的个数
      if (this.graph.getSelectedCellCount() > 1) {
        return;
      }
      if (this.emit) {
        this.emit({
          type: EventType.NODE_CLICK,
          eventParam: { ...args, graph: this.graph }
        });
      }
    });
    this.graph.on("node:resizing", (args: { e: Event; x: number; y: number; node: Node; view: View }) => {
      //console.log(args);
      this.onNodeResizing(args);
    });
    this.graph.on("node:rotate", (args: { e: Event; x: number; y: number; node: Node; view: View }) => {
      this.onNodeRotate(args);
    });
    this.graph.on("node:rotating", (args: { e: Event; x: number; y: number; node: Node; view: View }) => {
      this.onNodeRotating(args);
    });
    this.graph.on("node:added", (args: { node: Node; index: number }) => {
      this.onNodeAdd(args, this.graph);
    });
  }

  onBlankClick() {
    // 移除右键菜单
    this.removeContextMenu();
  }
  onResize() {
    initGraphSize(this.graph);
  }
  onNodeResizing(args: { e: Event; x: number; y: number; node: Node; view: View }) {
    const node = args.node;
    if (isGroup(node)) {
      this.graphGroup.resize(node);
    }
  }
  onNodeRotate(args: { e: Event; x: number; y: number; node: Node; view: View }) {
    const node = args.node;
    if (isGroup(node)) {
      this.graphGroup.rotate(node);
    }
  }
  onNodeRotating(args: { e: Event; x: number; y: number; node: Node; view: View }) {
    const node = args.node;
    if (isGroup(node)) {
      this.graphGroup.rotating(node);
    }
  }

  onContextMenu(args: { e: Event; x: number; y: number; cell: Cell; view: View }) {
    // 判断当前选中的节点是否是当前右键的节点,非选中节点不允许右键
    const cells = this.graph.getSelectedCells();
    // 如果选中的节点中不包括当前右键的节点
    let include = false;
    if (cells) {
      include = cells.includes(args.cell);
    }
    if (!include) {
      this.graph.clearTransformWidgets();
      // 设置不触发下面的unselect事件
      increaseInvalidTriggerEvent(this.graph, TriggerEventType.UNSELECT);
      this.graph.unselect(cells, { silent: false });
      // 设置不触发下面的resetSelection事件
      increaseInvalidTriggerEvent(this.graph, TriggerEventType.SELECT);
      this.graph.resetSelection(args.cell, { silent: false });
    }
    this.createContextMenu(args);
  }
  onContextMenuItemClick(e: Event, item: ContextMenuItem) {
    if (!item.type || !item.enable) {
      return;
    }
    // 点击后隐藏右键菜单
    this.removeContextMenu();
    this.contextmenu.trigger(item, this.graph, this.graphGroup);
  }

  createContextMenu(args: { e: Event; x: number; y: number; cell: Cell; view: View }) {
    const items: ContextMenuItem[] = this.contextmenu.create(this.graph);
    if (items.length == 0) {
      console.log("contextmenu items size less 1, no display");
      return;
    }
    if (!this.contextMenuParentContainer) {
      this.contextMenuParentContainer = Dom.createElement("div");
      Dom.addClass(this.contextMenuParentContainer, "graph-contextmenu-container");
      Dom.setAttribute(this.contextMenuParentContainer, "id", "graph-contextmenu-container");
      this.graph.container.appendChild(this.contextMenuParentContainer);
    }
    if (Dom.hasClass(this.contextMenuParentContainer, "graph-contextmenu-container-hide")) {
      Dom.removeClass(this.contextMenuParentContainer, "graph-contextmenu-container-hide");
    }
    if (this.contextMenuParentContainer.hasChildNodes()) {
      this.contextMenuParentContainer.removeChild(this.contextMenuContainer);
    }
    this.contextMenuContainer = Dom.createElement("div");
    Dom.prepend(this.contextMenuParentContainer, this.contextMenuContainer);

    Dom.addClass(this.contextMenuContainer, "graph-contextmenu");
    for (const item of items) {
      const child = Dom.createElement("div");
      child.innerHTML = item.title;
      Dom.addClass(child, "graph-contextmenu-item");
      if (!item.enable) {
        Dom.addClass(child, "graph-contextmenu-item-disabled");
      }
      Dom.setAttributes(child, {
        "data-type": item.type
      });
      child.addEventListener("click", e => {
        this.onContextMenuItemClick(e, item);
      });
      Dom.append(this.contextMenuContainer, child);
    }
    // 设置位置
    Dom.setAttributes(this.contextMenuParentContainer, {
      style: "left:" + args.x + "px;top:" + args.y + "px"
    });
  }
  removeContextMenu() {
    if (this.contextMenuParentContainer && this.contextMenuParentContainer.hasChildNodes()) {
      this.contextMenuParentContainer.removeChild(this.contextMenuContainer);
      if (!Dom.hasClass(this.contextMenuParentContainer, "graph-contextmenu-container-hide")) {
        Dom.addClass(this.contextMenuParentContainer, "graph-contextmenu-container-hide");
      }
    }
  }
  onNodeAdd(args: { node: Node; index: number }, graph: Graph) {
    // 处理组的拖拽添加
    this.handleGroupDndAdd(args.node, graph);
  }
  handleGroupDndAdd(node: Node, graph: Graph) {
    const data = node.getData();
    if (!data || !data.dndCells) {
      return;
    }
    let dndCells = data.dndCells as Cell[];
    // reset
    node.setData({ dndCells: null });
    if (dndCells.length < 2) {
      return;
    }
    // 只算子组件
    const childCells = dndCells.slice(1);
    renderGroupCells(graph, childCells, node, true);
  }
}

export default GraphEvent;
