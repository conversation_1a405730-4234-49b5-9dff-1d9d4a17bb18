export namespace OsParam {
  interface FilterListParams {
    name: string;
    extensions: string[];
  }
  export interface SelectFileParams {
    title: string;
    filterList: FilterListParams[];
  }

  export interface OpenDirectoryParams {
    id: string;
  }

  export interface OpenSaveFileDialogParams {
    title: string;
    defaultPath: string;
    filterList: FilterListParams[];
  }

  export interface SelectFileDialogParams {
    selectType?: "file" | "folder" | "both";
    filters?: { name: string; extensions: string[] }[];
  }

  export interface CopyFileParams {
    src: string;
    dest: string;
  }
}
