interface FileItem {
  // 文件相关信息字段
  checked: boolean;
  index?: number;
  id: number;
  fileName: string; // 文件名
  fileSize: number; // 文件大小
  fileSizeAs: string; // 文件大小
  type: string; // 文件类型
  path: string; // 文件下载地址
  lastModified: string; // 文件更新时间
  status: string; // 文件状态
  percentType: string; // 进度条
  percent: number; // 文件上传或者下载进度
  checkSum: number;
  hasTask: boolean;
  taskid: string; // 任务号
  fileParentPath: string;
  // 新增：下载时重命名
  rename?: string;
}

interface UrpcFileItem {
  fileName: string; // 文件名
  fileSize: number; // 文件大小
  lastModified: string; // 文件更新时间
  checkSum: number;
  fileParentPath: string;
}
interface UpadRpcFileDownloadItem {
  /** 文件对应表格行，方便显示用，cbb用不到 */
  fileRow?: number;
  /** 文件本地全路径 */
  filePath: string;
  /** 文件大小，如果不设置，cbb读取文件信息再计算 */
  fileSize?: number;
  /** 文件的装置全路径如/shr/app.xml，
   * 如果不设置cbb取remoteParentPath和文件名作为终端文件路径
   * 适用于本地文件名和终端文件名不一致的情形
   */
  remotePath?: string;
}
export type { FileItem, UrpcFileItem, UpadRpcFileDownloadItem };
