import { defineStore } from "pinia";
import { KeepAliveState } from "@/stores/interface";

const name = "simple-keepAlive"; // 定义模块名称

/** KeepAlive模块 */
export const useKeepAliveStore = defineStore({
  id: name,
  state: (): KeepAliveState => ({
    keepAliveName: []
  }),
  actions: {
    // Add KeepAliveName
    async addKeepAliveName(name: string) {
      !this.keepAliveName.includes(name) && this.keepAliveName.push(name);
    },
    // Remove KeepAliveName
    async removeKeepAliveName(name: string) {
      this.keepAliveName = this.keepAliveName.filter((item: string) => item !== name);
    },
    // Set KeepAliveName
    async setKeepAliveName(keepAliveName: string[] = []) {
      this.keepAliveName = keepAliveName;
    }
  }
});
