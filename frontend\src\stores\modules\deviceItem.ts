export interface DeviceItem {
  index?: number;
  id: string;
  ip: string;
  name: string;
  port: string;
  encrypted: boolean;
  prjType: number;
  deviceType: number;
  isConnect: boolean;
  isActive: boolean;
  connectTimeout?: number;
  paramTimeout?: number;
  readTimeout?: number;
  connectTime: string;
  status: string; // 文件状态
  percentType: string; // 进度条
  percent: number; // 文件上传或者下载进度
  downFile: boolean;
  importParam: boolean;
}
