<template>
  <div class="info-container card">
    <div class="button-container">
      <el-button :icon="Download" type="success" @click="exportToExcel">{{ t("device.info.export") }}</el-button>
    </div>
    <el-descriptions title="" border :column="2" direction="horizontal">
      <el-descriptions-item v-for="item in data" :key="item.index" :label="item.desc" label-align="right">{{ item.value }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Download } from "@element-plus/icons-vue";
import { ElLoading, ElMessageBox } from "element-plus";
import { deviceinfoApi } from "@/api/modules/biz/debug/deviceinfo";
import { osControlApi } from "@/api/modules/biz/os";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { addConsole } = useDebugStore();
const { debugIndex } = useDebugStore();
const props = defineProps<{ deviceId: string }>(); // 组件透传装置ID

// 初始化 data 为一个空数组，并指定其类型
const data = ref<DebugInfoItem[]>([]);

onMounted(async () => {
  const comp = debugIndex.compData.get(props.deviceId);
  console.log(comp?.name);
  getDeviceInfo({ names: [comp?.pname, comp?.name] }); // props.deviceId 已在 API 调用中透传
});

async function getDeviceInfo(param: any) {
  try {
    const response = await deviceinfoApi.getDeviceInfoByDevice(props.deviceId, param);
    if (Number(response.code) === 0) {
      // 确保 response.data 是一个数组
      if (Array.isArray(response.data)) {
        data.value = response.data as DebugInfoItem[];
      } else {
        addConsole(t("device.info.getInfoFailed") + t("device.info.dataEmpty"));
      }
    } else {
      addConsole(t("device.info.getInfoFailed") + response.msg);
    }
  } catch (error) {
    addConsole(t("device.info.getInfoFailed") + error);
  }
}

const exportToExcel = async () => {
  const defaultPath = t("device.info.title") + ".xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.info.title"),
    defaultPath,
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "docx", extensions: ["docx"] }
    ]
  });
  // 导出路径不存在返回
  if (!selectPath) {
    return;
  }
  console.log("selectPath:", selectPath);

  // 确保传递的 data.value 是一个对象数组
  const simplifiedData = data.value.map(item => ({
    description: item.desc,
    value: item.value
  }));

  // 创建 loading 实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: t("device.info.exportLoading"),
    background: "rgba(0, 0, 0, 0.3)"
  });

  try {
    const result = await deviceinfoApi.exportDeviceInfoByDevice(props.deviceId, {
      selectPath: String(selectPath),
      data: simplifiedData
    });

    // 判断返回结果中的code为0，提示成功，否则提示失败
    if (Number(result.code) === 0) {
      addConsole(t("device.info.exportSuccess") + "，" + selectPath);
      ElMessageBox.alert(t("device.info.exportSuccess"), t("device.info.exportTip"), {
        confirmButtonText: t("device.info.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("device.info.exportFailed"));
      ElMessageBox.alert(t("device.info.exportFailed"), t("device.info.exportTip"), {
        confirmButtonText: t("device.info.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.info.exportFailed") + ":", error);
    ElMessageBox.alert(t("device.info.exportFailed"), t("device.info.exportTip"), {
      confirmButtonText: t("device.info.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    loadingInstance.close();
  }
};

watch(
  () => debugIndex.compData.get(props.deviceId),
  newValue => {
    if (newValue) {
      const comp = debugIndex.compData.get(props.deviceId);
      getDeviceInfo({ names: [comp?.pname, comp?.name] });
    }
  }
);
</script>

<style scoped lang="scss">
.info-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 0;
}
.button-container {
  margin-top: 10px;
  margin-bottom: 10px;
}
.el-descriptions {
  padding: 0;
}
</style>
