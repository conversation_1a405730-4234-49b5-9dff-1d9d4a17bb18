<template>
  <div class="upload-box">
    <el-upload
      :id="uuid"
      :action="`#`"
      :auto-upload="false"
      :disabled="self_disabled"
      :before-upload="beforeUpload"
      :http-request="handleHttpUpload"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :drag="drag"
      :accept="fileType.join(',')"
      :style="{ height, width, borderRadius }"
    >
      <template v-if="imageUrl">
        <img :src="imageUrl" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div v-if="!self_disabled" class="handle-icon" @click="editImg">
            <el-icon><Edit /></el-icon>
            <span>{{ t("components.upload.edit") }}</span>
          </div>
          <div class="handle-icon" @click="imgViewVisible = true">
            <el-icon><ZoomIn /></el-icon>
            <span>{{ t("components.upload.view") }}</span>
          </div>
          <div v-if="!self_disabled" class="handle-icon" @click="deleteImg">
            <el-icon><Delete /></el-icon>
            <span>{{ t("components.upload.delete") }}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="upload-empty">
          <slot name="empty">
            <el-icon><Plus /></el-icon>
            <!-- <span>{{ t('components.upload.uploadImage') }}</span> -->
          </slot>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
    <el-image-viewer v-if="imgViewVisible" :url-list="[imageUrl]" @close="imgViewVisible = false" />
  </div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject } from "vue";
import { useI18n } from "vue-i18n";
import { generateUUID } from "@/utils";
import { uploadApi } from "@/api";
import { ElNotification, formContextKey, formItemContextKey } from "element-plus";
import type { UploadProps, UploadRawFile, UploadRequestOptions } from "element-plus";

// 使用国际化
const { t } = useI18n();

interface UploadFileProps {
  imageUrl: string; // 图片地址 ==> 必传
  api?: (params: any) => Promise<any>; // 上传图片的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
  fileSize?: number; // 图片大小限制 ==> 非必传（默认为 5M）
  fileType?: File.ImageMimeType[]; // 图片类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
  height?: string; // 组件高度 ==> 非必传（默认为 150px）
  width?: string; // 组件宽度 ==> 非必传（默认为 150px）
  borderRadius?: string; // 组件边框圆角 ==> 非必传（默认为 8px）
  autoUpload?: boolean; // 是否自动上传,false将不会调用上传接口 ==> 非必传（默认为 true）
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
  imageUrl: "",
  drag: true,
  disabled: false,
  fileSize: 5,
  fileType: () => ["image/jpeg", "image/png", "image/gif"],
  height: "150px",
  width: "150px",
  borderRadius: "8px",
  autoUpload: true
});

// 生成组件唯一id
const uuid = ref("id-" + generateUUID());

// 查看图片
const imgViewVisible = ref(false);
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

/**
 * @description 图片上传
 * @param options upload 所有配置项
 * */
const emit = defineEmits<{
  "update:imageUrl": [value: string];
}>();
const handleHttpUpload = async (options: UploadRequestOptions) => {
  // 如果不是自动上传，将图片转为 base64
  if (!props.autoUpload) {
    getBase64(options.file).then(res => {
      emit("update:imageUrl", res as string);
    });
  } else {
    let formData = new FormData();
    formData.append("file", options.file);
    try {
      const api = props.api ?? uploadApi.uploadImg;
      const { data } = await api(formData);
      emit("update:imageUrl", data.fileUrl);
      // 调用 el-form 内部的校验方法（可自动校验）
      formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
    } catch (error) {
      options.onError(error as any);
    }
  }
};

/**
 * @description 删除图片
 * */
const deleteImg = () => {
  emit("update:imageUrl", "");
};

/**
 * @description 编辑图片
 * */
const editImg = () => {
  const dom = document.querySelector(`#${uuid.value} .el-upload__input`);
  dom && dom.dispatchEvent(new MouseEvent("click"));
};

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
  const imgType = props.fileType.includes(rawFile.type as File.ImageMimeType);
  if (!imgType)
    ElNotification({
      title: t("components.upload.tips"),
      message: t("components.upload.invalidFormat"),
      type: "warning"
    });
  if (!imgSize)
    setTimeout(() => {
      ElNotification({
        title: t("components.upload.tips"),
        message: t("components.upload.fileSizeExceeded", { size: props.fileSize }),
        type: "warning"
      });
    }, 0);
  return imgType && imgSize;
};

/**
 * @description 图片上传成功
 * */
const uploadSuccess = () => {
  //只有自动上传才会提示
  if (props.autoUpload) {
    ElNotification({
      title: t("components.upload.tips"),
      message: t("components.upload.uploadSuccess"),
      type: "success"
    });
  }
};

/**
 * @description 图片上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: t("components.upload.tips"),
    message: t("components.upload.uploadFailed"),
    type: "error"
  });
};

// 文件转base64，用于显示图片
const getBase64 = (file: UploadRawFile) => {
  return new Promise((resolve, reject) => {
    // FileReader类就是专门用来读文件的
    const reader = new FileReader();
    reader.readAsDataURL(file);
    // 成功和失败返回对应的信息，reader.result一个base64，可以直接使用
    reader.onload = () => resolve(reader.result);
    // 失败返回失败的信息
    reader.onerror = error => reject(error);
  });
};
</script>

<style scoped lang="scss">
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;
      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}
:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed;
    background: var(--el-disabled-bg-color) !important;
    border: 1px dashed var(--el-border-color-darker);
    &:hover {
      border-color: var(--el-border-color-darker) !important;
    }
  }
}
.upload-box {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      width: v-bind(width);
      height: v-bind(height);
      border-radius: v-bind(borderRadius);
    }
  }
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .upload-handle {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 50%);
    opacity: 0;
    transition: opacity 0.3s;
    .handle-icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
      font-size: 12px;
      color: #ffffff;
      cursor: pointer;
      .el-icon {
        margin-bottom: 4px;
        font-size: 16px;
      }
    }
  }
  .el-upload:hover .upload-handle {
    opacity: 1;
  }
  .upload-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--el-text-color-secondary);
    .el-icon {
      margin-bottom: 8px;
      font-size: 28px;
    }
  }
}
</style>
