<template>
  <el-radio-group v-model="statusValue" @change="onChange">
    <el-radio value="open">{{ t("graphDefine.setStatus.open") }}</el-radio>
    <el-radio value="close">{{ t("graphDefine.setStatus.close") }}</el-radio>
    <el-radio value="">{{ t("graphDefine.setStatus.none") }}</el-radio>
  </el-radio-group>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { EquipmentStatus } from "../../../graph/Graph";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const prop = defineProps<{
  value: EquipmentStatus;
}>();
const statusValue = ref(prop.value);
const emit = defineEmits<{
  (e: "change", data: EquipmentStatus): void;
}>();
const onChange = () => {
  emit("change", statusValue.value);
};
</script>
