export default {
  downList: {
    title: "下载列表",
    deviceDirectory: "装置目录",
    addFile: "添加文件",
    addFolder: "添加文件夹",
    defaultExportFileName: "下载文件列表.xlsx",
    exportTitle: "导出下载文件列表",
    importTitle: "导入下载文件列表",
    exportSuccess: "导出下载文件列表成功，{path}",
    exportFailed: "导出下载文件列表失败",
    importSuccess: "导入下载文件列表成功",
    importFailed: "导入下载文件列表失败: {msg}",
    fileExists: "文件{path}已经存在，添加失败！",
    fileDeleted: "文件{path}已删除",
    filesDeleted: "文件已批量删除",
    buttons: {
      addFile: "添加文件",
      addFolder: "添加文件夹",
      import: "导入",
      export: "导出",
      delete: "删除",
      clear: "清空",
      moveUp: "上移",
      moveDown: "下移"
    },
    columns: {
      index: "序号",
      fileName: "文件名",
      fileSize: "文件大小",
      filePath: "文件路径",
      lastModified: "最后修改时间",
      operation: "操作"
    },
    export: {
      defaultPath: "下载列表",
      title: "导出下载列表"
    },
    import: {
      title: "导入下载列表"
    },
    dialog: {
      title: "提示",
      confirm: "确定"
    },
    messages: {
      filesDeleted: "文件已删除",
      exportSuccess: "导出成功",
      exportFailed: "导出失败",
      importSuccess: "导入成功",
      importFailed: "导入失败"
    }
  }
};
