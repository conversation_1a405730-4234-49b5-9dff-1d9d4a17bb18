import { moduleRequest } from "@/api/request";
import { ReqId, ResPage, Button } from "@/api/interface";
const http = moduleRequest("/sys/limit/button/");

const buttonApi = {
  /** 获取单页分页 */
  page(params: Button.Page) {
    return http.get<ResPage<Button.ButtonInfo>>("page", params);
  },
  /** 获取单页详情 */
  detail(params: ReqId) {
    return http.get<Button.ButtonInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除单页 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 删除单页 */
  batch(params: Button.Batch) {
    return http.post("batch", params);
  }
};

export { buttonApi };
