<template>
  <div class="content-box">
    <!-- 左侧 -->
    <div class="device-left" ref="deviceLeft" :style="deviceLeftStyle.showDeviceList">
      <DeviceSearch></DeviceSearch>
      <DeviceList></DeviceList>
    </div>
    <!-- 分隔线 -->
    <div ref="resizeVerticalRef" class="resize-vertical-no-border">
      <!-- 折叠按钮 - 垂直居中 -->
      <div class="collapse-button-container">
        <DeviceListCollapse :is-collapse="isCollapse" @update:is-collapse="isCollapse = $event" />
      </div>
    </div>
    <!-- 右侧 -->
    <div class="device-right" ref="deviceRight" :class="{ 'with-monitor': showMessageMonitor }">
      <div class="main-content" :class="{ 'split-view': showMessageMonitor }">
        <Devices></Devices>
        <Console></Console>
      </div>

      <!-- 报文监视面板 -->
      <div v-if="showMessageMonitor" class="message-monitor-panel">
        <MessageMonitor :device-id="currentDeviceId" @close="handleCloseMessageMonitor" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="treeFilter">
import DeviceSearch from "@/views/biz/debug/device/components/DeviceSearch.vue";
import Devices from "@/views/biz/debug/device/components/Devices.vue";
import DeviceList from "@/views/biz/debug/device/components/DeviceList.vue";
import Console from "@/views/biz/debug/device/components/Console.vue";
import DeviceListCollapse from "@/views/biz/debug/device/components/DeviceListCollapse.vue";
import MessageMonitor from "@/views/biz/debug/device/components/MessageMonitor.vue";
import { useResizeVertical } from "@/scripts/resizeVertical";
import { ref, computed, onMounted, onUnmounted } from "vue";
import mittBus from "@/utils/mittBus";
import { DebugDeviceInfo } from "@/stores/interface";
import { deviceConnectApi } from "@/api/modules/biz/debug/deviceinfomenu";
const deviceLeft = ref();
const resizeVerticalRef = ref();
const deviceRight = ref();
const isCollapse = ref(false);
const showMessageMonitor = ref(false);
const currentDeviceId = ref("");

const deviceLeftStyle = computed<any>(() => {
  let value = isCollapse.value ? "none" : "flex";
  return { showDeviceList: { display: `${value}` } };
});

// 处理显示报文监视
const handleShowMessageMonitor = (device: unknown) => {
  const deviceInfo = device as DebugDeviceInfo;
  console.log("显示报文监视", deviceInfo);
  currentDeviceId.value = deviceInfo.id;
  showMessageMonitor.value = true;
  console.log("showMessageMonitor:", showMessageMonitor.value, "currentDeviceId:", currentDeviceId.value);
};

// 处理关闭报文监视
const handleCloseMessageMonitor = async () => {
  try {
    // 如果正在监听，先停止监听
    if (currentDeviceId.value) {
      await deviceConnectApi.stopMessageMonitor(currentDeviceId.value);
    }
  } catch (error) {
    console.error("停止报文监视失败:", error);
  } finally {
    showMessageMonitor.value = false;
    currentDeviceId.value = "";
  }
};
useResizeVertical(deviceLeft, deviceRight, resizeVerticalRef, undefined, {
  persistent: true,
  keyOne: "deviceLeft",
  keyTwo: "deviceRight",
  defaultOne: "240",
  defaultTwo: "",
  maxOne: 340,
  minOne: 240
});

onMounted(() => {
  mittBus.on("showMessageMonitor", handleShowMessageMonitor);
});

onUnmounted(() => {
  mittBus.off("showMessageMonitor", handleShowMessageMonitor);
});
</script>

<style scoped lang="scss">
@import "./index";
@import "@/styles/resize";

.device-right {
  &.with-monitor {
    display: flex;
    flex-direction: row;
    gap: 8px;
    height: 100%;
    overflow: hidden; // 防止整体滚动
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; // 防止flex子项溢出
    height: 100%; // 确保高度填满

    &.split-view {
      width: 60%; // 主内容占60%
    }

    // 确保子组件正确处理溢出
    > * {
      flex-shrink: 0; // 防止子组件被压缩
      overflow: auto; // 允许子组件内部滚动
    }
  }

  .message-monitor-panel {
    width: 40%; // 报文监视占40%
    min-width: 300px;
    max-width: 500px;
    border-left: 1px solid var(--el-border-color);
    background-color: var(--el-bg-color);
    overflow: hidden; // 防止面板整体滚动
  }
}
</style>
