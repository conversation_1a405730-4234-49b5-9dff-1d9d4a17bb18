export default {
  search: {
    placeholder: "Search by keyword"
  },
  categories: {
    title: "📦IT Tools",
    formatting: "📝Formatting Tools",
    xml: "🟡XML Formatter",
    json: "🟡JSON Formatter",
    conversion: "🔄Conversion Tools",
    radix: "🟢Radix Converter",
    temperature: "🟢Temperature Converter",
    encryption: "🔑Encryption Tools",
    textEncryption: "🔵Text Encryption/Decryption"
  },
  encryption: {
    title: "Text Encryption/Decryption",
    description: "Encrypt and decrypt plaintext using encryption algorithms such as AES, TripleDES, Rabbit, or RC4",
    encrypt: "Encrypt",
    inputText: "Text to encrypt:",
    inputPlaceholder: "Enter the text you want to encrypt...",
    key: "Key:",
    keyPlaceholder: "Enter encryption key",
    algorithm: "Encryption Algorithm:",
    outputText: "Encrypted Text:",
    outputPlaceholder: "Encrypted result will appear here...",
    decrypt: "Decrypt",
    decryptInputText: "Text to decrypt:",
    decryptInputPlaceholder: "Enter the encrypted text to decrypt...",
    decryptKey: "Key:",
    decryptAlgorithm: "Decryption Algorithm:",
    decryptOutputText: "Decrypted Text:",
    decryptError: "Unable to decrypt text"
  },
  json: {
    title: "JSON Formatter",
    description: "Format JSON strings into a friendly readable format",
    sortKeys: "Sort Keys",
    indentSize: "Indent Size",
    inputLabel: "JSON to format",
    inputPlaceholder: "Paste your JSON...",
    outputLabel: "Formatted JSON",
    invalid: "This document does not conform to JSON specification, please check"
  },
  xml: {
    title: "XML Formatter",
    description: "Format XML strings into a friendly readable format",
    collapseContent: "Collapse Content:",
    indentSize: "Indent Size:",
    inputLabel: "Input XML",
    inputPlaceholder: "Paste your XML...",
    outputLabel: "Formatted XML",
    invalid: "This document does not conform to XML specification, please check"
  },
  temperature: {
    title: "Temperature Converter",
    description: "Convert between Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur, and Rømer temperature degrees",
    kelvin: "Kelvin",
    kelvinUnit: "K",
    celsius: "Celsius",
    celsiusUnit: "°C",
    fahrenheit: "Fahrenheit",
    fahrenheitUnit: "°F",
    rankine: "Rankine",
    rankineUnit: "°R",
    delisle: "Delisle",
    delisleUnit: "°De",
    newton: "Newton",
    newtonUnit: "°N",
    reaumur: "Réaumur",
    reaumurUnit: "°Ré",
    romer: "Rømer",
    romerUnit: "°Rø"
  },
  radix: {
    title: "Radix Converter",
    description: "Convert numbers between different bases (decimal, hexadecimal, binary, octal, base64, etc.)",
    inputLabel: "Number to convert",
    inputPlaceholder: "Please enter a number (e.g.: 100)",
    outputLabel: "Conversion Results",
    binary: "Binary (2)",
    binaryPlaceholder: "Binary result...",
    octal: "Octal (8)",
    octalPlaceholder: "Octal result...",
    decimal: "Decimal (10)",
    decimalPlaceholder: "Decimal result...",
    hex: "Hexadecimal (16)",
    hexPlaceholder: "Hexadecimal result...",
    base64: "Base64 (64)",
    base64Placeholder: "Base64 result...",
    customBase: "Custom Base",
    customBasePlaceholder: "Base {{base}} result..."
  },
  jsonViewer: {
    title: "JSON Formatter",
    description: "Format JSON strings into a friendly readable format",
    sortKeys: "Sort Keys",
    indentSize: "Indent Size",
    inputJson: "JSON to format",
    formattedJson: "Formatted JSON",
    placeholder: "Paste your JSON...",
    validationError: "This document does not conform to JSON specification. Please check"
  }
};
