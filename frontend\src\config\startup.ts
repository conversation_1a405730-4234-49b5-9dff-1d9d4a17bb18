/**
 * 启动优化配置
 */

export interface StartupConfig {
  // 性能监控
  enablePerformanceMonitor: boolean;

  // 缓存配置
  enableStartupCache: boolean;
  cacheExpiry: number; // 缓存过期时间（毫秒）

  // 资源加载配置
  delayedResourcesTimeout: number; // 延迟资源加载时间（毫秒）
  iconPreloadDelay: number; // 图标预加载延迟时间（毫秒）

  // 组件加载配置
  enableLazyComponents: boolean;
  componentLoadBatchSize: number; // 组件批量加载大小

  // 路由配置
  enableRouteCache: boolean;
  routePreloadDelay: number; // 路由预加载延迟时间（毫秒）

  // 服务启动配置
  enableAsyncServiceStart: boolean;
  serviceStartTimeout: number; // 服务启动超时时间（毫秒）

  // 开发模式配置
  isDevelopment: boolean;
  enableDebugLogs: boolean;
}

// 默认启动配置
export const defaultStartupConfig: StartupConfig = {
  // 性能监控
  enablePerformanceMonitor: true,

  // 缓存配置
  enableStartupCache: true,
  cacheExpiry: 30 * 60 * 1000, // 30分钟

  // 资源加载配置
  delayedResourcesTimeout: 50, // 50ms后加载非关键资源
  iconPreloadDelay: 1000, // 1秒后加载其他图标集合

  // 组件加载配置
  enableLazyComponents: true,
  componentLoadBatchSize: 5,

  // 路由配置
  enableRouteCache: true,
  routePreloadDelay: 100, // 100ms延迟

  // 服务启动配置
  enableAsyncServiceStart: true,
  serviceStartTimeout: 5000, // 5秒超时

  // 开发模式配置
  isDevelopment: import.meta.env.DEV,
  enableDebugLogs: import.meta.env.DEV
};

// 获取启动配置
export function getStartupConfig(): StartupConfig {
  // 可以从localStorage或其他地方读取用户自定义配置
  const userConfig = getUserStartupConfig();

  return {
    ...defaultStartupConfig,
    ...userConfig
  };
}

// 获取用户自定义启动配置
function getUserStartupConfig(): Partial<StartupConfig> {
  try {
    const stored = localStorage.getItem("visualdebug_startup_config");
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn("读取用户启动配置失败:", error);
  }

  return {};
}

// 保存用户启动配置
export function saveUserStartupConfig(config: Partial<StartupConfig>): void {
  try {
    const currentConfig = getUserStartupConfig();
    const newConfig = { ...currentConfig, ...config };

    localStorage.setItem("visualdebug_startup_config", JSON.stringify(newConfig));
    console.log("✅ 启动配置已保存");
  } catch (error) {
    console.warn("❌ 保存启动配置失败:", error);
  }
}

// 重置启动配置
export function resetStartupConfig(): void {
  try {
    localStorage.removeItem("visualdebug_startup_config");
    console.log("🔄 启动配置已重置");
  } catch (error) {
    console.warn("❌ 重置启动配置失败:", error);
  }
}

// 启动优化建议
export interface OptimizationSuggestion {
  type: "info" | "warning" | "error";
  title: string;
  description: string;
  action?: string;
}

// 获取启动优化建议
export function getOptimizationSuggestions(): OptimizationSuggestion[] {
  const suggestions: OptimizationSuggestion[] = [];
  const config = getStartupConfig();

  // 检查缓存配置
  if (!config.enableStartupCache) {
    suggestions.push({
      type: "warning",
      title: "启动缓存已禁用",
      description: "启用启动缓存可以显著提高应用启动速度",
      action: "启用启动缓存"
    });
  }

  // 检查性能监控
  if (!config.enablePerformanceMonitor && config.isDevelopment) {
    suggestions.push({
      type: "info",
      title: "性能监控已禁用",
      description: "在开发模式下启用性能监控有助于识别性能瓶颈",
      action: "启用性能监控"
    });
  }

  // 检查延迟加载配置
  if (config.delayedResourcesTimeout > 200) {
    suggestions.push({
      type: "warning",
      title: "资源延迟加载时间过长",
      description: "延迟时间过长可能影响用户体验",
      action: "减少延迟时间"
    });
  }

  // 检查服务启动超时
  if (config.serviceStartTimeout < 3000) {
    suggestions.push({
      type: "warning",
      title: "服务启动超时时间过短",
      description: "超时时间过短可能导致服务启动失败",
      action: "增加超时时间"
    });
  }

  return suggestions;
}

// 应用启动优化配置
export function applyStartupOptimizations(config: StartupConfig): void {
  console.group("🚀 应用启动优化配置");

  console.log("性能监控:", config.enablePerformanceMonitor ? "✅ 启用" : "❌ 禁用");
  console.log("启动缓存:", config.enableStartupCache ? "✅ 启用" : "❌ 禁用");
  console.log("懒加载组件:", config.enableLazyComponents ? "✅ 启用" : "❌ 禁用");
  console.log("异步服务启动:", config.enableAsyncServiceStart ? "✅ 启用" : "❌ 禁用");
  console.log("延迟资源加载时间:", `${config.delayedResourcesTimeout}ms`);
  console.log("缓存过期时间:", `${config.cacheExpiry / 1000}秒`);

  const suggestions = getOptimizationSuggestions();
  if (suggestions.length > 0) {
    console.group("💡 优化建议");
    suggestions.forEach(suggestion => {
      const icon = suggestion.type === "error" ? "❌" : suggestion.type === "warning" ? "⚠️" : "ℹ️";
      console.log(`${icon} ${suggestion.title}: ${suggestion.description}`);
    });
    console.groupEnd();
  }

  console.groupEnd();
}

// 导出当前配置实例
export const startupConfig = getStartupConfig();
