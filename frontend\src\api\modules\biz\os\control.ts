import { moduleIpcRequest } from "@/api/request";
import { OsParam } from "@/api/interface/biz/os/osParam";
const ipc = moduleIpcRequest("controller/common/os/");

const osControlApi = {
  /** 打开文件夹 */
  openDirectory(params: OsParam.OpenDirectoryParams) {
    return ipc.invoke<any>("openDirectory", params);
  },
  /** 选择文件夹 */
  selectFolder() {
    return ipc.invoke<any>("selectFolder");
  },
  /** 选择文件-单选(带参数) */
  selectFileByParams(params: OsParam.SelectFileParams) {
    return ipc.invoke<any>("selectFileByParams", params);
  },
  /** 选择文件-多选(带参数) */
  selectFilesByParams(params: OsParam.SelectFileParams) {
    return ipc.invoke<any>("selectFilesByParams", params);
  },
  selectFileOrFolder(params: OsParam.SelectFileDialogParams) {
    return ipc.invoke<any>("selectFileOrFolder", params);
  },
  /** 获取目录内容 */
  getDirectoryContents(params: { path: string }) {
    return ipc.invoke<any>("getDirectoryContents", params);
  },
  /** 获取系统根目录 */
  getRootDirectories() {
    return ipc.invoke<any>("getRootDirectories");
  },
  /** 打开文件保存框 */
  openSaveFileDialog() {
    return ipc.invoke<any>("openSaveFileDialog");
  },
  /** 打开文件保存框(带参数) */
  openSaveFileDialogByParams(params: OsParam.OpenSaveFileDialogParams) {
    return ipc.invoke<string | undefined>("openSaveFileDialogByParams", params);
  },
  /** 获取文件或文件夹大小 */
  getFileOrFolderSize(params: { path: string }) {
    return ipc.invoke<any>("getFileOrFolderSize", params);
  },
  /** 复制文件 */
  copyFile(params: { src: string; dest: string }) {
    return ipc.invoke<any>("copyFile", params);
  },
  /** 获取系统常用文件夹 */
  getSystemSpecialFolders() {
    return ipc.invoke<any>("getSystemSpecialFolders");
  }
};

export { osControlApi };
