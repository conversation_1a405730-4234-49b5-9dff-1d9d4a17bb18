import { BasedbService } from "./basedb";
import { t } from "../../data/i18n/i18n";
import { logger } from "ee-core/log";

/**
 * sqlite数据存储
 * @class
 */
class SqlitedbService extends BasedbService {
  userTableName: string;
  constructor() {
    const options = {
      dbname: "sqlite-demo.db",
    };
    super(options);
    this.userTableName = "user";
    this.initTable();
    logger.info(
      `[SqlitedbService] ${t("services.database.databaseConnected")}`
    );
  }

  /*
   * 初始化表
   */
  private initTable(): void {
    try {
      // 检查表是否存在
      const masterStmt = this.db.prepare(
        "SELECT * FROM sqlite_master WHERE type=? AND name = ?"
      );
      let tableExists = masterStmt.get("table", this.userTableName);
      if (!tableExists) {
        // 创建表
        const create_user_table_sql = `CREATE TABLE ${this.userTableName}
        (
           id INTEGER PRIMARY KEY AUTOINCREMENT,
           name CHAR(50) NOT NULL,
           age INT
        );`;
        this.db.exec(create_user_table_sql);
        logger.info(
          `[SqlitedbService] ${t("services.database.tableCreated")}: ${this.userTableName}`
        );
      }
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.tableCreationFailed")}:`,
        error
      );
    }
  }

  /*
   * 增 Test data (sqlite)
   */
  async addTestDataSqlite(data: { name: string; age: number }) {
    try {
      const insert = this.db.prepare(
        `INSERT INTO ${this.userTableName} (name, age) VALUES (@name, @age)`
      );
      insert.run(data);
      logger.info(
        `[SqlitedbService] ${t("services.database.dataInserted")}:`,
        data
      );
      return true;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.dataInsertFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * 删 Test data (sqlite)
   */
  async delTestDataSqlite(name: string = ""): Promise<boolean> {
    try {
      const delUser = this.db.prepare(
        `DELETE FROM ${this.userTableName} WHERE name = ?`
      );
      delUser.run(name);
      logger.info(
        `[SqlitedbService] ${t("services.database.dataDeleted")}: ${name}`
      );
      return true;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.dataDeleteFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * 改 Test data (sqlite)
   */
  async updateTestDataSqlite(
    name: string = "",
    age: number = 0
  ): Promise<boolean> {
    try {
      const updateUser = this.db.prepare(
        `UPDATE ${this.userTableName} SET age = ? WHERE name = ?`
      );
      updateUser.run(age, name);
      logger.info(
        `[SqlitedbService] ${t("services.database.dataUpdated")}: ${name}, age: ${age}`
      );
      return true;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.dataUpdateFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * 查 Test data (sqlite)
   */
  async getTestDataSqlite(age: number = 0): Promise<any[]> {
    try {
      const selectUser = this.db.prepare(
        `SELECT * FROM ${this.userTableName} WHERE age = @age`
      );
      const users = selectUser.all({ age: age });
      logger.info(
        `[SqlitedbService] ${t("services.database.queryExecuted")}: age = ${age}, count = ${users.length}`
      );
      return users;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.queryFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * all Test data (sqlite)
   */
  async getAllTestDataSqlite(): Promise<any[]> {
    try {
      const selectAllUser = this.db.prepare(
        `SELECT * FROM ${this.userTableName} `
      );
      const allUser = selectAllUser.all();
      logger.info(
        `[SqlitedbService] ${t("services.database.queryExecuted")}: all users, count = ${allUser.length}`
      );
      return allUser;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.queryFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * get data dir (sqlite)
   */
  async getDataDir(): Promise<string> {
    try {
      const dir = this.storage.getDbDir();
      logger.info(
        `[SqlitedbService] ${t("services.database.dataDirRetrieved")}: ${dir}`
      );
      return dir;
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.dataDirRetrieveFailed")}:`,
        error
      );
      throw error;
    }
  }

  /*
   * set custom data dir (sqlite)
   */
  async setCustomDataDir(dir: string): Promise<void> {
    if (dir.length == 0) {
      return;
    }

    try {
      this.changeDataDir(dir);
      this.initTable();
      logger.info(
        `[SqlitedbService] ${t("services.database.customDataDirSet")}: ${dir}`
      );
    } catch (error) {
      logger.error(
        `[SqlitedbService] ${t("services.database.customDataDirSetFailed")}:`,
        error
      );
      throw error;
    }
  }
}
SqlitedbService.toString = () => "[class SqlitedbService]";
const sqlitedbService = new SqlitedbService();

export { SqlitedbService, sqlitedbService };
