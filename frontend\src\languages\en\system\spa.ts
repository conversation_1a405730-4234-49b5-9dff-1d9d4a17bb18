export default {
  title: "SPA Management",
  list: {
    title: "SPA List",
    add: "Add SPA",
    deleteSelected: "Delete Selected",
    deleteConfirm: "Are you sure you want to delete SPA {title}?"
  },
  form: {
    title: "{opt} SPA",
    basicSettings: "Basic Settings",
    functionSettings: "Function Settings",
    name: "SPA Name",
    type: "SPA Type",
    icon: "Icon",
    path: "Route Path",
    pathPlaceholder: "Please enter route path, e.g.: /home/<USER>",
    componentName: "Component Name",
    componentPath: "Component Path",
    linkPath: "Link Address",
    linkPathPlaceholder: "Please enter link address, e.g.: http://www.baidu.com",
    sort: "Sort",
    description: "Description",
    isHome: "Set as Home Page",
    isHide: "Hide Page",
    isFull: "Full Screen Page",
    isAffix: "Pin Tab",
    isKeepAlive: "Route Cache",
    cancel: "Cancel",
    confirm: "Confirm",
    nameRequired: "Please enter SPA name",
    typeRequired: "Please select SPA type",
    pathRequired: "Please enter route path",
    componentNameRequired: "Please enter component name",
    componentPathRequired: "Please enter component path",
    sortRequired: "Please enter sort order",
    iconRequired: "Please select icon"
  }
};
