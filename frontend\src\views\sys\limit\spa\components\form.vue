<!-- 
 * @Description: 单页管理表单页面

 * @Date: 2023-12-15 15:43:59
!-->
<template>
  <form-container v-model="visible" :title="`${spaProps.opt}${t('spa.title')}`" form-size="600px">
    <el-form
      ref="spaFormRef"
      :rules="rules"
      :disabled="spaProps.disabled"
      :model="spaProps.record"
      :hide-required-asterisk="spaProps.disabled"
      label-width="auto"
      label-suffix=" :"
    >
      <!-- 基本设置 -->
      <el-divider content-position="left">{{ t("spa.form.basicSettings") }}</el-divider>
      <s-form-item :label="t('spa.form.pageName')" prop="title">
        <s-input v-model="spaProps.record.title"></s-input>
      </s-form-item>
      <s-form-item :label="t('spa.form.pageType')" prop="menuType">
        <s-radio-group v-model="spaProps.record.menuType" :options="spaTypeOptions" button />
      </s-form-item>
      <s-form-item :label="t('spa.form.icon')" prop="icon">
        <SelectIconPlus v-model:icon-value="spaProps.record.icon!" />
      </s-form-item>
      <div v-if="spaProps.record.menuType === MenuTypeDictEnum.MENU">
        <s-form-item :label="t('spa.form.routePath')" prop="path">
          <s-input v-model="spaProps.record.path" :placeholder="t('spa.form.routePathPlaceholder')"></s-input>
        </s-form-item>
        <s-form-item :label="t('spa.form.componentName')" prop="name">
          <s-input v-model="spaProps.record.name">
            <template #prepend>setup name=</template>
          </s-input>
        </s-form-item>
        <s-form-item :label="t('spa.form.componentPath')" prop="component">
          <s-input v-model="spaProps.record.component">
            <template #prepend>src/views/</template>
          </s-input>
        </s-form-item>
      </div>
      <div v-else>
        <s-form-item :label="t('spa.form.routePath')" prop="path">
          <s-input v-model="spaProps.record.path" :placeholder="t('spa.form.routePathPlaceholder')"></s-input>
        </s-form-item>
      </div>
      <s-form-item :label="t('spa.form.sort')" prop="sortCode">
        <el-slider v-model="spaProps.record.sortCode" show-input :min="1" />
      </s-form-item>
      <s-form-item :label="t('spa.form.description')" prop="description">
        <s-input v-model="spaProps.record.description"></s-input>
      </s-form-item>
      <!-- 功能设置 -->
      <el-divider content-position="left">{{ t("spa.form.functionSettings") }}</el-divider>
      <el-row :gutter="24">
        <el-col :span="12">
          <s-form-item :label="t('spa.form.setHome')" prop="isHome">
            <s-radio-group v-model="spaProps.record.isHome" :options="yesOptions" button />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item :label="t('spa.form.hidePage')" prop="isHide">
            <s-radio-group v-model="spaProps.record.isHide" :options="yesOptions" button />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item :label="t('spa.form.fullScreen')" prop="isFull">
            <s-radio-group v-model="spaProps.record.isFull" :options="yesOptions" button />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item :label="t('spa.form.fixedTab')" prop="isAffix">
            <s-radio-group v-model="spaProps.record.isAffix" :options="yesOptions" button />
          </s-form-item>
        </el-col>
        <el-col :span="12">
          <s-form-item :label="t('spa.form.routeCache')" prop="isKeepAlive">
            <s-radio-group v-model="spaProps.record.isKeepAlive" :options="yesOptions" button />
          </s-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ t("spa.form.cancel") }}</el-button>
      <el-button v-show="!spaProps.disabled" type="primary" @click="handleSubmit">{{ t("spa.form.confirm") }}</el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { Spa, spaApi } from "@/api";
import { required } from "@/utils/formRules";
import { FormOptEnum, SysDictEnum, MenuTypeDictEnum } from "@/enums";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const visible = ref(false); //是否显示表单
const dictStore = useDictStore(); //字典仓库

// 单页类型选项
const spaTypeOptions = dictStore
  .getDictList(SysDictEnum.MENU_TYPE)
  .filter((item: { value: MenuTypeDictEnum }) => item.value == MenuTypeDictEnum.MENU || item.value == MenuTypeDictEnum.LINK);
// 是否选项
const yesOptions = dictStore.getDictList(SysDictEnum.YES_NO, true);

// 表单参数
const spaProps = reactive<FormProps.Base<Spa.SpaInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  title: [required(t("spa.form.validation.pageNameRequired"))],
  menuType: [required(t("spa.form.validation.pageTypeRequired"))],
  path: [required(t("spa.form.validation.routePathRequired"))],
  name: [required(t("spa.form.validation.componentNameRequired"))],
  component: [required(t("spa.form.validation.componentPathRequired"))],
  sortCode: [required(t("spa.form.validation.sortRequired"))],
  icon: [required(t("spa.form.validation.iconRequired"))]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<Spa.SpaInfo>) {
  Object.assign(spaProps, props); //合并参数
  if (props.opt == FormOptEnum.ADD) {
    //如果是新增,设置默认值
    spaProps.record.menuType = MenuTypeDictEnum.MENU;
    spaProps.record.sortCode = 99;
    spaProps.record.isHome = true;
    spaProps.record.isHide = true;
    spaProps.record.isFull = true;
    spaProps.record.isAffix = true;
    spaProps.record.isKeepAlive = true;
  }

  visible.value = true; //显示表单
  if (props.record.id) {
    //如果传了id，就去请求api获取record
    spaApi.detail({ id: props.record.id }).then(res => {
      spaProps.record = res.data;
    });
  }
}

// 提交数据（新增/编辑）
const spaFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  spaFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败
    //提交表单
    await spaApi
      .submitForm(spaProps.record, spaProps.record.id != undefined)
      .then(() => {
        spaProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>
<style lang="scss" scoped>
:deep(.s-input-group__prepend) {
  padding: 0 10px !important;
}
</style>
