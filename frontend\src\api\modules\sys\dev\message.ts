import { moduleRequest } from "@/api/request";
import { ReqId, ResPage, SysMessage } from "@/api";
const http = moduleRequest("/sys/dev/message/");
const messageApi = {
  /** 获取消息分页 */
  page(params: any) {
    return http.get<ResPage<SysMessage.SysMessageInfo>>("page", params);
  },
  /** 获取消息详情 */
  detail(params = {}) {
    return http.get<SysMessage.SysMessageInfo>("detail", params);
  },
  /** 获取已读未读详情 */
  readDetail(params = {}) {
    return http.get<SysMessage.receiverDetail[]>("readDetail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除单页 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  }
};

export { messageApi };
