import { calculateArc, calculateEllipse, calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "1200",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(11, 10.67, 23, 22.47)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(11, 26.53, 23, 22.47)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 17.33,
        y1: 15.29,
        x2: 23,
        y2: 20
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 27.33,
        y1: 15,
        x2: 22,
        y2: 20
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 22.33,
        y1: 19.41,
        x2: 22.33,
        y2: 25
      }
    },
    {
      tagName: "polygon",
      groupSelector: "triangle",
      attrs: {
        points: calculateTriangle(17.67, 37, 9.33, 7.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 22.33,
        y1: 6,
        x2: 22.33,
        y2: 0
      }
    },
    {
      tagName: "path",
      groupSelector: "arc",
      attrs: {
        d: calculateArc(0, 4.67, 34.33, 34.33, 0.46, 71.9, 0)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    },
    triangle: {
      fill: "transparent",
      stroke: "#000"
    },
    arc: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
