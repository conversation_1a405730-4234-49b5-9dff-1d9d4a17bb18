<template>
  <div class="custom-file-selector">
    <el-dialog v-model="dialogVisible" width="60%" :before-close="handleClose" draggable>
      <template #title>
        <div class="dialog-title">
          <el-icon class="dialog-title-icon">
            <Folder />
          </el-icon>
          <span class="dialog-title-text">{{ t("common.customFileSelector.title") }}</span>
        </div>
      </template>
      <div class="selector-container">
        <!-- 文件系统树形结构 -->
        <div class="file-tree-container">
          <div class="quick-folders" style="display: flex; gap: 8px; margin-bottom: 8px">
            <el-tooltip content="磁盘根目录" placement="bottom">
              <el-button size="small" type="info" @click="handleGoRoot" circle>
                <el-icon>
                  <Folder />
                </el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip v-for="folder in quickFolders" :key="folder.path" :content="folder.name" placement="bottom">
              <el-button size="small" @click="jumpToQuickFolder(folder)" circle :type="activeQuickFolderPath === folder.path ? 'primary' : 'default'">
                <el-icon>
                  <component :is="getFolderIcon(folder.key)" />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
          <div class="tree-header">
            <el-input v-model="searchKeyword" :placeholder="t('common.customFileSelector.searchPlaceholder')" clearable @input="handleSearch">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <div class="tree-content">
            <div v-if="loading" class="loading-state">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              {{ t("common.customFileSelector.loading") }}
            </div>
            <div v-else-if="errorMessage" class="error-state">
              <el-icon>
                <Warning />
              </el-icon>
              {{ errorMessage }}
            </div>
            <el-tree
              v-else
              ref="fileTreeRef"
              :data="fileTreeData"
              :props="treeProps"
              :load="loadNode"
              lazy
              node-key="path"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              @check="handleNodeCheck"
              show-checkbox
              check-strictly
            >
              <template #default="{ node, data }">
                <div class="tree-node" style="min-width: max-content">
                  <el-icon v-if="data.isDirectory" class="folder-icon">
                    <FolderOpened />
                  </el-icon>
                  <el-icon v-else class="file-icon">
                    <Document />
                  </el-icon>
                  <el-tooltip :content="node.label" placement="top" effect="dark" :show-after="500" :hide-after="0">
                    <span class="node-label">{{ node.label }}</span>
                  </el-tooltip>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 已选择的项目 -->
        <div class="selected-items-container">
          <div class="selected-header">
            <h4>{{ t("common.customFileSelector.selectedItems") }}</h4>
            <el-button type="danger" size="small" @click="clearSelected" :disabled="selectedItems.length === 0">
              {{ t("common.customFileSelector.clearAll") }}
            </el-button>
          </div>
          <div class="selected-content">
            <div v-for="item in selectedItems" :key="item.path" class="selected-item">
              <el-icon v-if="item.isDirectory" class="folder-icon">
                <FolderOpened />
              </el-icon>
              <el-icon v-else class="file-icon">
                <Document />
              </el-icon>
              <span class="item-name">{{ item.name }}</span>
              <span class="item-path">{{ item.path }}</span>
              <span class="item-size">{{ item.size ? (item.size / 1024 / 1024).toFixed(2) + " MB" : "" }}</span>
              <el-button type="danger" size="small" circle @click="removeSelectedItem(item)">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
            <div v-if="selectedItems.length === 0" class="empty-state">
              {{ t("common.customFileSelector.noItemsSelected") }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            {{ t("common.customFileSelector.cancel") }}
          </el-button>
          <el-button type="primary" @click="handleConfirm" :disabled="selectedItems.length === 0">
            {{ t("common.customFileSelector.confirm") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import {
  Search,
  Folder,
  FolderOpened,
  Document,
  Close,
  Loading,
  Warning,
  Download,
  Picture,
  VideoCamera,
  Headset,
  Monitor
} from "@element-plus/icons-vue";
import { osControlApi } from "@/api/modules/biz/os";

interface FileItem {
  path: string;
  name: string;
  isDirectory: boolean;
  size?: number;
  modifiedAt?: Date;
}

interface TreeNode {
  path: string;
  name: string;
  isDirectory: boolean;
  children?: TreeNode[];
  leaf?: boolean;
  modifiedAt?: Date;
  createdAt?: Date;
  accessedAt?: Date;
  size?: number;
}

const { t } = useI18n();

// 响应式数据
const dialogVisible = ref(false);
const searchKeyword = ref("");
const fileTreeRef = ref();
const selectedItems = ref<FileItem[]>([]);
const fileTreeData = ref<TreeNode[]>([]);
const loading = ref(false);
const errorMessage = ref("");

const quickFolders = ref<any[]>([]);
const activeQuickFolderPath = ref<string>("");

watch(quickFolders, val => {
  console.log("quickFolders:", val);
});

// 在 <script setup> 区域添加 handleGoRoot 方法
const handleGoRoot = () => {
  loadRootDirectories();
  activeQuickFolderPath.value = "";
};

// 快捷方式图标映射
const folderIconMap: Record<string, any> = {
  desktop: Monitor,
  documents: Document,
  downloads: Download,
  music: Headset,
  pictures: Picture,
  videos: VideoCamera
};
const getFolderIcon = (key: string) => {
  return folderIconMap[key?.toLowerCase?.()] || Folder;
};

// 树形组件配置
const treeProps = {
  children: "children",
  label: "name",
  isLeaf: "leaf"
};

// 打开对话框
const open = () => {
  dialogVisible.value = true;
  errorMessage.value = "";
  loadQuickFolders();
  loadRootDirectories();
};

// 加载系统常用文件夹
const loadQuickFolders = async () => {
  try {
    const res = await osControlApi.getSystemSpecialFolders();
    // 兼容 res 直接为数组或为 { data: [...] }
    if (Array.isArray(res)) {
      quickFolders.value = res;
    } else if (Array.isArray(res.data)) {
      quickFolders.value = res.data;
    } else {
      quickFolders.value = [];
    }
    console.log("getSystemSpecialFolders 返回：", res);
  } catch (e) {
    quickFolders.value = [];
  }
};

// 快捷跳转到常用文件夹
const jumpToQuickFolder = async (folder: any) => {
  loading.value = true;
  errorMessage.value = "";
  activeQuickFolderPath.value = folder.path;
  try {
    const children = await getDirectoryContents(folder.path);
    fileTreeData.value = children;
  } catch (e) {
    errorMessage.value = t("common.customFileSelector.error.loadFailed");
  } finally {
    loading.value = false;
  }
};

// 加载根目录
const loadRootDirectories = async () => {
  loading.value = true;
  errorMessage.value = "";

  try {
    const rootDirs = await osControlApi.getRootDirectories();
    fileTreeData.value = rootDirs.data || rootDirs;
  } catch (error) {
    console.error("Failed to load root directories:", error);
    errorMessage.value = t("common.customFileSelector.error.loadFailed");
    // 如果API失败，使用默认值
    fileTreeData.value = [
      { path: "C:\\", name: "C:", isDirectory: true, leaf: false },
      { path: "D:\\", name: "D:", isDirectory: true, leaf: false },
      { path: "E:\\", name: "E:", isDirectory: true, leaf: false }
    ];
  } finally {
    loading.value = false;
  }
};

// 加载子节点
const loadNode = async (node: any, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    resolve(fileTreeData.value);
    return;
  }

  try {
    const path = node.data.path;
    const children = await getDirectoryContents(path);
    resolve(children);
  } catch (error) {
    console.error("Failed to load node:", error);
    // 返回空数组而不是抛出错误，让树形组件继续工作
    resolve([]);
  }
};

// 获取目录内容
const getDirectoryContents = async (dirPath: string): Promise<TreeNode[]> => {
  try {
    const contents = await osControlApi.getDirectoryContents({ path: dirPath });
    const data = contents.data || contents;
    return data.map((item: any) => ({
      path: item.path,
      name: item.name,
      isDirectory: item.isDirectory,
      leaf: item.leaf,
      modifiedAt: item.modifiedAt ? new Date(item.modifiedAt) : undefined,
      createdAt: item.createdAt ? new Date(item.createdAt) : undefined,
      accessedAt: item.accessedAt ? new Date(item.accessedAt) : undefined,
      size: item.size || 0
    }));
  } catch (error) {
    console.error("Failed to get directory contents:", error);
    // 返回空数组而不是抛出错误
    return [];
  }
};

// 节点点击事件只保留 lastSelectedNode 记录
const handleNodeClick = () => {
  console.log("handleNodeClick");
};

// 节点选择事件
let lastCheckedNode: TreeNode | null = null;

const handleNodeCheck = async (data: TreeNode, checkedInfo: any) => {
  // 支持 Shift 区间选择（同一级目录下）
  const evt = window.event as MouseEvent;
  if (evt && typeof evt.shiftKey === "boolean" && evt.shiftKey && lastCheckedNode && fileTreeRef.value) {
    const node = fileTreeRef.value.getNode(data.path);
    const lastNode = fileTreeRef.value.getNode(lastCheckedNode.path);
    if (node && lastNode && node.parent === lastNode.parent) {
      const siblings = node.parent.childNodes.map((n: any) => n.data);
      const startIdx = siblings.findIndex((item: any) => item.path === lastCheckedNode!.path);
      const endIdx = siblings.findIndex((item: any) => item.path === data.path);
      if (startIdx !== -1 && endIdx !== -1) {
        const [from, to] = [startIdx, endIdx].sort((a, b) => a - b);
        const range = siblings.slice(from, to + 1);
        // 区间内所有节点都勾选
        range.forEach((item: any) => {
          fileTreeRef.value.setChecked(item.path, true);
        });
      }
    } else {
      if (typeof window !== "undefined") {
        window.alert("仅支持同一目录下的区间选择，请先展开需要选择的目录");
      }
    }
  }
  lastCheckedNode = data;

  // 获取所有已勾选节点，包含区间内新勾选的
  let checkedNodes: TreeNode[] = [];
  if (fileTreeRef.value) {
    checkedNodes = fileTreeRef.value.getCheckedNodes();
  } else {
    checkedNodes = checkedInfo.checkedNodes;
  }

  // 去重处理
  const uniqueNodes = Array.from(new Map(checkedNodes.map(node => [node.path, node])).values());

  // 获取 size 信息并保留时间信息
  const sizePromises = uniqueNodes.map(async (node: TreeNode) => {
    const res = await osControlApi.getFileOrFolderSize({ path: node.path });
    return {
      path: node.path,
      name: node.name,
      isDirectory: node.isDirectory,
      size: res.size || node.size || 0,
      modifiedAt: node.modifiedAt || new Date()
    };
  });
  selectedItems.value = await Promise.all(sizePromises);
};

// 搜索功能
const handleSearch = () => {
  if (fileTreeRef.value) {
    fileTreeRef.value.filter(searchKeyword.value);
  }
};

// 过滤节点
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 移除选中的项目
const removeSelectedItem = (item: FileItem) => {
  const index = selectedItems.value.findIndex(i => i.path === item.path);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
    // 同时取消树形组件中的选中状态
    if (fileTreeRef.value) {
      fileTreeRef.value.setChecked(item.path, false);
    }
  }
};

// 清空所有选中项目
const clearSelected = () => {
  selectedItems.value = [];
  if (fileTreeRef.value) {
    fileTreeRef.value.setCheckedKeys([]);
  }
};

// 确认选择
const handleConfirm = () => {
  const result = selectedItems.value.length === 1 ? selectedItems.value[0] : selectedItems.value;
  emit("confirm", result);
  handleClose();
};

// 取消选择
const handleCancel = () => {
  emit("cancel");
  handleClose();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  selectedItems.value = [];
  searchKeyword.value = "";
  emit("close");
};

// 事件定义
const emit = defineEmits<{
  confirm: [items: FileItem | FileItem[]];
  cancel: [];
  close: [];
}>();

// 暴露方法
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.custom-file-selector {
  .dialog-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    .dialog-title-icon {
      margin-right: 8px;
      font-size: 22px;
      color: #409eff;
    }
    .dialog-title-text {
      flex: 1;
    }
  }
  .selector-container {
    display: flex;
    gap: 24px;
    height: 420px;
    padding: 16px 12px;
    background: #f8fafd;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  }
  .file-tree-container {
    display: flex;
    flex-basis: 32%;
    flex-direction: column;
    min-width: 220px;
    max-width: 320px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgb(64 158 255 / 4%);
    .quick-folders {
      padding: 8px 12px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
    }
    .tree-header {
      padding: 10px 12px 6px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
    }
    .tree-content {
      flex: 1;
      padding: 10px 12px;
      overflow-x: auto;
      overflow-y: auto;
      // 关键：让 el-tree 及其内容宽度自适应
      .el-tree {
        min-width: max-content;
        width: auto !important;
      }
      .el-tree-node__content {
        min-width: max-content;
        width: auto !important;
      }
      .loading-state,
      .error-state {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #909399;
        .is-loading {
          animation: rotating 2s linear infinite;
        }
      }
      .error-state {
        color: #f56c6c;
      }
    }
  }
  .selected-items-container {
    display: flex;
    flex-basis: 68%;
    flex-direction: column;
    min-width: 320px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgb(64 158 255 / 4%);
    .selected-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      background: #f7f9fa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .selected-content {
      flex: 1;
      padding: 12px 16px;
      overflow: auto;
      .selected-item {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 10px 8px;
        margin-bottom: 10px;
        background: #f9fafc;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        transition: box-shadow 0.2s;
        &:hover {
          box-shadow: 0 2px 8px rgb(64 158 255 / 8%);
        }
        .item-name {
          flex: 1;
          font-weight: 500;
        }
        .item-path {
          flex: 2;
          overflow: hidden;
          font-size: 12px;
          color: #909399;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-size {
          flex: 1;
          font-size: 12px;
          color: #67c23a;
          text-align: right;
        }
      }
      .empty-state {
        padding: 40px 0;
        color: #909399;
        text-align: center;
      }
    }
  }
  .tree-node {
    display: flex;
    gap: 8px;
    align-items: center;
    .folder-icon {
      color: #e6a23c;
    }
    .file-icon {
      color: #409eff;
    }
    .node-label {
      flex: 1;
    }
  }
  .dialog-footer {
    padding: 8px 0 0;
    text-align: right;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
