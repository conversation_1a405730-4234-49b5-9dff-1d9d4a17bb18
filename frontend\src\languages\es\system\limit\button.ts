export default {
  limit: {
    button: {
      title: "Gestión de Botones",
      buttonList: "Lista de Botones",
      addButton: "Agregar Bot<PERSON>",
      batchAdd: "Agregar en Lote",
      deleteSelected: "Eliminar Seleccionados",
      confirm: "Confirmar",
      name: "Nombre del Botón",
      code: "Código del Botón",
      sort: "Índice",
      description: "Descripción",
      operation: "Operación",
      form: {
        add: "Agregar Bo<PERSON>",
        edit: "Editar Botón",
        view: "Ver Botón",
        title: "Nombre del Botón",
        code: "Código del Botón",
        sort: "Índice",
        description: "Descripción",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          title: "Por favor ingrese el nombre del botón",
          code: "Por favor ingrese el código del botón",
          sort: "Por favor ingrese el índice"
        }
      },
      batch: {
        title: "Agregar Botones en Lote",
        module: "<PERSON><PERSON><PERSON><PERSON>",
        buttons: "Lista de Botones",
        add: "Agregar",
        delete: "Eliminar",
        cancel: "Cancelar",
        confirm: "Confirmar",
        validation: {
          module: "Por favor seleccione el módulo perteneciente",
          buttons: "Por favor agregue botones"
        }
      }
    }
  }
};
