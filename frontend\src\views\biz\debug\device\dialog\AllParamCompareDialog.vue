<template>
  <el-dialog
    v-model="visible"
    custom-class="resizable-dialog"
    :style="{
      width: dialogWidth + 'px'
    }"
    :close-on-click-modal="false"
    :title="t('device.allParamCompare.title')"
    :fullscreen="false"
    max-width="80%"
    max-height="80vh"
  >
    <div class="drag-handle" @mousedown="startResize"></div>

    <div class="main-box">
      <TreeFilter
        label="grpname"
        :multiple="false"
        style="height: 100%"
        title="定值组"
        :icon="ArrowRightBold"
        :data="treeFilterData"
        :request-api="getInitData"
        :default-value="initParam.grpName"
        @change="changeTreeFilter"
      />

      <div class="table-box">
        <ProTable
          :columns="columns"
          ref="proTable"
          :init-param="initParam"
          @search="getData"
          :pagination="false"
          max-height="500"
          highlight-current-row
          :data="diffTabledata"
          :request-auto="false"
          @select-all="selectionChange"
          @select="handleSelect"
          :data-callback="dataCallback"
          row-key="name"
        >
          <!-- Expand -->
          <template #expand="scope">
            {{ scope.row }}
          </template>
        </ProTable>
      </div>
    </div>
    <template #header>
      <div class="dialog-header">
        <el-icon><ChromeFilled /></el-icon>
        <span>{{ t("device.allParamCompare.title") }}</span>
      </div>
    </template>
    <template #footer>
      <el-button @click="handleCancel">{{ t("device.allParamCompare.cancel") }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ t("device.allParamCompare.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, onMounted, computed } from "vue";
import { ChromeFilled } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { DiffData, DiffItem, GroupInfo } from "@/api/interface/biz/debug/diffitem";
import { ArrowRightBold } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const proTable = ref<ProTableInstance>();
const diffTabledata = ref<DiffItem[]>([]);

const dialogWidth = ref(1100); // 初始宽度
const dialogHeight = ref(800); // 初始高度
const tableHeight = ref(500);
let isResizing = false;
let startX = 0;
let startY = 0;

const startResize = e => {
  isResizing = true;
  startX = e.clientX;
  startY = e.clientY;
  document.addEventListener("mousemove", onResize);
  document.addEventListener("mouseup", stopResize);
};
const onResize = e => {
  if (!isResizing) return;
  const deltaX = e.clientX - startX;
  const deltaY = e.clientY - startY;
  dialogWidth.value += deltaX;
  dialogHeight.value += deltaY;
  tableHeight.value += deltaY - 100;
  startX = e.clientX;
  startY = e.clientY;
};

const stopResize = () => {
  isResizing = false;
  document.removeEventListener("mousemove", onResize);
  document.removeEventListener("mouseup", stopResize);
};
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)

const treeFilterData = ref<GroupInfo[]>([]);

const getTreeFilter = async () => {
  let resultValue = toRaw(props.diffdata);
  resultValue.forEach(item => {
    treeFilterData.value.push({
      id: item.grpname,
      grpname: item.grpname
    });
  });
  initParam.grpName = "";
};
const getInitData = async () => {
  return { data: [] };
};
// 树形筛选切换
const changeTreeFilter = (val: string) => {
  console.log(val);
  proTable.value!.pageable.pageNum = 1;
  initParam.grpName = val;
  getData();
};

// 接收父组件传递的 props
const props = defineProps<{
  visible: boolean;
  diffdata: DiffData;
}>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ grpName: "" });

const columns = reactive<ColumnProps[]>([
  { type: "selection", fixed: "left", width: 50 },
  {
    type: "index",
    label: t("device.allParamCompare.sequence"),
    width: 60
  },
  {
    prop: "grpName",
    label: t("device.allParamCompare.groupName")
  },
  {
    prop: "name",
    label: t("device.allParamCompare.name"),
    search: {
      el: "input",
      tooltip: t("device.allParamCompare.searchName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "description",
    label: t("device.allParamCompare.description"),
    search: {
      el: "input",
      tooltip: t("device.allParamCompare.searchDescription"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "minValue",
    label: t("device.allParamCompare.minValue")
  },
  {
    prop: "maxValue",
    label: t("device.allParamCompare.maxValue")
  },
  {
    prop: "step",
    label: t("device.allParamCompare.step")
  },
  {
    prop: "unit",
    label: t("device.allParamCompare.unit"),
    isShow: false
  },
  {
    prop: "inf",
    label: t("device.allParamCompare.address"),
    isShow: false
  },
  {
    prop: "oldValue",
    label: t("device.allParamCompare.oldValue"),
    render: scope => {
      return (
        <div>
          <el-text type="primary">{scope.row.oldValue}</el-text>
        </div>
      );
    }
  },
  {
    prop: "newValue",
    label: t("device.allParamCompare.newValue"),
    render: scope => {
      return (
        <div>
          <el-text type="danger">{scope.row.newValue}</el-text>
        </div>
      );
    }
  }
]);
async function getData() {
  console.log("getData");
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  console.log(newParams);
  let resultValue = toRaw(props.diffdata);
  let resultData: DiffItem[] = [];
  const grpName = initParam.grpName;
  if (grpName !== "") {
    resultValue = resultValue.filter(item => {
      return item.grpname === grpName;
    });
  }
  resultValue.forEach(item => {
    resultData.push(...item.data);
  });
  if (newParams.name) {
    resultData = resultData?.filter(data => {
      return data.name.toLowerCase().includes(newParams.name.toLowerCase());
    });
    console.log(resultData);
  }
  if (newParams.description) {
    resultData = resultData?.filter(data => {
      return data.description.toLowerCase().includes(newParams.description.toLowerCase());
    });
  }
  diffTabledata.value = resultData;
  proTable.value?.refresh();
  nextTick(() => {
    proTable.value?.tableData.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.name));
    });
  });
}
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

onMounted(() => {
  console.log("diffdata", props.diffdata);
  nextTick(() => {
    getTreeFilter();
    getData();
    proTable.value?.element?.toggleAllSelection();
  });
});

// 定义事件
const emit = defineEmits(["confirm", "cancel", "update:visible"]);

// 使用计算属性来实现双向绑定
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit("update:visible", value);
  }
});
let selectIds: string[] = [];
const selectionChange = isSelectAll => {
  console.log("selectionChange", isSelectAll);
  if (isSelectAll.length > 0) {
    isSelectAll.forEach(row => {
      selectIds = [...new Set([...selectIds, row.name])];
    });
  } else {
    proTable.value?.tableData.forEach(row => {
      selectIds = selectIds.filter(name => name !== row.name);
    });
  }
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.name === row.name);
    if (isSelected) {
      selectIds = [...new Set([...selectIds, row.name])];
    } else {
      selectIds = selectIds.filter(name => name !== row.name);
    }
  });
};
// 处理确认导入
const handleConfirm = () => {
  if (selectIds.length == 0) {
    ElMessageBox.alert(t("device.allParamCompare.messages.noSelection"), t("device.allParamCompare.messages.error"), {
      type: "error"
    });
    return;
  }

  let resultValue = toRaw(props.diffdata);
  let resultData: DiffItem[] = [];
  resultValue.forEach(item => {
    resultData.push(...item.data);
  });

  resultData = resultData.filter(item => {
    return selectIds.includes(item.name);
  });

  emit("confirm", resultData);
  // 通过emit通知父组件关闭对话框
  emit("update:visible", false);
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  // 通过emit通知父组件关闭对话框
  emit("update:visible", false);
};
</script>

<style scoped>
.old-value {
  color: #f56c6c;
  text-decoration: line-through;
}
.new-value {
  font-weight: bold;
  color: #67c23a;
}
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}
:deep(.resizable-dialog) {
  display: flex;
  flex-direction: column;
  min-width: 400px;
  min-height: 200px;
  overflow: auto;
  resize: both;
}
.content {
  flex: 1;
  overflow: auto;
}
.drag-handle {
  /* 覆盖 CSS resize 的默认拖拽柄 */
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2000; /* 确保在最上层 */
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}
</style>
