import { ReqId, ResPage, SysOrg } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/organization/org/");

const sysOrgApi = {
  /** 获取组织分页 */
  page(params: SysOrg.Page) {
    return http.get<ResPage<SysOrg.SysOrgInfo>>("page", params);
  },
  /** 获取组织树 */
  tree() {
    return http.get<SysOrg.SysOrgTree[]>("tree");
  },
  /** 获取组织详情 */
  detail(params: ReqId) {
    return http.get<SysOrg.SysOrgInfo>("detail", params);
  },
  /**  提交表单 edit为true时为编辑，默认为新增 */
  submitForm(params = {}, edit: boolean = false) {
    return http.post(edit ? "edit" : "add", params);
  },
  /** 删除组织 */
  delete(params: ReqId[]) {
    return http.post("delete", params);
  },
  /** 复制组织 */
  copy(params = {}) {
    return http.post("copy", params);
  }
};

export { sysOrgApi };
