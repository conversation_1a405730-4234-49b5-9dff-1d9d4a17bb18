<template>
  <div class="layout">
    <div id="content" class="container">
      <div style="width: 100%; height: 100%">
        <div id="canvas" ref="canvas" style="width: 100%; height: 100%"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: "GraphView"
};
</script>
<script setup lang="ts">
import { Graph } from "@antv/x6";
import { Selection } from "@antv/x6-plugin-selection";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import GraphEvent from "../../../graph/GraphEvent";
import {
  CbrDis,
  ContextMenuItem,
  ContextMenuItemType,
  EquipmentData,
  EquipmentManagerName,
  EventTypeParams,
  GraphViewData,
  RenderData,
  SetSaddrValue
} from "../../../graph/Graph";
import { provide } from "vue";
import EquipmentManager from "../../../equipment/EquipmentManager";

import GraphContextMenuManager from "./GraphContextMenuManager";
import GrapgGroup from "../../../graph/GraphGroup";
import { CommonPlugin, GraphFactory, initGraphData } from "../../../graph";
import { GraphRenderFactory } from "../../../graph/GraphRenderFactory";

let graph: Graph;
let graphViewData: GraphViewData | undefined;
// 定义事件
const emit = defineEmits<{
  (e: "renderData", data: RenderData): void;
  (e: "triggerClick", data: EventTypeParams): void;
  (e: "triggerContextMenu", data: unknown): void;
}>();
// 定义方法
defineExpose({
  setGraphData: (data: GraphViewData) => {
    graphViewData = data;
    initData(data);
  },
  setSaddrValue: (data: SetSaddrValue[]) => {
    setSaddrValue(data);
  },
  unselect: () => {
    unselect();
  }
});
const canvas = ref();
// 表单数据
const form = ref<{
  electricDatas: EquipmentData[];
  renderData?: RenderData;
}>({
  electricDatas: []
});
// 电气符号
const equipmentManager = new EquipmentManager();
provide(EquipmentManagerName, equipmentManager);
// 右键
const contextMenuManager = new GraphContextMenuManager();
const graphRenderFactory = new GraphRenderFactory();

const { t } = useI18n();

const registerElectricNode = async () => {
  try {
    await equipmentManager.loadEquipment();
    equipmentManager.registerNode();
  } catch (e) {
    console.log(t("graph.messages.loadEquipmentError"), e);
  } finally {
    form.value.electricDatas = equipmentManager.equipmentList;
  }
};
onMounted(async () => {
  // 加载图符
  await registerElectricNode();
  graph = new Graph({
    container: canvas.value as HTMLElement,
    grid: {
      visible: true,
      size: 10,
      type: "mesh"
    },
    background: {
      color: "#fff"
    },
    interacting: false,
    translating: {
      restrict: true
    },
    autoResize: true
  });
  // 处理事件
  new GraphEvent(
    graph,
    {
      create: contextMenuManager.createContextMenu,
      trigger: contextMenuEvent
    },
    emitEvent
  );
  // 插件
  graph.use(
    new Selection({
      enabled: true,
      movable: false,
      showNodeSelectionBox: true,
      showEdgeSelectionBox: false,
      rubberband: true,
      strict: true,
      pointerEvents: "none"
    })
  );
  graph.use(
    new CommonPlugin({
      enabled: true,
      data: {}
    })
  );
});

const initData = (data: GraphViewData) => {
  initGraphData(graph, data.graphData);
  // 自定义数据包括默认的电器符合和自定义的
  let equipmentDatas: EquipmentData[] = [];
  if (equipmentManager.equipmentList) {
    equipmentDatas = equipmentDatas.concat(equipmentManager.equipmentList);
  }
  if (data.equipmentDatas) {
    equipmentDatas = equipmentDatas.concat(data.equipmentDatas);
  }
  // 渲染数据
  const factory = new GraphFactory();
  form.value.renderData = factory.render(graph, equipmentDatas);
  emit("renderData", form.value.renderData as RenderData);
};

const emitEvent = (args: EventTypeParams) => {
  emit("triggerClick", args);
};
const contextMenuEvent = (item: ContextMenuItem, graph: Graph, graphGroup: GrapgGroup) => {
  switch (item.type) {
    case ContextMenuItemType.EQUIPMENT_SADDR:
      break;
    default:
      contextMenuManager.trigger(item, graph, graphGroup);
      break;
  }
};
const setSaddrValue = (data: SetSaddrValue[]) => {
  if (form.value.renderData) {
    graphRenderFactory.setSaddrValue(data, form.value.renderData as RenderData, graphViewData?.cbrDis as CbrDis);
  }
};

const unselect = () => {
  const cells = graph.getSelectedCells();
  if (cells && cells.length > 0) {
    graph.unselect(cells);
  }
};
</script>
<style>
@import "../../../styles/Graph.css";
.layout {
  height: 100%;
}
.container {
  height: 100%;
  overflow: auto;
}
.canvas {
  width: 100%;
  height: 100%;
}
</style>
./GraphEvent
