import { logger } from "ee-core/log";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import {
  GraphControlRequest,
  GraphGetDataRequest,
  GraphRegisterInfo,
  GraphRegisterRequest,
  GraphRegisterResponse,
} from "../../interface/hmi/graph";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { DebugInfoItem } from "../../interface/debug/debuginfo";
import {
  CtlMode,
  CtlResult,
  DataValueReadRequestData,
} from "iec-upadrpc/dist/src/data";
import { IECResult } from "iec-common/dist/data/iecdata";

/**
 * 服务提供
 * <AUTHOR>
 * @version 1.0
 */
class GraphViewService {
  registerMap: Map<string, GraphRegisterInfo>;
  constructor() {
    this.registerMap = new Map();
  }

  /**
   * 注册图符数据
   * @returns
   */
  async register(data: GraphRegisterRequest): Promise<ApiResponse> {
    logger.info(
      "[GraphViewService] register - deviceId is %s hmiId is %s",
      data.deviceId,
      data.hmiId
    );
    // 查找设备配置信息
    const deviceData = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      data.deviceId
    );
    if (!deviceData) {
      logger.error(
        "[GraphViewService] register - device not found, deviceId is ",
        data.deviceId
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        ERROR_MESSAGES.DEVICE_NOT_CONNECTED
      );
    }
    const deviceItemMap = deviceData.debugItemObjMap;
    // 查找短地址对应的数据项
    let deviceItem: DebugInfoItem | undefined;
    const notFoundtList: string[] = [];
    const vkeys: string[] = [];
    const saddrMap: Map<string, DebugInfoItem> = new Map();
    const vkeyMap: Map<string, DebugInfoItem> = new Map();
    for (const saddr of data.saddrs) {
      if (saddr.trim().length == 0) {
        continue;
      }
      deviceItem = deviceItemMap.get(saddr);
      if (deviceItem && deviceItem.vKey) {
        vkeys.push(deviceItem.vKey);
        saddrMap.set(saddr, deviceItem);
        vkeyMap.set(deviceItem.vKey, deviceItem);
      } else {
        notFoundtList.push(saddr);
      }
    }
    // 生成注册信息
    const registerId = data.deviceId + "_" + data.hmiId;
    const registerInfo: GraphRegisterInfo = {
      saddrMap: saddrMap,
      vkeyMap: vkeyMap,
      vkeys: vkeys,
    };
    this.registerMap.delete(registerId);
    this.registerMap.set(registerId, registerInfo);
    const response: GraphRegisterResponse = {
      registerId: registerId,
      notFoundSaddrs: notFoundtList,
    };
    logger.info("[GraphViewService] register - registerId is ", registerId);
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      response
    );
  }
  async getData(requestData: GraphGetDataRequest): Promise<ApiResponse> {
    logger.debug(
      "[GraphViewService] getData - registerId is ",
      requestData.registerId
    );
    const client = GlobalDeviceData.getInstance().getClient(
      requestData.deviceId
    );
    if (!client || !client.isConnected()) {
      return handleCustomResponse(
        ERROR_CODES.DEVICE_NOT_CONNECTED,
        ERROR_MESSAGES.DEVICE_NOT_CONNECTED
      );
    }
    const graphRegisterInfo = this.registerMap.get(requestData.registerId);
    if (!graphRegisterInfo) {
      return handleCustomResponse(10, "请先注册数据");
    }
    const requestOptions: DataValueReadRequestData = {
      infItems: graphRegisterInfo.vkeys,
    };
    const result = await client.getDataValues(requestOptions);
    if (!result.isSuccess()) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备接口获取数据失败"
      );
    }
    // 封装数据格式
    const resultData = result.data;
    const msg = this.checkServiceError(resultData, requestData.deviceId);
    if (msg != undefined) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备接口获取数据失败" + msg
      );
    }
    const dataValues: {
      saddr: string;
      value: string;
      error?: string;
    }[] = [];
    let deviceItem: DebugInfoItem | undefined;
    for (const item of resultData.value) {
      deviceItem = graphRegisterInfo.vkeyMap.get(item.vkey);
      if (deviceItem) {
        dataValues.push({
          saddr: deviceItem.name,
          value: String(item.value),
          error: item.error,
        });
      }
    }
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      dataValues
    );
  }

  async remoteControl(requestData: GraphControlRequest): Promise<ApiResponse> {
    logger.info("[GraphViewService] remoteControl - data is ", requestData);
    const client = GlobalDeviceData.getInstance().getClient(
      requestData.deviceId
    );
    if (!client || !client.isConnected()) {
      return handleCustomResponse(
        ERROR_CODES.DEVICE_NOT_CONNECTED,
        ERROR_MESSAGES.DEVICE_NOT_CONNECTED
      );
    }
    // 判断直控和选控
    let result: IECResult<CtlResult>;
    if (requestData.data.ctlModel == CtlMode.DIRECT) {
      result = await client.selectConfirm(requestData.data);
    } else {
      result = await client.select(requestData.data, true);
    }
    if (!result.isSuccess()) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥控接口失败"
      );
    }
    const resultData = result.data;
    const msg = this.checkServiceError(resultData, requestData.deviceId);
    if (msg != undefined) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥控接口失败ServiceError" + msg
      );
    }
    // 检查addCause
    if (!resultData.success) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥控接口失败AddCause" + resultData.addCauseDesc
      );
    }
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      true
    );
  }

  async remoteSet(requestData: GraphControlRequest): Promise<ApiResponse> {
    logger.info("[GraphViewService] remoteSet - data is ", requestData);
    const client = GlobalDeviceData.getInstance().getClient(
      requestData.deviceId
    );
    if (!client || !client.isConnected()) {
      return handleCustomResponse(
        ERROR_CODES.DEVICE_NOT_CONNECTED,
        ERROR_MESSAGES.DEVICE_NOT_CONNECTED
      );
    }
    // 判断直控和选控
    let result: IECResult<CtlResult>;
    if (requestData.data.ctlModel == CtlMode.DIRECT) {
      result = await client.selectConfirm(requestData.data);
    } else {
      result = await client.select(requestData.data, true);
    }
    if (!result.isSuccess()) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥设接口失败"
      );
    }
    const resultData = result.data;
    const msg = this.checkServiceError(resultData, requestData.deviceId);
    if (msg != undefined) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥设接口失败ServiceError" + msg
      );
    }
    // 检查addCause
    if (!resultData.success) {
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        "调用设备遥设接口失败AddCause" + resultData.addCauseDesc
      );
    }
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      true
    );
  }

  checkServiceError(
    data: { ServiceError: number },
    deviceId: string
  ): string | undefined {
    // 封装数据格式
    const resultData = data;
    if (resultData.ServiceError != undefined && resultData.ServiceError != 0) {
      const deviceMap =
        GlobalDeviceData.getInstance().deviceInfoMap.get(deviceId);
      let msg: string | undefined;
      if (deviceMap) {
        msg = deviceMap.getServiceErrMsgByCode(resultData.ServiceError + "");
      }
      if (msg == undefined) {
        msg = "错误码" + resultData.ServiceError;
      }
      return msg;
    }
    return undefined;
  }
}

GraphViewService.toString = () => "[class GraphViewService]";
const graphViewService = new GraphViewService();
export { GraphViewService, graphViewService };
