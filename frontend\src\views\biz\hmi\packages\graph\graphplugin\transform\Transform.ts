import { Dom, Graph, Node } from "@antv/x6";
import { isLine } from "../../GraphUtil";

/**
 * 扩展变换插件
 * <AUTHOR>
 * @version 1.0 2025-03-24
 */
export class TransformImpl {
  public view: any;
  public container: any;
  private options: any;
  private node: Node;
  private graph: Graph;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(options: any, node: Node, graph: Graph) {
    this.options = options;
    this.node = node;
    this.graph = graph;

    // 初始化视图和容器
    this.initializeTransform();
  }

  private initializeTransform() {
    // 获取节点视图
    this.view = this.graph.findViewByCell(this.node);

    // 创建容器元素
    this.container = document.createElement("div");
    this.container.className = "x6-widget-transform";

    // 将容器添加到图形容器中
    if (this.graph.container) {
      this.graph.container.appendChild(this.container);
    }
  }

  // 渲染节点方法
  renderHandles() {
    const view = this.view;
    if (view) {
      const node = view.cell as Node;
      // 判断是否是线
      if (isLine(node)) {
        // 线只保留2个节点
        if (this.container) {
          Dom.addClass(this.container, "line");
        }
      }
    }
  }

  // 事件监听方法
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
    return this;
  }

  // 移除事件监听
  off(event?: string, callback?: Function) {
    if (!event) {
      this.eventListeners.clear();
    } else if (!callback) {
      this.eventListeners.delete(event);
    } else {
      const listeners = this.eventListeners.get(event);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    }
    return this;
  }

  // 触发事件
  trigger(event: string, ...args: any[]) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(...args));
    }

    // 支持通配符事件监听
    const wildcardListeners = this.eventListeners.get("*");
    if (wildcardListeners) {
      wildcardListeners.forEach(callback => callback(event, ...args));
    }
    return this;
  }

  // 清理方法
  dispose() {
    this.eventListeners.clear();
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
