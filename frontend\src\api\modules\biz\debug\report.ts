import { moduleIpcRequest } from "@/api/request";
import { ReportParam } from "@/api";

const ipc = moduleIpcRequest("controller/debug/report/");

const reportApi = {
  /** 获取通用报告数据（指定 deviceId） */
  getCommonReportListByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcCommonReportRes>("getCommonReportList", params, deviceId);
  },
  /** 获取整组报告数据（指定 deviceId） */
  getGroupReportListByDevice(deviceId: string, params: ReportParam.IECRpcGroupReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcCommonReportRes>("getGroupReportList", params, deviceId);
  },
  /** 获取操作报告数据（指定 deviceId） */
  getOperateReportListByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcOperateReportRes>("getOperateReportList", params, deviceId);
  },
  /** 获取审计报告（指定 deviceId） */
  getAuditReportListByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcAuditLogReportRes>("getAuditLogList", params, deviceId);
  },
  /** 刷新通用报告数据（指定 deviceId） */
  refreshReportByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcReportRefreshRes>("refreshReport", params, deviceId);
  },
  /** 刷新整组报告数据（指定 deviceId） */
  refreshGroupReportByDevice(deviceId: string, params: ReportParam.IECRpcGroupReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcGroupReportRefreshRes>("refreshGroupReport", params, deviceId);
  },
  /** 刷新动作报告数据（指定 deviceId） */
  refreshTripReportByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcTripReportRefreshRes>("refreshTripReport", params, deviceId);
  },
  /** 刷新操作报告数据（指定 deviceId） */
  refreshOperateReportByDevice(deviceId: string, params: ReportParam.IECRpcReportSearchParam) {
    return ipc.iecInvokeWithDevice<ReportParam.IECRpcOperateReportRefreshRes>("refreshOperateReport", params, deviceId);
  },
  /** 导出通用报告数据（指定 deviceId，本地操作但保持装置上下文） */
  exportCommonReportByDevice(deviceId: string, params: ReportParam.IECRpcCommonReportExportParam) {
    return ipc.iecInvokeWithDevice<any>("exportCommonReport", params, deviceId);
  },
  /** 召唤录波（指定 deviceId） */
  uploadWaveByDevice(deviceId: string, params: string | unknown) {
    return ipc.iecInvokeWithDevice<any>("uploadWave", params, deviceId);
  },
  /** 取消召唤录波（指定 deviceId） */
  cancelUploadByDevice(deviceId: string, params: undefined) {
    return ipc.iecInvokeWithDevice<any>("cancelUpload", params, deviceId);
  },
  /** 打开录波文件（本地操作） */
  openWaveFile(params: ReportParam.IECRpcReportOpenWaveParam) {
    return ipc.invoke<any>("openWaveFile", params);
  }
};

export { reportApi };
