import { defineStore } from "pinia";
import { userCenterApi, UserCenter, SysMessage } from "@/api";
import { ElNotification } from "element-plus";
import { Message } from "@element-plus/icons-vue";

const name = "simple-message"; // 定义模块名称

/* MqttState */
export interface MessageState {
  /** 未读消息信息 */
  unReadInfo: any;
  /** 未读消息数 */
  unReadCount: number;
  /** 新未读消息 */
  newUnRead: SysMessage.SysMessageInfo[];
}

/** Mqtt模块 */
export const useMessageStore = defineStore({
  id: name,
  state: (): MessageState => ({
    unReadInfo: {},
    unReadCount: 0,
    newUnRead: []
  }),
  getters: {
    unReadCountGet: state => state.unReadCount,
    unReadInfoGet: state => state.unReadInfo
  },
  actions: {
    /** 显示更多 */
    setShowMore(state: boolean) {
      this.showMore = state;
    },
    /** 增加未读消息数 */
    unReadCountAdd(value: number) {
      this.unReadCount += value;
    },
    /** 减少未读消息数 */
    unReadCountSubtract(value: number) {
      this.unReadCount -= value;
    },
    /** 设置未读消息数 */
    unReadCountSet(value: number) {
      this.unReadCount = value;
    },
    /** 获取未读消息数 */
    async getUnReadInfo(notice: boolean = false) {
      await userCenterApi.unReadCount().then(res => {
        if (res.data.length > 0) {
          //未读消息信息数量转换
          this.unReadInfo = res.data.reduce((acc, item) => {
            acc[item.category] = item.unReadCount;
            return acc;
          }, {});
          //遍历未读消息信息，获取未读消息总数
          let count = 0;
          res.data.map((item: UserCenter.ResUnReadCount) => {
            count += item.unReadCount;
          });
          //如果未读消息总数大于当前未读消息总数，则获取最新未读消息
          if (count > this.unReadCount) {
            this.getNewMessage(notice);
          }
          this.unReadCountSet(count);
        } else {
          this.unReadCountSet(0);
        }
      });
    },
    /** 获取最新未读消息 */
    async getNewUnRead() {
      await userCenterApi.newUnRead().then(res => {
        if (res.data.length > 0) {
          this.newUnRead = res.data;
        }
      });
    },
    /** 获取未读消息数 */
    getUnReadCount(category: string) {
      return this.unReadInfo[category] || 0;
    },
    /**提示有新消息 */
    getNewMessage(notice: boolean = false, message: string = "您有一条新消息,请注意查收!") {
      this.getNewUnRead();
      if (notice) {
        ElNotification({
          title: "收到一条新消息",
          message: message,
          icon: Message,
          offset: 40
        });
      }
    },
    /**定时刷新最新消息*/
    async getNewMessageInterval() {
      setInterval(() => {
        this.getUnReadInfo(true);
      }, 10000);
    },
    /* 重置未读消息数 */
    reSet() {
      this.unReadCount = 0;
      this.unReadInfo = {};
      this.newUnRead = [];
    }
  }
});
