<template>
  <svg
    t="1742955943373"
    class="icon"
    viewBox="0 0 1097 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="41811"
    :width="props.width"
    :height="props.height"
  >
    <path
      :fill="props.color"
      d="M1024 0a73.142857 73.142857 0 0 1 73.142857 73.142857v505.417143a73.142857 73.142857 0 0 1-73.142857 73.142857h-38.546286a72.777143 72.777143 0 0 1-37.961143-10.678857v216.722286a109.714286 109.714286 0 0 1-109.714285 109.714285H146.285714a109.714286 109.714286 0 0 1-109.714285-109.714285V352.402286a109.714286 109.714286 0 0 1 109.714285-109.714286h116.370286a73.142857 73.142857 0 0 1-3.364571-21.869714V73.142857a73.142857 73.142857 0 0 1 73.142857-73.142857H1024z m-186.148571 315.830857H146.285714a36.571429 36.571429 0 0 0-36.571428 36.571429v505.417143a36.571429 36.571429 0 0 0 36.571428 36.571428h691.565715a36.571429 36.571429 0 0 0 36.571428-36.571428v-505.417143a36.571429 36.571429 0 0 0-36.571428-36.571429z"
      p-id="41812"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { HmiIconProps } from ".";
const props = withDefaults(defineProps<HmiIconProps>(), {
  width: 32,
  height: 32,
  color: "#666666"
});
</script>
