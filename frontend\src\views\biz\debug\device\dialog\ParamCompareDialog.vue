<template>
  <el-dialog
    v-model="visible"
    custom-class="resizable-dialog"
    :style="{
      width: dialogWidth + 'px'
    }"
    :close-on-click-modal="false"
    :title="t('device.paramCompare.title')"
    :fullscreen="false"
    width="60%"
    max-height="80vh"
  >
    <div class="drag-handle" @mousedown="startResize"></div>

    <div class="content">
      <ProTable
        ref="proTable"
        :columns="columns"
        @search="getData"
        highlight-current-row
        max-height="500"
        :init-param="initParam"
        :pagination="false"
        :data="diffTabledata"
        :data-callback="dataCallback"
        @select-all="selectionChange"
        @select="handleSelect"
        row-key="name"
      >
        <!-- Expand -->
        <template #expand="scope">
          {{ scope.row }}
        </template>
      </ProTable>
    </div>
    <template #header>
      <div class="dialog-header">
        <el-icon><ChromeFilled /></el-icon>
        <span>{{ t("device.paramCompare.title") }}</span>
      </div>
    </template>
    <template #footer>
      <el-button @click="handleCancel">{{ t("device.paramCompare.cancel") }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ t("device.paramCompare.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, onMounted, computed } from "vue";
import { ChromeFilled } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { DiffItem } from "@/api/interface/biz/debug/diffitem";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
// ProTable 实例
const proTable = ref<ProTableInstance>();
const dialogWidth = ref(1000); // 初始宽度
const dialogHeight = ref(800); // 初始高度

let isResizing = false;
let startX = 0;
let startY = 0;

const startResize = e => {
  isResizing = true;
  startX = e.clientX;
  startY = e.clientY;
  document.addEventListener("mousemove", onResize);
  document.addEventListener("mouseup", stopResize);
};

const onResize = e => {
  if (!isResizing) return;
  const deltaX = e.clientX - startX;
  const deltaY = e.clientY - startY;
  dialogWidth.value += deltaX;
  dialogHeight.value += deltaY;
  startX = e.clientX;
  startY = e.clientY;
};

const stopResize = () => {
  isResizing = false;
  document.removeEventListener("mousemove", onResize);
  document.removeEventListener("mouseup", stopResize);
};
let selectIds: string[] = [];
const selectionChange = isSelectAll => {
  console.log("selectionChange", isSelectAll);
  if (isSelectAll.length > 0) {
    isSelectAll.forEach(row => {
      selectIds = [...new Set([...selectIds, row.name])];
    });
  } else {
    proTable.value?.tableData.forEach(row => {
      selectIds = selectIds.filter(name => name !== row.name);
    });
  }
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.name === row.name);
    if (isSelected) {
      selectIds = [...new Set([...selectIds, row.name])];
    } else {
      selectIds = selectIds.filter(name => name !== row.name);
    }
  });
};

const diffTabledata = ref<DiffItem[]>([]);
// 接收父组件传递的 props
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  diffdata: {
    type: Array<DiffItem>,
    default: () => []
  }
});

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const columns = reactive<ColumnProps[]>([
  { type: "selection", fixed: "left", width: 50 },
  {
    type: "index",
    label: t("device.paramCompare.sequence"),
    width: 60
  },
  {
    prop: "name",
    label: t("device.paramCompare.name"),
    search: {
      el: "input",
      tooltip: t("device.paramCompare.searchName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "description",
    label: t("device.paramCompare.description"),
    search: {
      el: "input",
      tooltip: t("device.paramCompare.searchDescription"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "minValue",
    label: t("device.paramCompare.minValue")
  },
  {
    prop: "maxValue",
    label: t("device.paramCompare.maxValue")
  },
  {
    prop: "step",
    label: t("device.paramCompare.step")
  },
  {
    prop: "unit",
    label: t("device.paramCompare.unit"),
    isShow: false
  },
  {
    prop: "inf",
    label: t("device.paramCompare.address"),
    isShow: false
  },
  {
    prop: "oldValue",
    label: t("device.paramCompare.oldValue"),
    render: scope => {
      return (
        <div>
          <el-text type="primary">{scope.row.oldValue}</el-text>
        </div>
      );
    }
  },
  {
    prop: "newValue",
    label: t("device.paramCompare.newValue"),
    render: scope => {
      return (
        <div>
          <el-text type="danger">{scope.row.newValue}</el-text>
        </div>
      );
    }
  }
]);
async function getData() {
  console.log("getData");
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  console.log(newParams);
  let resultData = toRaw(props.diffdata);
  if (newParams.name) {
    resultData = resultData?.filter(data => {
      return data.name.toLowerCase().includes(newParams.name.toLowerCase());
    });
    console.log(resultData);
  }
  if (newParams.description) {
    resultData = resultData?.filter(data => {
      return data.description.toLowerCase().includes(newParams.description.toLowerCase());
    });
  }
  diffTabledata.value = resultData;
  proTable.value?.refresh();
  nextTick(() => {
    proTable.value?.tableData.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, selectIds.includes(row.name));
    });
  });
}
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

onMounted(() => {
  console.log("diffData", props.diffdata);
  nextTick(() => {
    getData();
    proTable.value?.element?.toggleAllSelection();
  });
});

// 定义事件
const emit = defineEmits(["confirm", "cancel", "update:visible"]);

// 使用计算属性来实现双向绑定
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit("update:visible", value);
  }
});

// 处理确认导入
const handleConfirm = () => {
  if (proTable.value?.selectedListIds.length == 0) {
    ElMessageBox.alert(t("device.paramCompare.messages.noSelection"), t("device.paramCompare.messages.error"), {
      type: "error"
    });
    return;
  }

  let resultValue = toRaw(props.diffdata);
  let resultData: DiffItem[] = [];
  resultData = resultValue.filter(item => {
    return selectIds.includes(item.name);
  });
  emit("confirm", resultData);
  // 通过emit通知父组件关闭对话框
  emit("update:visible", false);
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  // 通过emit通知父组件关闭对话框
  emit("update:visible", false);
};
</script>

<style scoped>
.old-value {
  color: #f56c6c;
  text-decoration: line-through;
}
.new-value {
  font-weight: bold;
  color: #67c23a;
}
.header {
  margin-bottom: 5px;
}
.dialog-header {
  display: flex;
  gap: 8px;
  align-items: center;
}
:deep(.resizable-dialog) {
  display: flex;
  flex-direction: column;
  min-width: 400px;
  min-height: 200px;
  overflow: auto;
  resize: both;
}
.content {
  flex: 1;
  overflow: auto;
}
.drag-handle {
  /* 覆盖 CSS resize 的默认拖拽柄 */
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2000; /* 确保在最上层 */
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}
</style>
