<template>
  <div class="card">
    <el-tabs v-model="activeName" tab-position="left" class="config-main">
      <el-tab-pane name="sysConfig">
        <template #label>
          <span style="display: flex; align-items: center">
            <el-icon style="margin-right: 6px"><Setting /></el-icon>
            <span>{{ t("sys.config.title") }}</span>
          </span>
        </template>
        <SysBaseConfig :sys-configs="sysBase" />
      </el-tab-pane>
      <el-tab-pane name="paramConfig">
        <template #label>
          <span style="display: flex; align-items: center">
            <el-icon style="margin-right: 6px"><List /></el-icon>
            <span>{{ t("sys.config.paramTitle") }}</span>
          </span>
        </template>
        <ParamConfig :param-configs="paramInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import SysBaseConfig from "./components/sysBaseConfig.vue";
import ParamConfig from "./components/paramConfig.vue";
import { SysConfig } from "@/api";
import { useI18n } from "vue-i18n";
import { Setting, List } from "@element-plus/icons-vue";

const { t } = useI18n();
const activeName = ref("sysConfig");

const sysBase = ref<SysConfig.ConfigInfo[]>([]);
const paramInfo = ref<SysConfig.ConfigInfo[]>([]);
</script>

<style lang="scss" scoped>
.config-main {
  width: 100%;
  height: 100%;
  margin-left: 3px;
  :deep(.el-tabs__header) {
    margin-bottom: 10px;
  }
  :deep(.el-tabs) {
    height: 100%;
    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
