import { IECReq } from "../../interface/debug/request";
import { IECResult } from "iec-common/dist/data/iecdata";
import { BaseService } from "./baseservice";
import {
  UnSubMonVarRequestData,
  UnSubRealEventRequestData,
  UpadRpcServiceRes,
} from "iec-upadrpc/dist/src/data";

/**
 * <AUTHOR>
 * @version 1.0 2025-04-24
 */
class RealEventService extends BaseService {
  async subRealEvent(
    req: IECReq<string[]>
  ): Promise<IECResult<UpadRpcServiceRes>> {
    let result = new IECResult<UpadRpcServiceRes>();
    result.code = 0;
    const client = this.getClient(req.head.id).data;
    if (!client) {
      return result;
    }
    result = await client.subRealEvent(req.data);
    return result;
  }
  async unSubRealEvent(
    req: IECReq<UnSubRealEventRequestData>
  ): Promise<IECResult<UpadRpcServiceRes>> {
    let result = new IECResult<UpadRpcServiceRes>();
    result.code = 0;
    const client = this.getClient(req.head.id).data;
    if (!client) {
      return result;
    }
    result = await client.unSubRealEvent(req.data);
    return result;
  }
}
RealEventService.toString = () => "[class RealEventService]";
const realEventService = new RealEventService();

export { RealEventService, realEventService };
