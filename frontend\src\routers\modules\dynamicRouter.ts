import { HOME_URL } from "@/config";
import { RouteRecordRaw } from "vue-router";
import { useUserStore, useAuthStore } from "@/stores/modules";
import router from "@/routers";

// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * @description 初始化动态路由
 */
export const initDynamicRouter = async () => {
  const userStore = useUserStore();
  const authStore = useAuthStore();

  try {
    let homePath: string = HOME_URL; // 首页路径
    // 1.获取菜单列表 && 按钮权限列表
    await authStore.getAuthMenuList();
    await authStore.getAuthButtonList();
    const home = authStore.authMenuListGet.filter(item => item.isHome === true); // 获取首页
    if (home.length > 0) {
      homePath = home[0].path; // 设置第一个首页项作为首页路径
    } else {
      //如果不存在首页设置第一个菜单为首页
      let firstMenu = authStore.authMenuListGet[0].children;
      if (firstMenu && firstMenu.length > 0) {
        homePath = firstMenu[0].path as string;
      }
    }

    // 3.添加动态路由
    authStore.flatMenuListGet.forEach(item => {
      item.children && delete item.children;
      if (item.component && typeof item.component == "string") {
        // 首页单独分包并命名 chunk
        if (item.path === HOME_URL) {
          item.component = () => import(/* webpackChunkName: "home" */ `@/views/biz/debug/device/index.vue`);
        } else {
          item.component = modules["/src/views/" + item.component + ".vue"];
        }
      }
      if (item.meta.isFull) {
        router.addRoute(item as unknown as RouteRecordRaw);
      } else {
        router.addRoute("layout", item as unknown as RouteRecordRaw);
      }
    });
    return Promise.resolve(homePath);
  } catch (error) {
    // 当按钮 || 菜单请求出错时，重定向到登陆页
    userStore.clearToken();
    return Promise.reject(error);
  }
};
