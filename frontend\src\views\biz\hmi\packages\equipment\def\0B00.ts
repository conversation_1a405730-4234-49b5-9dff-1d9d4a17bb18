import { calculateEllipse, calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "0B00",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.5)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 15.86, 23, 22.5)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.33,
        y1: 4.63,
        x2: 12,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 16.33,
        y1: 4.33,
        x2: 11,
        y2: 9.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.33,
        y1: 8.75,
        x2: 11.33,
        y2: 14.33
      }
    },
    {
      tagName: "polygon",
      groupSelector: "polygon",
      attrs: {
        points: calculateTriangle(6.67, 26.33, 9.33, 7.33)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    },
    polygon: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
