export default {
  limit: {
    role: {
      title: "Gestion des rôles",
      roleList: "Liste des rôles",
      addRole: "Ajouter un rôle",
      deleteSelected: "Supprimer la sélection",
      confirm: "Confirmer",
      name: "Nom du rôle",
      code: "Code du rôle",
      sort: "Tri",
      description: "Description",
      operation: "Opération",
      form: {
        add: "Ajouter un rôle",
        edit: "Modifier le rôle",
        view: "Voir le rôle",
        name: "Nom du rôle",
        title: "Nom du rôle",
        code: "Code du rôle",
        category: "Catégorie du rôle",
        org: "Organisation d'appartenance",
        status: "Statut",
        sort: "Tri",
        description: "Description",
        cancel: "Annuler",
        confirm: "Confirmer",
        validation: {
          name: "Veuillez entrer le nom du rôle",
          title: "Veuillez entrer le nom du rôle",
          code: "Veuillez entrer le code du rôle",
          category: "Veuillez sélectionner la catégorie du rôle",
          org: "Veuillez sélectionner l'organisation d'appartenance",
          status: "Veuillez sélectionner le statut",
          sort: "Veuillez entrer le tri"
        }
      },
      permission: {
        title: "Attribution des permissions",
        role: "Rôle",
        permissions: "Permissions",
        save: "Enregistrer",
        cancel: "Annuler",
        validation: {
          role: "Veuillez sélectionner un rôle",
          permissions: "Veuillez sélectionner des permissions"
        }
      },
      columns: {
        name: "Nom du rôle",
        code: "Code du rôle",
        category: "Catégorie du rôle",
        org: "Organisation d'appartenance",
        status: "Statut",
        sort: "Tri",
        operation: "Opération"
      },
      category: {
        system: "Rôle système",
        org: "Rôle organisationnel"
      },
      status: {
        enable: "Activer",
        disable: "Désactiver"
      },
      grantResource: {
        title: "Autoriser les ressources",
        warning: "Veuillez sélectionner les ressources à autoriser",
        firstLevel: "Menu de premier niveau",
        menu: "Menu",
        buttonAuth: "Autorisation des boutons",
        cancel: "Annuler",
        confirm: "Confirmer",
        selectDataScope: "Sélectionner la portée des données",
        api: "Interface",
        dataScope: "Portée des données"
      },
      grantPermission: {
        title: "Autoriser les permissions",
        warning: "Veuillez sélectionner les permissions à autoriser",
        api: "Interface",
        apiPlaceholder: "Veuillez entrer le nom de l'interface",
        dataScope: "Portée des données",
        cancel: "Annuler",
        confirm: "Confirmer"
      },
      dataScope: {
        selectOrg: "Sélectionner l'organisation",
        orgList: "Liste des organisations",
        cancel: "Annuler",
        confirm: "Confirmer"
      }
    }
  }
};
