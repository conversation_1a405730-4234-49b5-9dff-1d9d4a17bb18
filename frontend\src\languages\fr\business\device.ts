export default {
  configure: {
    remoteSet: "Téléréglage"
  },
  console: {
    title: "Console",
    clear: "Effacer",
    selectAll: "Tout sélectionner",
    copy: "Copier",
    copySuccess: "Copie réussie",
    noTextSelected: "Aucun texte sélectionné",
    copyFailed: "Échec de la copie",
    clearSuccess: "Console effacée",
    collapse: "Réduire",
    expand: "Développer"
  },
  groupInfo: {
    title: "Informations de groupe",
    table: {
      id: "Index",
      name: "Nom",
      desc: "Description",
      fc: "FC",
      count: "Nombre"
    },
    messages: {
      fetchDataError: "Erreur lors de l'obtention des données",
      fetchedData: "Données obtenues :"
    }
  },
  treeClickLog: "Clic treeClick : ",
  contentView: "Vue du contenu",
  emptyDeviceId: "L'ID de l'appareil actuel est vide",
  invalidResponseStructure: "Structure de réponse invalide",
  formattedMenuDataLog: "Données de menu formatées ===",
  allSettings: "Toutes les valeurs fixes",
  allEditSpSettings: "Toutes les valeurs fixes mono-zone",
  allEditSgSettings: "Toutes les valeurs fixes multi-zones",
  deviceTreeDataLog: "Données de l'arbre d'appareils",
  failedToLoadMenu: "Échec du chargement du menu d'appareil :",
  innerTabs: {
    contentView: "Contenu",
    fileUpload: "Upload",
    fileDownload: "Download",
    deviceTime: "Sync",
    deviceOperate: "Opération",
    variableDebug: "Debug",
    oneClickBackup: "Sauvegarde",
    entryConfig: "Config",
    tabClickLog: "Clic d'onglet :"
  },
  devices: {
    notConnectedAlt: "Appareil non connecté",
    pleaseConnect: "Veuillez d'abord connecter l'appareil !"
  },
  list: {
    unnamedDevice: "Appareil sans nom",
    connected: "Connecté",
    disconnected: "Déconnecté",
    connect: "Se connecter",
    edit: "Modifier",
    disconnect: "Se déconnecter",
    remove: "Supprimer",
    noDeviceFound: "Aucun appareil trouvé",
    handleClickLog: "Clic handleListClick :",
    disconnectBeforeEdit: "Veuillez d'abord vous déconnecter avant de modifier",
    connectSuccess: "Appareil {name} : Connexion réussie",
    connectExist: "Appareil {name} : Connexion déjà existante",
    connectFailed: "Appareil {name} : Échec de connexion",
    connectFailedReason: "Raison de l'échec de connexion de l'appareil :",
    disconnectedSuccess: "Appareil {name} : Déconnecté",
    operateFailed: "Appareil {name} : Échec de l'opération",
    disconnectBeforeDelete: "Veuillez d'abord vous déconnecter avant de supprimer",
    dataLog: "Données :",
    ipPortExist: "Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"
  },
  search: {
    placeholder: "Rechercher un appareil",
    ipPortExist: "Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"
  },
  summaryPie: {
    other: "Autre",
    title: "Proportion des valeurs fixes",
    subtext: "Valeurs fixes du groupe"
  },
  deviceInfo: {
    title: "Informations d'appareil",
    export: "Exporter",
    exportTitle: "Exporter les informations d'appareil",
    exportLoading: "Export des informations de base de l'appareil en cours...",
    exportSuccess: "Export des informations de base de l'appareil réussi",
    exportFailed: "Échec de l'export des informations de base de l'appareil",
    getInfoFailed: "Échec de l'obtention des informations d'appareil. Raison : {msg}",
    getInfoFailedEmpty: "Échec de l'obtention des informations d'appareil. Raison : données vides !",
    defaultFileName: "Informations d'appareil.xlsx",
    confirm: "Confirmer",
    tip: "Conseil"
  },
  allParamSetting: {
    title: "Toutes les valeurs fixes",
    autoRefresh: "Actualisation automatique",
    refresh: "Actualiser",
    confirm: "Confirmer",
    import: "Importer",
    export: "Exporter",
    groupTitle: "Groupe de valeurs fixes :",
    allGroups: "Tout",
    noDataToImport: "Aucune donnée à importer",
    importSuccess: "Import des valeurs fixes réussi",
    importFailed: "Échec de l'import des valeurs fixes : {msg}",
    requestFailed: "Échec de la requête, veuillez réessayer plus tard",
    queryFailed: "Échec de la requête des valeurs fixes : {msg}",
    unsavedChanges: "Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",
    confirmButton: "Confirmer",
    cancelButton: "Annuler",
    alertTitle: "Conseil",
    errorTitle: "Erreur",
    noDataToConfirm: "Aucune donnée à confirmer",
    confirmSuccess: "Mise à jour des valeurs fixes réussie",
    confirmFailed: "Échec de la mise à jour des valeurs fixes : ",
    responseLog: "Données de réponse :",
    continueAutoRefresh: "Continuer l'actualisation automatique",
    settingGroup: "Groupe de valeurs fixes",
    all: "Tout",
    minValue: "Valeur minimale",
    maxValue: "Valeur maximale",
    step: "Pas",
    unit: "Unité",
    searchNamePlaceholder: "Entrez le nom de la valeur fixe pour rechercher",
    searchDescPlaceholder: "Entrez la description de la valeur fixe pour rechercher",
    autoRefreshWarning: "La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",
    invalidValue: "La valeur {value} de la valeur fixe {name} n'est pas dans la plage valide",
    exportFileName: "Valeurs fixes de paramètres d'appareil_Toutes les valeurs fixes.xlsx",
    selectPathLog: "Sélectionner le chemin : ",
    exportSuccess: "Liste des valeurs exportée avec succès"
  },
  variable: {
    autoRefresh: "Actualisation automatique",
    variableName: "Nom de variable",
    inputVariableName: "Veuillez entrer le nom de variable à ajouter",
    refresh: "Actualiser",
    add: "Ajouter",
    confirm: "Confirmer",
    import: "Importer",
    export: "Exporter",
    delete: "Supprimer",
    noDataToConfirm: "Aucune donnée à confirmer",
    warning: "Avertissement",
    variableModifiedSuccess: "Modification de variable réussie",
    variableModifiedFailed: "Échec de la modification de variable, raison :",
    requestFailed: "Échec de la requête, veuillez réessayer plus tard",
    error: "Erreur",
    success: "Succès",
    variableAddSuccess: "Ajout de variable réussi",
    variableAddFailed: "Échec de l'ajout de variable, raison :",
    variableDeleteSuccess: "Suppression de variable réussie",
    variableDeleteFailed: "Échec de la suppression de variable, raison :",
    exportSuccess: "Export des informations de variables de débogage d'appareil réussi",
    exportFailed: "Échec de l'export des informations de variables de débogage d'appareil, raison :",
    importSuccess: "Import des informations de variables de débogage d'appareil réussi",
    importFailed: "Échec de l'import des informations de variables de débogage d'appareil :",
    confirmRefresh: "Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",
    confirmAutoRefresh: "Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation automatique ?",
    pleaseInputVariableName: "Veuillez remplir le nom de variable",
    exportTitle: "Exporter les variables de débogage d'appareil",
    importTitle: "Importer les variables de débogage d'appareil",
    defaultExportPath: "Variables de débogage d'appareil.xlsx",
    title: "Débogage de variables",
    variableNamePlaceholder: "Veuillez entrer le nom de variable à ajouter",
    batchDelete: "Suppression en lot",
    modifySuccess: "Modification de variable réussie",
    modifyFailed: "Échec de la modification de variable, raison : {msg}",
    alertTitle: "Avertissement",
    successTitle: "Conseil",
    errorTitle: "Erreur",
    confirmButton: "Confirmer",
    cancelButton: "Annuler",
    sequence: "Index",
    name: "Nom",
    value: "Valeur",
    type: "Type",
    description: "Description",
    address: "Adresse",
    operation: "Opération",
    enterVariableName: "Veuillez entrer le nom de variable à ajouter",
    responseLog: "Données de réponse :",
    addSuccess: "Ajout de variable réussi",
    addFailed: "Échec de l'ajout de variable, raison :",
    addFailedWithName: "Échec de l'ajout de variable {name} : {reason}",
    exportFileName: "Variables de débogage d'appareil.xlsx",
    selectPathLog: "Sélection du chemin :",
    exportSuccessLog: "Export des informations de variables de débogage d'appareil réussi, {path}",
    exportFailedLog: "Échec de l'export des informations de variables de débogage d'appareil, raison :",
    importFailedLog: "Échec de l'import des informations de variables de débogage d'appareil :",
    unsavedChanges: "Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",
    continueAutoRefresh: "Continuer l'actualisation automatique",
    tip: "Conseil",
    sequenceNumber: "Index"
  },
  backup: {
    sequence: "Index",
    title: "Sauvegarde d'appareil",
    savePath: "Chemin de sauvegarde",
    setPath: "Définir le chemin de sauvegarde",
    setPathTitle: "Définir le chemin",
    startBackup: "Démarrer la sauvegarde",
    cancelBackup: "Annuler la sauvegarde",
    backup: "Sauvegarder",
    backupType: "Type de sauvegarde",
    progress: "Progression",
    status: "État",
    operation: "Opération",
    backupTypes: {
      paramValue: "Sauvegarder les valeurs fixes de paramètres d'appareil",
      faultInfo: "Sauvegarder les informations de rapport de défaillance de l'appareil",
      cidConfigPrjLog: "Sauvegarder cid/ccd/device_config/debug_info/prj/log",
      waveReport: "Sauvegarder les fichiers de forme d'onde de l'appareil"
    },
    backupDesc: "Description du contenu de la sauvegarde",
    backupDescTypes: {
      paramValue: "Exporter les valeurs de paramètres de l'appareil (param export.xlsx)",
      faultInfo: "Exporter les informations de défaut de l'appareil (rapports d'événement/opération/défaut/audit)",
      cidConfigPrjLog: "Exporter les fichiers de configuration (CID/CCD, configuration XML, fichiers journaux)",
      waveReport: "Exporter les fichiers d'onde de l'appareil (/wave/comtrade)"
    },
    locateFolder: "Localiser le dossier",
    backupSuccess: "Sauvegarde réussie",
    openFolderFailed: "Échec de l'ouverture du dossier",
    backupFailed: "Échec de la sauvegarde",
    noTypeSelected: "Veuillez d'abord sélectionner le type de sauvegarde",
    cancelSuccess: "Annulation réussie",
    cancelFailed: "Échec de l'annulation"
  },
  operate: {
    title: "Opération d'appareil",
    manualWave: "Enregistrer manuellement les événements",
    resetDevice: "Réinitialiser l'appareil",
    clearReport: "Effacer le rapport",
    clearWave: "Effacer les événements",
    executing: "En cours d'exécution...",
    selectOperation: "Veuillez sélectionner l'opération",
    success: {
      manualWave: "Enregistrement manuel des événements réussi",
      resetDevice: "Réinitialisation de l'appareil réussie",
      clearReport: "Effacement du rapport réussi",
      clearWave: "Effacement des événements réussi"
    },
    fail: {
      manualWave: "Échec de l'enregistrement manuel des événements, raison :",
      resetDevice: "Échec de la réinitialisation de l'appareil, raison :",
      clearReport: "Échec de l'effacement du rapport, raison :",
      clearWave: "Échec de l'effacement des événements, raison :"
    }
  },
  time: {
    title: "Synchronisation d'appareil",
    currentTime: "Temps actuel",
    deviceTime: "Temps de l'appareil",
    selectDateTime: "Sélectionner la date et le temps",
    milliseconds: "Millisecondes",
    now: "Maintenant",
    read: "Lire",
    write: "Écrire",
    readSuccess: "Réussite de la lecture de l'heure de l'appareil.",
    readFailed: "Échec de la lecture de l'heure de l'appareil : {msg}",
    readFailedInvalidFormat: "Échec de la lecture de l'heure de l'appareil : Format de temps invalide",
    readFailedDataError: "Échec de la lecture de l'heure de l'appareil : Erreur de format des données",
    writeSuccess: "Écriture de l'heure de l'appareil réussie.",
    writeFailed: "Échec de l'écriture de l'heure de l'appareil : {msg}",
    writeFailedInvalidFormat: "Échec de l'écriture de l'heure de l'appareil : Format de temps invalide",
    millisecondsRangeError: "La plage de valeurs de millisecondes doit être comprise entre 0 et 999",
    unknownError: "Erreur inconnue"
  },
  reportOperate: {
    title: "Opération de rapport",
    date: "Date :",
    search: "Rechercher",
    save: "Enregistrer",
    clearList: "Effacer la liste",
    loading: "Chargement des données",
    progress: {
      title: "Informations de progression",
      loading: "Chargement",
      searching: "Recherche en cours {type}"
    },
    table: {
      reportId: "Numéro de rapport",
      name: "Nom",
      time: "Temps",
      operationAddress: "Adresse d'opération",
      operationParam: "Paramètre d'opération",
      value: "Valeur",
      step: "Étape",
      source: "Source",
      sourceType: "Type de source",
      result: "Résultat"
    },
    messages: {
      selectDateRange: "Veuillez sélectionner une plage de temps complète",
      noDataToSave: "Aucune donnée à enregistrer",
      saveSuccess: "Enregistrement réussi",
      saveReport: "Enregistrer le rapport"
    }
  },
  reportGroup: {
    title: "Groupe de rapport",
    date: "Date :",
    search: "Rechercher",
    save: "Enregistrer",
    clearList: "Effacer la liste",
    autoRefresh: "Actualisation automatique",
    loading: "Chargement des données",
    progress: {
      title: "Informations de progression",
      loading: "Chargement",
      searching: "Recherche en cours {type}"
    },
    table: {
      reportId: "Numéro de rapport",
      time: "Temps",
      description: "Description"
    },
    contextMenu: {
      uploadWave: "Invoquer les événements",
      getHistoryReport: "Obtenir le rapport historique",
      saveResult: "Enregistrer le résultat",
      clearContent: "Effacer le contenu de la page"
    },
    messages: {
      selectDateRange: "Veuillez sélectionner une plage de temps complète",
      noDataToSave: "Aucune donnée à enregistrer",
      noFileToUpload: "Aucun fichier à invoquer",
      saveSuccess: "Enregistrement réussi",
      saveReport: "Enregistrer le rapport",
      waveToolNotConfigured: "Chemin d'outil de forme d'onde non configuré",
      waveFileUploading: "Invoquer le fichier de forme d'onde",
      waveFileUploadComplete: "Invoquer le fichier de forme d'onde terminé",
      waveFileUploadCompleteWithPath: "Invoquer le fichier de forme d'onde terminé, chemin : {path}",
      openWaveFileConfirm: "Souhaitez-vous ouvrir le fichier de forme d'onde avec un outil tiers?",
      openWaveFileTitle: "Avertissement",
      confirm: "Confirmer",
      cancel: "Annuler"
    },
    refresh: {
      stop: "Arrêter l'actualisation",
      start: "Actualiser automatiquement"
    },
    hiddenItems: {
      show: "Afficher les éléments cachés",
      hide: "Ne pas afficher les éléments cachés"
    }
  },
  fileDownload: {
    title: "Téléchargement de fichier",
    deviceDirectory: "Répertoire de l'appareil",
    reboot: "Redémarrer",
    noReboot: "Ne pas redémarrer",
    selectFile: "Sélectionner le fichier",
    addDownloadFile: "Ajouter le fichier à télécharger",
    addDownloadFolder: "Ajouter le dossier à télécharger",
    addDownloadFilesAndFolders: "Ajouter les fichiers et dossiers",
    downloadFile: "Télécharger le fichier",
    cancelDownload: "Annuler le téléchargement",
    importList: "Importer la liste",
    exportList: "Exporter la liste",
    batchDelete: "Supprimer en lot",
    clearList: "Effacer la liste",
    download: "Télécharger",
    delete: "Supprimer",
    fileName: "Nom du fichier",
    fileSize: "Taille du fichier",
    filePath: "Chemin du fichier",
    lastModified: "Dernière modification",
    progress: "Progression",
    status: "État",
    operation: "Opération",
    folder: "Dossier",
    waitingDownload: "En attente de téléchargement",
    calculatingFileInfo: "Calculer les informations du fichier",
    downloadPreparing: "Préparation du téléchargement",
    downloading: "Téléchargement en cours...",
    downloadComplete: "Téléchargement terminé",
    downloadError: "Erreur de téléchargement :",
    userCancelled: "Utilisateur annulé",
    allFilesComplete: "Téléchargement terminé",
    fileExists: "Le fichier {path} existe déjà, échec de l'ajout !",
    selectValidFile: "Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",
    remotePathEmpty: "Chemin distant ne peut être vide",
    noDownloadTask: "Aucune tâche de téléchargement obtenue pour annuler",
    fileSizeZero: "Le fichier {fileName} a une taille de 0, impossible de télécharger",
    downloadCancelled: "Téléchargement de fichier {path} annulé terminé",
    downloadCancelledFailed: "Échec de l'annulation du téléchargement de fichier {path}, raison : {msg}",
    fileDeleted: "Suppression de fichier {path} terminée",
    exportSuccess: "Exportation de la liste de téléchargement de fichiers réussie",
    exportFailed: "Échec de l'exportation de la liste de téléchargement de fichiers",
    importSuccess: "Importation de la liste de téléchargement de fichiers réussie",
    importFailed: "Échec de l'importation de la liste de téléchargement de fichiers : {msg}",
    downloadList: "Liste de téléchargement de fichiers",
    exportTitle: "Exporter la liste de téléchargement de fichiers",
    importTitle: "Importer la liste de téléchargement de fichiers",
    error: "Erreur",
    tip: "Conseil",
    confirm: "Confirmer",
    sequence: "Index",
    confirmButton: "Confirmer",
    cancelButton: "Annuler",
    alertTitle: "Conseil",
    errorTitle: "Erreur",
    successTitle: "Succès",
    warningTitle: "Avertissement",
    loading: "Chargement",
    executing: "Exécution...",
    noData: "Aucune donnée",
    selectDateRange: "Veuillez sélectionner la plage de dates",
    search: "Rechercher",
    save: "Enregistrer",
    clear: "Effacer",
    refresh: "Actualiser",
    stop: "Arrêter",
    start: "Démarrer",
    show: "Afficher",
    hide: "Masquer",
    showHiddenItems: "Afficher les éléments masqués",
    hideHiddenItems: "Masquer les éléments",
    continue: "Continuer",
    cancel: "Annuler",
    confirmImport: "Confirmer l'importation",
    confirmExport: "Confirmer l'exportation",
    confirmDelete: "Confirmer la suppression",
    confirmClear: "Confirmer l'effacement",
    confirmCancel: "Confirmer l'annulation",
    confirmContinue: "Confirmer la continuation",
    confirmStop: "Confirmer l'arrêt",
    confirmStart: "Confirmer le démarrage",
    confirmShow: "Confirmer l'affichage",
    confirmHide: "Confirmer le masquage",
    confirmRefresh: "Confirmer l'actualisation",
    confirmSave: "Confirmer l'enregistrement",
    confirmSearch: "Confirmer la recherche",
    confirmClearList: "Confirmer l'effacement de la liste",
    confirmImportList: "Confirmer l'importation de la liste",
    confirmExportList: "Confirmer l'exportation de la liste",
    confirmBatchDelete: "Confirmer la suppression par lot",
    confirmDownload: "Confirmer le téléchargement",
    confirmCancelDownload: "Confirmer l'annulation du téléchargement",
    confirmDeleteFile: "Confirmer la suppression du fichier",
    confirmClearFiles: "Confirmer l'effacement des fichiers",
    confirmImportFiles: "Confirmer l'importation des fichiers",
    confirmExportFiles: "Confirmer l'exportation des fichiers",
    confirmBatchDeleteFiles: "Confirmer la suppression par lot des fichiers",
    confirmDownloadFiles: "Confirmer le téléchargement des fichiers",
    confirmCancelDownloadFiles: "Confirmer l'annulation du téléchargement des fichiers",
    rename: "Renommer pour le téléchargement",
    renamePlaceholder: "Renommer lors du téléchargement (optionnel)",
    renameCopyFailed: "Échec de la copie du fichier pour le renommage :",
    packageProgram: "Emballage de programme",
    selectSaveDir: "Sélectionner le répertoire de sauvegarde",
    packageBtn: "Emballer",
    locateDir: "Localiser le dossier",
    saveDirEmpty: "Veuillez d'abord sélectionner un répertoire de sauvegarde !",
    packageSuccess: "Emballage de programme terminé !",
    packageFailed: "Échec de l'emballage : {msg}",
    noFileSelected: "Veuillez sélectionner les fichiers à emballer !",
    zipPath: "Chemin du fichier ZIP : {zipPath}",
    addToDownload: "Ajouter au téléchargement",
    fileAdded: "Fichier ajouté à la liste de téléchargement : {path}",
    rebootSuccess: "Redémarrage de l'appareil réussi",
    rebootFailed: "Échec du redémarrage de l'appareil : {msg}"
  },
  fileUpload: {
    serialNumber: "Index",
    title: "Téléchargement de fichier",
    importList: "Importer la liste",
    exportList: "Exporter la liste",
    batchDelete: "Supprimer en lot",
    clearList: "Effacer la liste",
    upload: "Télécharger",
    cancelUpload: "Annuler le téléchargement",
    delete: "Supprimer",
    sequence: "Index",
    fileName: "Nom du fichier",
    fileSize: "Taille du fichier",
    filePath: "Chemin du fichier",
    lastModified: "Dernière modification",
    progress: "Progression",
    statusTitle: "État",
    status: {
      waiting: "En attente de téléchargement",
      preparing: "Préparation du téléchargement",
      uploading: "Téléchargement en cours...",
      completed: "Téléchargement terminé",
      error: "Erreur de téléchargement :",
      cancelled: "Utilisateur annulé"
    },
    operation: "Opération",
    calculatingFileInfo: "Calculer les informations du fichier",
    uploadPreparing: "Préparation du téléchargement",
    uploading: "Téléchargement en cours...",
    uploadComplete: "Téléchargement terminé",
    uploadError: "Erreur de téléchargement : {errorMsg}",
    userCancelled: "Utilisateur annulé",
    allFilesComplete: "Téléchargement terminé",
    fileExists: "Le fichier {path} existe déjà, échec de l'ajout !",
    invalidFile: "Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",
    emptySavePath: "Chemin de sauvegarde de fichier ne peut être vide",
    fileUploadComplete: "Téléchargement de fichier {fileName} terminé",
    selectPath: "Sélectionner le chemin",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "Répertoire de l'appareil",
    savePath: "Chemin de sauvegarde",
    setPath: "Définir le chemin",
    getFiles: "Obtenir les fichiers",
    uploadFiles: "Télécharger les fichiers",
    errors: {
      invalidFile: "Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",
      emptySavePath: "Chemin de sauvegarde de fichier ne peut être vide",
      noUploadTask: "Aucune tâche de téléchargement obtenue pour annuler",
      getFilesFailed: "Échec de l'obtention des fichiers du répertoire de l'appareil",
      fileSizeZero: "Le fichier {fileName} a une taille de 0, impossible de télécharger"
    },
    messages: {
      uploadCompleted: "Téléchargement de fichier terminé",
      uploadCancelled: "Téléchargement de fichier annulé terminé",
      clearListSuccess: "Effacement de la liste de fichiers réussi"
    }
  },
  info: {
    title: "Informations d'appareil",
    export: "Exporter",
    exportSuccess: "Export des informations de base de l'appareil réussi",
    exportFailed: "Échec de l'export des informations de base de l'appareil",
    exportTip: "Conseil",
    confirm: "Confirmer",
    exportLoading: "Export des informations de base de l'appareil en cours...",
    getInfoFailed: "Échec de l'obtention des informations d'appareil. Raison :",
    dataEmpty: "Données vides !"
  },
  summary: {
    title: "Aperçu de groupe d'appareil",
    basicInfo: "Infos de base",
    settingTotal: "Total des valeurs fixes",
    telemetry: "Télémétrie",
    teleindication: "Téléindication",
    telecontrol: "Télécontrôle",
    driveOutput: "Sortie de commande",
    settingRatio: "Proportion des valeurs fixes"
  },
  dict: {
    refresh: "Actualiser",
    confirm: "Confirmer",
    import: "Importer",
    export: "Exporter",
    sequence: "Index",
    shortAddress: "Adresse courte",
    shortAddressTooltip: "Entrez l'adresse courte à rechercher",
    chinese: "Chinois",
    english: "Anglais",
    spanish: "Espagnol",
    french: "Français",
    operation: "Opération",
    confirmLog: "Confirmer le dictionnaire",
    importLog: "Importer le dictionnaire",
    exportLog: "Exporter le dictionnaire",
    refreshLog: "Actualiser le dictionnaire",
    newValueLog: "Nouvelle valeur :"
  },
  allParamCompare: {
    title: "Comparer la différence de valeur fixe d'importation",
    cancel: "Annuler",
    confirm: "Confirmer l'importation",
    groupName: "Nom de groupe",
    name: "Nom",
    description: "Description",
    minValue: "Valeur minimale",
    maxValue: "Valeur maximale",
    step: "Pas",
    unit: "Unité",
    address: "Adresse",
    oldValue: "Valeur ancienne",
    newValue: "Nouvelle valeur",
    sequence: "Index",
    searchName: "Entrez le nom de la valeur fixe pour rechercher",
    searchDescription: "Entrez la description de la valeur fixe pour rechercher",
    messages: {
      noSelection: "Aucune donnée sélectionnée",
      error: "Erreur"
    }
  },
  deviceForm: {
    title: {
      add: "Ajouter l'appareil",
      edit: "Modifier l'appareil"
    },
    name: "Nom de l'appareil",
    ip: "IP",
    port: "Port",
    connectTimeout: "Délai de connexion (millisecondes)",
    readTimeout: "Délai de requête globale (millisecondes)",
    paramTimeout: "Délai de modification de valeur fixe (millisecondes)",
    encrypted: "Connexion cryptée",
    advanced: {
      show: "Afficher les options avancées",
      hide: "Masquer les options avancées"
    },
    buttons: {
      cancel: "Annuler",
      confirm: "Confirmer"
    },
    messages: {
      nameRequired: "Veuillez entrer le nom de l'appareil",
      nameTooLong: "Le nom de l'appareil ne doit pas être trop long",
      invalidIp: "Veuillez entrer une IP valide",
      invalidPort: "Le port doit être compris entre 1 et 65535",
      timeoutTooShort: "Le délai ne doit pas être trop court"
    }
  },
  paramCompare: {
    title: "Comparer la différence de valeur fixe d'importation",
    cancel: "Annuler",
    confirm: "Confirmer l'importation",
    sequence: "Index",
    name: "Nom",
    description: "Description",
    minValue: "Valeur minimale",
    maxValue: "Valeur maximale",
    step: "Pas",
    unit: "Unité",
    address: "Adresse",
    oldValue: "Valeur ancienne",
    newValue: "Nouvelle valeur",
    searchName: "Entrez le nom de la valeur fixe pour rechercher",
    searchDescription: "Entrez la description de la valeur fixe pour rechercher",
    messages: {
      noSelection: "Aucune donnée sélectionnée",
      error: "Erreur"
    }
  },
  progress: {
    title: "Informations de progression",
    executing: "En cours d'exécution..."
  },
  remoteYm: {
    title: "Télémétrie à distance",
    sequence: "Index",
    shortAddress: "Adresse courte",
    description: "Description",
    value: "Valeur",
    operation: "Opération",
    inputShortAddressFilter: "Entrez l'adresse courte filtrée",
    inputDescriptionFilter: "Entrez la description filtrée",
    invalidData: "Valeur {name} de {value} non valide",
    error: "Erreur",
    success: "Succès",
    executeSuccess: "Exécution réussie",
    prompt: "Invite",
    confirmButton: "Confirmer",
    confirmExecute: "Confirmer l'exécution",
    confirm: "Confirmer",
    executeButton: "Exécuter",
    cancelButton: "Annuler"
  },
  remoteYt: {
    title: "Contrôle à distance",
    sequence: "Index",
    directControl: "Contrôle direct",
    selectControl: "Contrôle sélectionné",
    shortAddress: "Adresse courte",
    description: "Description",
    value: "Valeur",
    operation: "Opération",
    inputShortAddressFilter: "Entrez l'adresse courte filtrée",
    inputDescriptionFilter: "Entrez la description filtrée",
    invalidData: "Valeur {name} de {value} non valide",
    error: "Erreur",
    success: "Succès",
    executeSuccess: "Exécution réussie",
    prompt: "Invite",
    confirm: "Confirmer",
    errorInfo: "Information d'erreur",
    executeFailed: "Échec de l'exécution du télécontrôle à distance, raison : {msg}",
    executeSuccessLog: "{desc} Exécution du télécontrôle à distance réussie",
    cancelSuccess: "Annulation réussie",
    cancelFailed: "Échec de l'annulation du télécontrôle à distance, raison : {msg}",
    selectSuccess: "Sélection réussie, exécuter ?",
    confirmInfo: "Information de confirmation",
    execute: "Exécuter",
    cancel: "Annuler"
  },
  paramSetting: {
    title: "Valeurs fixes de paramètres d'appareil",
    autoRefresh: "Actualisation automatique",
    refresh: "Actualiser",
    confirm: "Confirmer",
    import: "Importer",
    export: "Exporter",
    currentEditArea: "Zone en cours d'exécution",
    selectEditArea: "Zone en cours de modification",
    noDataToImport: "Aucune donnée à importer",
    noDataToConfirm: "Aucune donnée à confirmer",
    importSuccess: "Import des valeurs fixes réussi",
    importFailed: "Échec de l'import des valeurs fixes",
    updateSuccess: "Mise à jour des valeurs fixes réussie",
    updateFailed: "Échec de la mise à jour des valeurs fixes",
    requestFailed: "Échec de la requête, veuillez réessayer plus tard",
    setEditArea: "Définir",
    setEditAreaTitle: "Définir la zone de modification",
    setEditAreaSuccess: "Définir la zone de modification réussie",
    modifiedWarning: "Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",
    autoRefreshWarning: "La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",
    autoRefreshDisabled: "La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",
    invalidValue: "La valeur {value} de la valeur fixe {name} n'est pas dans la plage valide",
    exportSuccess: "Exporter les valeurs fixes de paramètres d'appareil réussie",
    exportFailed: "Échec de l'exportation des valeurs fixes de paramètres d'appareil",
    noDiffData: "Aucune donnée de différence obtenue",
    table: {
      index: "Index",
      name: "Nom",
      description: "Description",
      value: "Valeur",
      minValue: "Valeur minimale",
      maxValue: "Valeur maximale",
      step: "Pas",
      address: "Adresse",
      unit: "Unité",
      operation: "Opération"
    },
    search: {
      namePlaceholder: "Entrez le nom de la valeur fixe pour rechercher",
      descPlaceholder: "Entrez la description de la valeur fixe pour rechercher"
    }
  },
  remoteControl: {
    title: "Contrôle",
    sequence: "Index",
    shortAddress: "Adresse courte",
    description: "Description",
    control: "Contrôle/Contrôle",
    type: "Type",
    operation: "Opération",
    directControl: "Contrôle direct",
    selectControl: "Contrôle sélectionné",
    controlClose: "Contrôle",
    controlOpen: "Contrôle",
    noCheck: "Ne pas vérifier",
    syncCheck: "Vérifier la synchronisation",
    deadCheck: "Vérifier la mort",
    confirmInfo: "Confirmer les informations",
    execute: "Exécuter",
    cancel: "Annuler",
    confirm: "Confirmer",
    success: "Succès",
    failed: "Échec",
    errorInfo: "Informations d'erreur",
    promptInfo: "Informations d'invite",
    confirmSuccess: "Sélection réussie, voulez-vous exécuter ?",
    executeSuccess: "Exécution réussie",
    cancelSuccess: "Annulation réussie",
    executeFailed: "Échec de l'exécution à distance, raison :",
    cancelFailed: "Échec de l'annulation à distance, raison :",
    remoteExecuteSuccess: "Exécution à distance réussie",
    remoteCancelSuccess: "Annulation à distance réussie"
  },
  remoteDrive: {
    action: "Action",
    executeSuccess: "Exécution réussie",
    executeFailed: "Échec de l'exécution",
    prompt: "Informations d'invite",
    error: "Informations d'erreur",
    confirm: "Confirmer",
    shortAddress: "Adresse courte",
    description: "Description",
    operation: "Opération",
    enterToFilter: "Entrez l'adresse courte filtrée",
    enterToFilterDesc: "Entrez la description filtrée",
    actionSuccess: "Exécution d'action réussie",
    actionFailed: "Échec de l'exécution d'action",
    failureReason: "Raison de l'échec",
    sequence: "Index"
  },
  remoteSignal: {
    autoRefresh: "Actualisation automatique",
    refresh: "Actualiser",
    export: "Exporter",
    sequence: "Index",
    name: "Nom",
    description: "Description",
    value: "Valeur",
    quality: "Qualité",
    searchName: "Entrez le nom pour rechercher",
    searchDesc: "Entrez la description pour rechercher",
    searchValue: "Entrez la valeur pour rechercher",
    exportTitle: "Exporter les informations d'indication à distance",
    exportSuccess: "Exporter les informations d'indication à distance réussie",
    exportFailed: "Échec de l'exportation des informations d'indication à distance",
    exportSuccessWithPath: "Exporter les informations d'indication à distance réussie,",
    exportFailedWithError: "Échec de l'exportation des informations d'indication à distance :",
    invalidData: "Données invalides :",
    errorInDataCallback: "Erreur de traitement de rappel de données :",
    errorFetchingData: "Erreur lors de la récupération des données :"
  },
  remoteTelemetry: {
    autoRefresh: "Actualisation automatique",
    refresh: "Actualiser",
    export: "Exporter",
    sequence: "Index",
    name: "Nom",
    description: "Description",
    value: "Valeur",
    unit: "Unité",
    quality: "Qualité",
    searchName: "Entrez le nom pour rechercher",
    searchDesc: "Entrez la description pour rechercher",
    searchValue: "Entrez la valeur pour rechercher",
    exportTitle: "Exporter les informations d'état d'appareil",
    exportSuccess: "Exporter les informations d'état d'appareil réussie",
    exportFailed: "Échec de l'exportation des informations d'état d'appareil",
    exportSuccessWithPath: "Exporter les informations d'état d'appareil réussie,",
    exportFailedWithError: "Échec de l'exportation des informations d'état d'appareil :",
    confirm: "Confirmer",
    tip: "Conseil",
    exportFileName: "Informations d'état d'appareil",
    selectPathLog: "Sélectionner le chemin :"
  },
  remote: {
    directControl: "Contrôle direct",
    selectControl: "Contrôle sélectionné",
    serialNumber: "Index",
    shortAddress: "Adresse courte",
    description: "Description",
    value: "Valeur",
    operation: "Opération",
    inputShortAddressFilter: "Entrez l'adresse courte filtrée",
    inputDescriptionFilter: "Entrez la description filtrée",
    invalidData: "Valeur {name} de {value} non valide",
    error: "Erreur",
    success: "Succès",
    executeSuccess: "Exécution réussie",
    prompt: "Informations d'invite",
    confirm: "Confirmer",
    errorInfo: "Informations d'erreur",
    executeFailed: "Échec de l'exécution à distance, raison : {msg}",
    executeSuccessLog: "{desc} Exécution à distance réussie",
    cancelSuccess: "Annulation réussie",
    cancelFailed: "Échec de l'annulation à distance, raison : {msg}",
    selectSuccess: "Sélection réussie, voulez-vous exécuter ?",
    confirmInfo: "Confirmer les informations",
    execute: "Exécuter",
    cancel: "Annuler"
  },
  report: {
    uploadWave: "Invoquer les événements",
    searchHistory: "Obtenir le rapport historique",
    saveResult: "Enregistrer le résultat",
    clearContent: "Effacer le contenu de la page",
    date: "Date",
    query: "Rechercher",
    save: "Enregistrer",
    autoRefresh: "Actualiser automatiquement",
    stopRefresh: "Arrêter l'actualisation",
    clearList: "Effacer la liste",
    progressInfo: "Informations de progression",
    loading: "Chargement des données",
    reportNo: "Numéro de rapport",
    time: "Temps",
    description: "Description",
    noFileToUpload: "Aucun fichier à invoquer",
    uploadSuccess: "Invoquer les événements terminé",
    uploadPath: "Invoquer les événements terminé, chemin :",
    noDataToSave: "Aucune donnée à enregistrer",
    saveSuccess: "Enregistrement réussi",
    saveReport: "Enregistrer le rapport",
    openWaveConfirm: "Souhaitez-vous ouvrir le fichier d'événements avec un outil tiers?",
    confirm: "Confirmer",
    cancel: "Annuler",
    waveToolNotConfigured: "Chemin d'outil de forme d'onde non configuré",
    pleaseSelectTimeRange: "Veuillez sélectionner une plage de temps complète",
    querying: "Recherche en cours",
    reportNumber: "Numéro de rapport",
    operationAddress: "Adresse d'opération",
    operationParams: "Paramètres d'opération",
    result: "Résultat",
    progress: "Informations de progression",
    loadingText: "Chargement",
    selectCompleteTimeRange: "Veuillez sélectionner une plage de temps complète",
    fileUploading: "Invoquer les événements",
    fileUploadComplete: "Invoquer les événements terminé"
  },
  customMenu: {
    addMenu: "Ajouter un menu personnalisé",
    editMenu: "Modifier le menu personnalisé",
    deleteMenu: "Supprimer le menu personnalisé",
    addReport: "Ajouter un rapport personnalisé",
    editReport: "Modifier le rapport personnalisé",
    deleteReport: "Supprimer le rapport personnalisé",
    addPointGroup: "Ajouter un groupe personnalisé (points)",
    editPointGroup: "Modifier le groupe personnalisé (points)",
    deletePointGroup: "Supprimer un groupe personnalisé (points)",
    selectedPoints: "Points sélectionnés",
    selectFc: "Sélectionner FC",
    selectGroupType: "Sélectionner le type de groupe",
    groupTypes: {
      ST: "Signal distant",
      MX: "Mesure distante",
      SP: "Réglage zone unique",
      SG: "Réglage zone multiple"
    },
    filterPlaceholder: "Filtrer par nom/description",
    loadingData: "Chargement des données...",
    noDataForFc: "Aucune donnée pour ce FC",
    noDataForGroupType: "Aucune donnée pour ce type de groupe",
    pleaseSelectFc: "Veuillez d'abord sélectionner FC",
    pleaseSelectGroupType: "Veuillez d'abord sélectionner le type de groupe",
    loadingGroupTypeData: "Chargement des données de type de groupe...",
    loadingGroupTypes: "Chargement des données de types de groupe...",
    loadedGroupTypes: "Types de groupe chargés",
    dataLoadComplete: "Chargement des données terminé",
    dataLoadFailed: "Échec du chargement des données",
    switchingToGroupType: "Basculement vers",
    loadingGroupTypeDataSingle: "Chargement des données...",
    loadGroupTypeFailed: "Échec du chargement des données",
    loadGroupTypeError: "Erreur lors du chargement des données",
    inputGroupName: "Veuillez saisir le nom du groupe",
    inputGroupDesc: "Veuillez saisir la description du groupe",
    selectGroupTypeFirst: "Veuillez d'abord sélectionner le type de groupe",
    menuName: "Nom du groupe",
    menuDesc: "Description",
    reportName: "Nom du rapport",
    reportDesc: "Description",
    reportKeyword: "Mot-clé",
    reportInherit: "Hériter du rapport",
    inputMenuName: "Veuillez saisir le nom du groupe",
    inputMenuDesc: "Veuillez saisir la description",
    inputReportName: "Veuillez saisir le nom du rapport",
    inputReportDesc: "Veuillez saisir la description",
    inputReportKeyword: "Veuillez saisir le mot-clé",
    selectReportInherit: "Veuillez sélectionner le rapport à hériter",
    cancel: "Annuler",
    confirm: "Confirmer",
    successAddMenu: "Menu personnalisé ajouté avec succès",
    successEditMenu: "Menu personnalisé modifié avec succès",
    successDeleteMenu: "Menu personnalisé supprimé avec succès",
    successAddReport: "Rapport personnalisé ajouté avec succès",
    successEditReport: "Rapport personnalisé modifié avec succès",
    successDeleteReport: "Rapport personnalisé supprimé avec succès",
    successDeletePointGroup: "Groupe personnalisé (points) supprimé avec succès",
    errorAction: "Échec de l'opération",
    errorDelete: "Échec de la suppression",
    confirmDeleteMenu: "Êtes-vous sûr de vouloir supprimer ce menu personnalisé ?",
    confirmDeleteReport: "Êtes-vous sûr de vouloir supprimer ce rapport personnalisé ?",
    confirmDeletePointGroup: "Êtes-vous sûr de vouloir supprimer ce groupe personnalisé (points) ?",
    tip: "Conseil"
  },
  tree: {
    inputGroupName: "Veuillez saisir le nom du groupe",
    expandAll: "Tout développer",
    collapseAll: "Tout réduire"
  }
};
