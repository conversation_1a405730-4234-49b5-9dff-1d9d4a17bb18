import { calculateRect } from "../../graph/GraphUtil";

const e = {
  shape: "0301",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 28,
        x2: 5,
        y2: 33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 5,
        y1: 0,
        x2: 5,
        y2: 6
      }
    },
    {
      tagName: "rect",
      groupSelector: "rect",
      attrs: {
        ...calculateRect(0, 5, 10, 23.5)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0.17,
        y1: 5,
        x2: 9.5,
        y2: 28
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 9.83,
        y1: 5,
        x2: 0.5,
        y2: 28
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    rect: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
