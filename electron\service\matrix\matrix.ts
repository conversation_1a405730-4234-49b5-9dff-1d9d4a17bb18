"use strict";

import { IECReq } from "../../interface/debug/request";
import { logger } from "ee-core/log";
import { ExcelExporter } from "../../utils/excelUtils";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { Column } from "../../interface/debug/exportTypes";
import { ImportResult, SheetData } from "../../interface/debug/sheetDara";
import { AllParamItem } from "../../data/debug/paramItem";
import { XmlHandler } from "../../utils/xmlHandler";
import { CsvHandler } from "../../utils/csvHandler";
import { MatrixTaskItem } from "../../interface/matrix/matrix";
import { IEC_EVENT, IECNotify } from "../../data/debug/notify";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { getMainWindow } from "ee-core/electron";
import { deviceOperateService } from "../debug/deviceoperate";
import { ChildPoolJob } from "ee-core/jobs";
import { t } from "../../data/i18n/i18n";

const IECCONSTANTS = require("../../data/debug/iecConstants").default;

/**
 * MatrixService
 * <AUTHOR>
 * @class
 */
class MatrixService {
  // 导出工具类
  private excelExporter: ExcelExporter;

  constructor() {
    this.excelExporter = new ExcelExporter();
  }

  /**
   *  执行任务
   * @returns {Promise<ApiResponse>}
   */
  async runTask(req: IECReq<any>, maxConcurrency = 4): Promise<boolean> {
    logger.info(
      `[MatrixService] ${t("services.matrix.runTaskEntry")}:`,
      req,
      `maxConcurrency: ${maxConcurrency}`
    );
    try {
      const devices = req.data as MatrixTaskItem[];
      logger.info(
        `[MatrixService] runTask - ${t("services.matrix.taskDeviceCount")}:`,
        devices.length
      );

      const pool = new ChildPoolJob();
      pool.create(maxConcurrency).then((pids) => {
        if (Array.isArray(devices)) {
          const totalBatch = Math.ceil(devices.length / maxConcurrency);
          // logger.info("totalBatch:", totalBatch);
          // 批量执行
          for (let batch = 0; batch < maxConcurrency; batch++) {
            const start = batch * totalBatch;
            const end = start + totalBatch;
            const deviceList = devices.slice(start, end);
            logger.info(deviceList.length);

            // logger.info("=================pids", pids);
            const jobId = pids[batch];
            // logger.info("=================device", device);
            // const filePath = path.join(__dirname, "worker.js")
            pool
              .runPromise("./jobs/matrix/task", {
                jobId,
                deviceList: JSON.stringify(deviceList),
              })
              .then((myProc) => {
                let eventName = "job-progress-" + jobId;
                myProc.emitter.on(eventName, (msg) => {
                  logger.info(msg);
                  if (msg.type === "progress" || msg.type === "result") {
                    const notify: IECNotify = {
                      type: "matrixDownload",
                      data: msg,
                    };
                    sendMessageToUIByNotify(
                      IEC_EVENT.MATRIX_NOTIFY,
                      notify,
                      getMainWindow()
                    );
                  }
                });
                let eventRName = "job-result-" + jobId;
                myProc.emitter.on(eventRName, (msg) => {
                  logger.info(msg);
                  if (msg.type === "progress" || msg.type === "result") {
                    const notify: IECNotify = {
                      type: "matrixDownload",
                      data: msg,
                    };
                    sendMessageToUIByNotify(
                      IEC_EVENT.MATRIX_NOTIFY,
                      notify,
                      getMainWindow()
                    );
                  }
                });
                let finishEvent = "job-result-finish";
                myProc.emitter.on(finishEvent, (msg) => {
                  logger.info(msg);
                  myProc.emitter.removeAllListeners(eventName);
                  myProc.emitter.removeAllListeners(eventRName);
                  myProc.emitter.removeAllListeners(finishEvent);
                });
              });
          }
        }
      });
      logger.info(
        `[MatrixService] runTask - ${t("services.matrix.taskDistributionComplete")}`
      );
      return true;
    } catch (error) {
      logger.error("[MatrixService] runTask - Error occurred", error);
      throw error;
    }
  }

  /**
   *  查询装置列表
   * @returns {Promise<ApiResponse>}
   */
  async getDeviceList(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[MatrixService] ${t("services.matrix.getDeviceListEntry")}:`,
      req
    );
    try {
      logger.debug(
        `[MatrixService] getDeviceList - ${t("services.matrix.getDeviceListStart")}`
      );
      const result = await matrixService.getDeviceList(req);
      logger.info(
        `[MatrixService] getDeviceList - ${t("services.matrix.getDeviceListComplete")}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] getDeviceList - Error occurred", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async addDevice(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(`[MatrixService] ${t("services.matrix.addDeviceEntry")}:`, req);
    try {
      logger.debug(
        `[MatrixService] addDevice - ${t("services.matrix.addDeviceStart")}`
      );
      const result = await matrixService.addDevice(req);
      logger.info(
        `[MatrixService] addDevice - ${t("services.matrix.addDeviceComplete")}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] addDevice - Error occurred", error);
      throw error;
    }
  }

  /**
   *  删除装置
   * @returns {Promise<ApiResponse>}
   */
  async deleteDevice(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[MatrixService] ${t("services.matrix.deleteDeviceEntry")}:`,
      req
    );
    try {
      logger.debug(
        `[MatrixService] deleteDevice - ${t("services.matrix.deleteDeviceStart")}`
      );
      const result = await deviceOperateService.removeDeviceCfg(req.head.id);
      logger.info(
        `[MatrixService] deleteDevice - ${t("services.matrix.deleteDeviceComplete")}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] deleteDevice - Error occurred", error);
      throw error;
    }
  }

  /**
   *  更新装置
   * @returns {Promise<ApiResponse>}
   */
  async updateDevice(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[MatrixService] ${t("services.matrix.updateDeviceEntry")}:`,
      req
    );
    try {
      logger.debug(
        `[MatrixService] updateDevice - ${t("services.matrix.updateDeviceStart")}`
      );
      const result = await deviceOperateService.updateDeviceCfg(req.data);
      logger.info(
        `[MatrixService] updateDevice - ${t("services.matrix.updateDeviceComplete")}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] updateDevice - Error occurred", error);
      throw error;
    }
  }

  async importDeviceList(req: IECReq<any>): Promise<any[]> {
    logger.info(
      `[MatrixService] ${t("services.matrix.importDeviceListEntry")}:`,
      req
    );
    try {
      logger.debug(
        `[MatrixService] importDeviceList - ${t("services.matrix.importDeviceListStart")}`
      );
      const { path } = req.data;
      let parsedData: any[] = [];
      if (String(path).endsWith("xlsx")) {
        const keyMapping = {
          [t("services.matrix.deviceNameHeader")]: "name",
          [t("services.matrix.deviceAddressHeader")]: "ip",
          [t("services.matrix.devicePortHeader")]: "port",
          [t("services.matrix.encryptedHeader")]: "encrypted",
          [t("services.matrix.downFileHeader")]: "downFile",
          [t("services.matrix.importParamHeader")]: "importParam",
          [t("services.matrix.connectTimeoutHeader")]: "connectTimeout",
          [t("services.matrix.paramTimeoutHeader")]: "paramTimeout",
          [t("services.matrix.readTimeoutHeader")]: "readTimeout",
        };
        parsedData = await this.excelExporter.parseExcel(path, keyMapping);
      }

      logger.info("[MatrixService] importDeviceList result", parsedData.length);
      logger.info(
        `[MatrixService] importDeviceList - ${t("services.matrix.importDeviceListComplete")}:`,
        parsedData.length
      );
      return parsedData;
    } catch (error) {
      logger.error("[MatrixService] importDeviceList error", error);
      throw error;
    }
  }

  // 异步导出exportDeviceList，成功返回true，失败返回false
  async exportDeviceList(req: IECReq<any>): Promise<boolean> {
    logger.info(
      `[MatrixService] ${t("services.matrix.exportDeviceListEntry")}:`,
      req
    );
    try {
      logger.debug(
        `[MatrixService] exportDeviceList - ${t("services.matrix.exportDeviceListStart")}`
      );
      const columns: Column[] = [
        { header: t("services.matrix.indexHeader"), key: "index", width: 10 },
        {
          header: t("services.matrix.deviceNameHeader"),
          key: "name",
          width: 20,
        },
        {
          header: t("services.matrix.deviceAddressHeader"),
          key: "ip",
          width: 30,
        },
        {
          header: t("services.matrix.devicePortHeader"),
          key: "port",
          width: 15,
        },
        {
          header: t("services.matrix.encryptedHeader"),
          key: "encrypted",
          width: 15,
        },
        {
          header: t("services.matrix.downFileHeader"),
          key: "downFile",
          width: 15,
        },
        {
          header: t("services.matrix.importParamHeader"),
          key: "importParam",
          width: 15,
        },
        {
          header: t("services.matrix.connectTimeoutHeader"),
          key: "connectTimeout",
          width: 15,
        },
        {
          header: t("services.matrix.paramTimeoutHeader"),
          key: "paramTimeout",
          width: 10,
        },
        {
          header: t("services.matrix.readTimeoutHeader"),
          key: "readTimeout",
          width: 10,
        },
      ];

      const { path, deviceItems } = req.data;

      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToExcel(
          deviceItems,
          columns,
          path,
          t("services.matrix.deviceListSheetName")
        );
      }
      logger.info(
        `[MatrixService] exportDeviceList - ${t("services.matrix.exportDeviceListComplete")}`
      );
      return true;
    } catch (error) {
      logger.error("[MatrixService] exportDeviceList error", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async addDownFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.addDownFileStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] addDownFile - Error occurred", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async deleteDownFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.deleteDownFileStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] deleteDownFile - Error occurred", error);
      throw error;
    }
  }
  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async updateDownFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.updateDownFileStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] updateDownFile - Error occurred", error);
      throw error;
    }
  }

  async importDownloadList(req: IECReq<any>): Promise<any[]> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.importDownloadListStart")}`
      );
      const { path } = req.data;
      let parsedData: any[] = [];
      if (String(path).endsWith("xlsx")) {
        const keyMapping = {
          [t("services.matrix.fileNameHeader")]: "fileName",
          [t("services.matrix.fileSizeHeader")]: "fileSizeAs",
          [t("services.matrix.filePathHeader")]: "path",
          [t("services.matrix.lastModifiedHeader")]: "lastModified",
        };
        parsedData = await this.excelExporter.parseExcel(path, keyMapping);
      }

      logger.info(
        `[MatrixService] ${t("services.matrix.importDownloadListResult")}`,
        parsedData.length
      );
      return parsedData;
    } catch (error) {
      logger.error("[MatrixService] importDownloadList error", error);
      throw error;
    }
  }

  // 异步导出exportDownloadList，成功返回true，失败返回false
  async exportDownloadList(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.exportDownloadListStart")}`
      );
      const columns: Column[] = [
        { header: t("services.matrix.indexHeader"), key: "index", width: 10 },
        {
          header: t("services.matrix.fileNameHeader"),
          key: "fileName",
          width: 20,
        },
        {
          header: t("services.matrix.fileSizeHeader"),
          key: "fileSizeAs",
          width: 30,
        },
        { header: t("services.matrix.filePathHeader"), key: "path", width: 40 },
        {
          header: t("services.matrix.lastModifiedHeader"),
          key: "lastModified",
          width: 40,
        },
      ];

      const { path, fileItems } = req.data;

      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToExcel(
          fileItems,
          columns,
          path,
          t("services.matrix.downloadFileListSheetName")
        );
      }
      return true;
    } catch (error) {
      logger.error("[MatrixService] exportDownloadList error", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async deleteParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.deleteParamStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] deleteParam - Error occurred", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async updateParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.updateParamStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] updateParam - Error occurred", error);
      throw error;
    }
  }

  async importParamList(req: IECReq<any>): Promise<AllParamItem[]> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.importParamListStart")}`
      );

      const { path } = req.data;
      const allParamItem: AllParamItem[] = [];
      // const paramItem: ParamItem[] = [];
      let parsedData: ImportResult = [];
      if (String(path).endsWith("xlsx")) {
        const keyMapping = {
          [t("services.matrix.paramNameHeader")]: "paramName",
          [t("services.matrix.paramDescHeader")]: "paramDesc",
          [t("services.matrix.valueHeader")]: "value",
          [t("services.matrix.minValueHeader")]: "minValue",
          [t("services.matrix.maxValueHeader")]: "maxValue",
          [t("services.matrix.stepHeader")]: "step",
          [t("services.matrix.unitHeader")]: "unit",
        };
        parsedData = await this.excelExporter.parseMultiSheets(path, {
          keyMapping,
        });
      } else if (String(path).endsWith("csv")) {
        const columns: Column[] = [
          {
            header: t("services.matrix.paramNameHeader"),
            key: "paramName",
            width: 20,
          },
          {
            header: t("services.matrix.paramDescHeader"),
            key: "paramDesc",
            width: 15,
          },
          { header: t("services.matrix.valueHeader"), key: "value", width: 20 },
          {
            header: t("services.matrix.minValueHeader"),
            key: "minValue",
            width: 15,
          },
          {
            header: t("services.matrix.maxValueHeader"),
            key: "maxValue",
            width: 20,
          },
          { header: t("services.matrix.stepHeader"), key: "step", width: 15 },
          { header: t("services.matrix.unitHeader"), key: "unit", width: 20 },
        ];
        parsedData = await CsvHandler.importMuti(path, columns);
      } else if (String(path).endsWith("xml")) {
        const columns: Column[] = [
          {
            header: t("services.matrix.paramNameHeader"),
            key: "paramName",
            width: 20,
          },
          {
            header: t("services.matrix.paramDescHeader"),
            key: "paramDesc",
            width: 15,
          },
          { header: t("services.matrix.valueHeader"), key: "value", width: 20 },
          {
            header: t("services.matrix.minValueHeader"),
            key: "minValue",
            width: 15,
          },
          {
            header: t("services.matrix.maxValueHeader"),
            key: "maxValue",
            width: 20,
          },
          { header: t("services.matrix.stepHeader"), key: "step", width: 15 },
          { header: t("services.matrix.unitHeader"), key: "unit", width: 20 },
        ];
        parsedData = await XmlHandler.importMultiGroups(path, columns);
      }

      parsedData.forEach((item) => {
        item.data?.forEach((ditem) => {
          ditem.grpName = item.grpname;
        });
        allParamItem.push({
          grpname: item.grpname || "",
          data: item.data || [],
        });
      });
      return allParamItem;
    } catch (error) {
      logger.error("[MatrixService] importParamList error", error);
      throw error;
    }
  }

  // 异步导出exportParanList，成功返回true，失败返回false
  async exportParanList(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.exportParamListStart")}`
      );

      const { path, paramItems } = req.data;

      const result: SheetData[] = paramItems;
      const columns: Column[] = [
        { header: t("services.matrix.indexHeader"), key: "index", width: 10 },
        {
          header: t("services.matrix.paramNameHeader"),
          key: "paramName",
          width: 20,
        },
        {
          header: t("services.matrix.paramDescHeader"),
          key: "paramDesc",
          width: 30,
        },
        { header: t("services.matrix.valueHeader"), key: "value", width: 15 },
        {
          header: t("services.matrix.minValueHeader"),
          key: "minValue",
          width: 15,
        },
        {
          header: t("services.matrix.maxValueHeader"),
          key: "maxValue",
          width: 15,
        },
        { header: t("services.matrix.stepHeader"), key: "step", width: 10 },
        { header: t("services.matrix.unitHeader"), key: "unit", width: 10 },
      ];

      if (String(path).endsWith("xlsx")) {
        await this.excelExporter.exportToMutilSheetExcel(result, columns, path);
      } else if (String(path).endsWith("csv")) {
        await CsvHandler.exportMuti(result, columns, path);
      } else if (String(path).endsWith("xml")) {
        await XmlHandler.exportMutiSheet(result, columns, path);
      }
      return true;
    } catch (error) {
      logger.error("[MatrixService] exportParanList error", error);
      throw error;
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async clearData(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info(
        `[MatrixService] ${t("services.matrix.clearDataStart")}`,
        req
      );
      // 新增装置
      const result = await matrixService.clearData(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixService] clearData - Error occurred", error);
      throw error;
    }
  }

  /**
   *  打包文件列表到指定目录
   * @returns {Promise<boolean>}
   */
  async handlePackage(req: IECReq<any>): Promise<string> {
    logger.info(
      `[MatrixService] ${t("services.matrix.handlePackageEntry")}:`,
      req
    );
    try {
      const { saveDir, fileList } = req.data;
      const os = require("os");
      const fs = require("fs");
      const path = require("path");
      const now = new Date();
      const pad = (n) => n.toString().padStart(2, "0");
      const dateStr = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
      // 使用操作系统临时目录
      const tempDir = os.tmpdir();
      const folderPath = `UPAD_Ver_${dateStr}`;
      const upadDir = path.join(tempDir, folderPath);
      if (!fs.existsSync(upadDir)) {
        fs.mkdirSync(upadDir, { recursive: true });
      }
      const devDir = path.join(upadDir, "dev");
      const prjDir = path.join(upadDir, "prj");
      const runDir = path.join(upadDir, "run");
      [devDir, prjDir, runDir].forEach((dir) => {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
      });
      for (const file of fileList) {
        const stat = fs.lstatSync(file.path);
        if (stat.isDirectory()) {
          // 目标目录：runDir 下以文件夹名为子目录
          const folderName = path.basename(file.path);
          const destDir = path.join(runDir, folderName);
          const copyDirRecursive = (src, dest) => {
            if (!fs.existsSync(dest)) {
              fs.mkdirSync(dest, { recursive: true });
            }
            const entries = fs.readdirSync(src, { withFileTypes: true });
            for (const entry of entries) {
              const srcPath = path.join(src, entry.name);
              const destPath = path.join(dest, entry.name);
              if (entry.isDirectory()) {
                copyDirRecursive(srcPath, destPath);
              } else {
                fs.copyFileSync(srcPath, destPath);
              }
            }
          };
          copyDirRecursive(file.path, destDir);
          continue;
        }
        const fileName = path.basename(file.path);
        if (fileName === "boot.img") {
          const dest = path.join(upadDir, fileName);
          fs.copyFileSync(file.path, dest);
        } else if (fileName === "rootfs.img") {
          const dest = path.join(upadDir, fileName);
          fs.copyFileSync(file.path, dest);
        } else {
          const dest = path.join(runDir, fileName);
          fs.copyFileSync(file.path, dest);
        }
      }
      // zip 文件也放在临时目录
      const zipName = `UPAD_Ver_${dateStr}.zip`;
      const zipPath = path.join(tempDir, zipName);
      const encryptExe = IECCONSTANTS.PATH_ZIP_ENCRYPT;

      const batDir = path.dirname(encryptExe);
      const batPath = path.join(batDir, "run_encrypt.bat");
      // 构建命令，bat 文件依然放在 exe 目录，参数用临时目录
      const cmdLines = [
        "chcp 65001", // 用UTF-8编码，兼容编辑器和cmd
        `cd /d "${batPath}"`,
        `"${encryptExe}" "${upadDir}" "${zipPath}"`,
      ];
      // 用 UTF-8 编码写入 bat 文件
      fs.writeFileSync(batPath, cmdLines.join("\r\n"), "utf8");

      logger.info(
        `[MatrixService] handlePackage - ${t("services.matrix.generateBatFile")}: ${batPath}`
      );

      // 执行 bat 文件
      await new Promise((resolve, reject) => {
        const { exec } = require("child_process");
        exec(
          `cmd /c "${batPath}"`,
          {
            encoding: "buffer",
            shell: "cmd.exe",
          },
          (error, stdout, stderr) => {
            const output = stdout ? stdout.toString("utf8") : "";
            const errOutput = stderr ? stderr.toString("utf8") : "";
            // 执行完毕后删除 bat 文件
            try {
              fs.unlinkSync(batPath);
            } catch (e) {
              logger.warn(t("services.matrix.deleteBatFileFailed"), e);
            }
            if (error) {
              logger.error(
                `[MatrixService] handlePackage - ${t("services.matrix.batExecutionFailed")}: ${errOutput}`
              );
              reject(error);
            } else {
              logger.info(
                `[MatrixService] handlePackage - ${t("services.matrix.batExecutionSuccess")}: ${output}`
              );
              resolve(true);
            }
          }
        );
      });
      logger.info(
        `[MatrixService] handlePackage - ${t("services.matrix.encryptCompressionComplete")}`
      );
      // 检查临时目录下的 zip 文件
      if (!fs.existsSync(zipPath)) {
        logger.error(
          `[MatrixService] handlePackage - ${t("services.matrix.compressionPackageNotGenerated")}: ${zipPath}`
        );
        throw new Error(
          t("services.matrix.compressionPackageGenerationFailed")
        );
      }
      const destZipPath = path.join(saveDir, zipName);
      fs.copyFileSync(zipPath, destZipPath);
      if (fs.existsSync(zipPath)) {
        fs.unlinkSync(zipPath);
      }
      const deleteFolderRecursive = (dirPath) => {
        if (fs.existsSync(dirPath)) {
          fs.readdirSync(dirPath).forEach((file) => {
            const curPath = path.join(dirPath, file);
            if (fs.lstatSync(curPath).isDirectory()) {
              deleteFolderRecursive(curPath);
            } else {
              fs.unlinkSync(curPath);
            }
          });
          fs.rmdirSync(dirPath);
        }
      };
      deleteFolderRecursive(upadDir);
      // 返回打包后的zip文件路径
      return destZipPath;
    } catch (error) {
      logger.error("[MatrixService] handlePackage - Error occurred", error);
      throw error;
    }
  }
}

MatrixService.toString = () => "[class MatrixService]";
const matrixService = new MatrixService();

export { MatrixService, matrixService };
