import { SysUser } from "@/api";

import { ReqPage } from "@/api";

export namespace SysOrg {
  /** 组织分页查询 */
  export interface Page extends ReqPage {}

  /** 组织树查询 */
  export interface ReqTree {
    parentId?: number | string;
  }

  /** 组织信息 */
  export interface SysOrgInfo {
    /** 组织id */
    id: number | string;
    /** 父ID */
    parentId: number | string;
    /** 主管Id */
    directorId: number | string | null;
    /** 组织名称 */
    name: string;
    /** 组织全称 */
    names: string;
    /** 组织编码 */
    code: string;
    /** 组织分类 */
    category: string;
    /** 状态 */
    status: string;
    /** 排序码 */
    sortCode: number;
    /** 主管信息 */
    directorInfo: SysUser.SysUserInfo | null;
  }
  /** 组织树 */
  export interface SysOrgTree {
    /** 组织id */
    id: number | string;
    /** 父ID */
    parentId: number | string;
    /** 组织名称 */
    name: string;
    /** 子集 */
    children: SysOrgTree[];
  }
}
