<!-- eslint-disable vue/html-closing-bracket-newline -->
<template>
  <el-tabs>
    <el-tab-pane :label="t(`hmi.graphProperties.node.nodeProperty`)">
      <el-form :label-width="80">
        <el-collapse>
          <el-collapse-item :title="t(`hmi.graphProperties.node.style`)" name="1">
            <el-form-item :label="t(`hmi.graphProperties.node.backgroundColor`)">
              <color-picker :color="form.bgColor" @color-picker="onBackgroundChange"></color-picker>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.borderWidth`)">
              <el-input-number v-model="form.borderWidth" :min="0" @change="onBorder(Attrs.BORDER_WIDTH)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.borderColor`)">
              <color-picker :color="form.borderColor" @color-picker="onBorderColor"></color-picker>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.borderDasharray`)">
              <el-input v-model="form.borderDasharray" @change="onBorder(Attrs.BORDER_DASHARRAY)"></el-input>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.rx`)">
              <el-input-number v-model="form.rx" :min="0" @change="onBorder(Attrs.RX)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.ry`)">
              <el-input-number v-model="form.ry" :min="0" @change="onBorder(Attrs.RY)"></el-input-number>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :title="t(`hmi.graphProperties.node.position`)">
            <el-form-item :label="t(`hmi.graphProperties.node.width`)">
              <el-input-number v-model="form.width" :min="0" @change="onBorder(Attrs.WIDTH)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.height`)">
              <el-input-number v-model="form.height" :min="0" @change="onBorder(Attrs.HEIGHT)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.x`)">
              <el-input-number v-model="form.x" :min="0" @change="onBorder(Attrs.X)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.y`)">
              <el-input-number v-model="form.y" :min="0" @change="onBorder(Attrs.Y)"></el-input-number>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :title="t(`hmi.graphProperties.node.property`)">
            <el-form-item :label="t(`hmi.graphProperties.node.angle`)">
              <el-input-number v-model="form.angle" :min="0" :max="360" @change="onBorder(Attrs.ANGLE)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.zIndex`)">
              <el-input-number v-model="form.zIndex" @change="onBorder(Attrs.ZINDEX)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.fontFamily`)">
              <el-input v-model="form.fontValue.fontFamily" @change="onBorder(Attrs.FONT_FAMILY)"></el-input>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.fontColor`)">
              <color-picker :color="form.fontValue.fontColor" @color-picker="onFontColor"></color-picker>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.fontSize`)">
              <el-input-number v-model="form.fontValue.fontSize" @change="onBorder(Attrs.FONT_SIZE)"></el-input-number>
            </el-form-item>
            <el-form-item :label="t(`hmi.graphProperties.node.text`)">
              <el-input v-model="form.fontValue.text" @change="onBorder(Attrs.TEXT)"></el-input>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { inject, Ref, ref } from "vue";
import ColorPicker from "./ColorPicker.vue";
import { Attrs, EventTypeParams, EventTypeParamsName } from "../../graph/Graph";
import { getNodeAttrValue, isTextShape } from "../../graph/GraphUtil";
import { Graph, Node } from "@antv/x6";
import { DataPlugin } from "../../graph/graphplugin/dataplugin";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const eventTypeParams = inject(EventTypeParamsName) as Ref<EventTypeParams>;
const node = eventTypeParams.value.eventParam.node as Node;
const graph = eventTypeParams.value.eventParam.graph as Graph;
const nodeAttrValue = getNodeAttrValue(node);
const form = ref(nodeAttrValue);
const dataplugin = graph.getPlugin("data") as DataPlugin;

const onBackgroundChange = (color: string) => {
  if (!node) {
    return;
  }
  if (!color) {
    color = "none";
  }
  node.setAttrByPath("body/fill", color);
};
const onBorderColor = (color: string) => {
  form.value.borderColor = color;
  onBorder(Attrs.BORDER_COLOR);
};
const onFontColor = (color: string) => {
  form.value.fontValue.fontColor = color;
  onBorder(Attrs.FONT_COLOR);
};
const onBorder = (type: Attrs) => {
  if (setFont(type)) {
    return;
  }
  switch (type) {
    case Attrs.BORDER_COLOR:
      node.setAttrByPath("body/stroke", form.value.borderColor);
      break;
    case Attrs.BORDER_WIDTH:
      node.setAttrByPath("body/strokeWidth", form.value.borderWidth);
      break;
    case Attrs.BORDER_DASHARRAY:
      node.setAttrByPath("body/strokeDasharray", form.value.borderDasharray);
      break;
    case Attrs.RX:
      node.setAttrByPath("body/rx", form.value.rx);
      break;
    case Attrs.RY:
      node.setAttrByPath("body/ry", form.value.ry);
      break;
    case Attrs.WIDTH:
      const size = node.getSize();
      size.width = form.value.width;
      node.setSize(size);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.HEIGHT:
      const sizeHeight = node.getSize();
      sizeHeight.height = form.value.height;
      node.setSize(sizeHeight);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.X:
      const position = node.getPosition();
      position.x = form.value.x;
      node.setPosition(position);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.Y:
      const positionY = node.getPosition();
      positionY.y = form.value.y;
      node.setPosition(positionY);
      dataplugin.getData().operator.resize(node, graph);
      break;
    case Attrs.ANGLE:
      // 记录旋转前的角度
      dataplugin.getData().operator.rotate(node, dataplugin.getData().group);
      node.rotate(form.value.angle, { absolute: true });
      dataplugin.getData().operator.rotating(node, dataplugin.getData().group);
      break;
    case Attrs.ZINDEX:
      node.setZIndex(form.value.zIndex);
      break;
    default:
      break;
  }
};
const setFont = (type: Attrs): boolean => {
  switch (type) {
    case Attrs.FONT_FAMILY:
      node.setAttrByPath("label/style/fontFamily", form.value.fontValue.fontFamily);
      node.setAttrByPath("text/fontFamily", form.value.fontValue.fontFamily);
      break;
    case Attrs.FONT_SIZE:
      node.setAttrByPath("label/style/fontSize", form.value.fontValue.fontSize);
      node.setAttrByPath("text/fontSize", form.value.fontValue.fontSize);
      break;
    case Attrs.FONT_COLOR:
      // 区分下是否是文本框
      if (isTextShape(node)) {
        node.setAttrByPath("label/style/color", form.value.fontValue.fontColor);
      }
      node.setAttrByPath("label/fill", form.value.fontValue.fontColor);
      node.setAttrByPath("text/fill", form.value.fontValue.fontColor);
      break;
    case Attrs.TEXT:
      node.setAttrByPath("label/text", form.value.fontValue.text);
      node.setAttrByPath("text/text", form.value.fontValue.text);
      break;
    default:
      return false;
  }
  return true;
};
</script>
