<template>
  <div class="activate-container flx-center">
    <div class="window-controls">
      <ToolBarRight />
    </div>
    <div class="activate-box">
      <div class="activate-form">
        <div class="title-bar">
          <div class="title-content">
            <img class="activate-icon" src="@/assets/images/logo.png" alt="" />
            <h2 class="logo-text">Sieyuan-VisualDebug</h2>
          </div>
        </div>
        <div class="form-content">
          <div class="form-item-row">
            <span class="form-label">{{ $t("activate.machineCode") }}</span>
            <el-input v-model="machineCode" readonly class="machine-code-input" />
          </div>
          <div class="form-item-row">
            <span class="form-label">{{ $t("activate.activationCode") }}</span>
            <el-input
              v-model="activateForm.activationCode"
              type="textarea"
              :rows="8"
              :placeholder="$t('activate.activationCodePlaceholder')"
              class="activation-code-textarea"
            />
          </div>
          <div class="activate-btn">
            <el-button :icon="CircleClose" round size="large" @click="resetForm()">{{ $t("activate.reset") }}</el-button>
            <el-button :icon="Check" round size="large" type="primary" :loading="loading" @click="activate()">
              {{ $t("activate.activate") }}
            </el-button>
          </div>
          <div v-if="loading" class="loading-progress">
            <div class="progress-bar">
              <div class="progress-inner"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="activate">
import { ref, reactive, onMounted } from "vue";

import { HOME_URL } from "@/config";
import { ElNotification, ElMessage } from "element-plus";
import { useActivateStore } from "@/stores/modules/activate";
import { CircleClose, Check } from "@element-plus/icons-vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import { licenseApi } from "@/api/modules/sys/activate/license";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const router = useRouter();
const activateStore = useActivateStore();

const loading = ref(false);
const activateForm = reactive({
  activationCode: ""
});

const machineCode = ref("");

onMounted(async () => {
  await getMacheCode();
});

const activate = async () => {
  if (!activateForm.activationCode) {
    ElMessage.warning(t("activate.activationCodePlaceholder"));
    return;
  }

  loading.value = true;
  try {
    const result = await licenseApi.activate(activateForm.activationCode);
    if (result.code == 0) {
      activateStore.setActivated(true);
      router.push(HOME_URL);
      ElNotification({
        title: t("activate.success.title"),
        message: t("activate.success.message"),
        type: "success",
        duration: 2000
      });
    } else {
      ElMessage.error(t("activate.error.unknown", { msg: result.msg || t("common.error.unknown") }));
    }
  } catch (error) {
    console.error(t("activate.error.unknown", { msg: error }));
    ElMessage.error(t("activate.error.network"));
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  activateForm.activationCode = "";
};

const getMacheCode = async () => {
  try {
    const result = await licenseApi.getMachineCode();
    if (result.code == 0) {
      machineCode.value = String(result.data);
    } else {
      ElMessage.error(t("activate.error.unknown", { msg: result.msg || t("common.error.unknown") }));
    }
  } catch (error) {
    console.error(t("activate.error.unknown", { msg: error }));
    ElMessage.error(t("activate.error.network"));
  }
};
</script>

<style scoped lang="scss">
.activate-container {
  position: relative;
  height: 100%;
  min-height: 500px;
  overflow: hidden;
  background-color: #f5f7fa;
  background-image: url("@/assets/images/login_bg.svg");
  background-size: cover;
  animation: fade-in 0.5s ease-in-out;
  .activate-box {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 1000;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 600px;
    max-width: 600px;
    height: auto;
    padding: 0;
    user-select: none;
    background-color: rgb(255 255 255 / 90%);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgb(0 0 0 / 12%);
    transform: translate(-50%, -50%);
    &:hover {
      box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
    }
    .activate-form {
      display: flex;
      flex-direction: column;
      width: 100%;
      overflow: hidden;
      background-color: var(--el-bg-color);
      border-radius: 12px;
      box-shadow: 0 4px 16px rgb(0 0 0 / 5%);
      animation: slide-up 0.5s ease-out;
      .title-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 70px;
        padding: 0 30px;
        user-select: none;
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        transition: background-color 0.3s ease;
        &:hover {
          background: linear-gradient(135deg, #bbdefb, #90caf9);
        }
        &:active {
          background: linear-gradient(135deg, #90caf9, #64b5f6);
        }
        .title-content {
          display: flex;
          gap: 15px;
          align-items: center;
          justify-content: center;
          .activate-icon {
            width: 40px;
            height: 40px;
            transition: transform 0.3s ease;
            &:hover {
              transform: scale(1.1);
            }
          }
          .logo-text {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            text-shadow: 0 2px 4px rgb(0 0 0 / 10%);
            white-space: nowrap;
          }
        }
      }
      .form-content {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 40px 40px 45px;
        .form-item-row {
          display: flex;
          align-items: flex-start;
          width: 100%;
          margin-bottom: 30px;
          animation: fade-in-up 0.6s ease-out;
          .form-label {
            flex-shrink: 0;
            min-width: 90px;
            margin-right: 20px;
            font-size: 16px;
            font-weight: 600;
            line-height: 40px;
            color: #303133;
            text-align: right;
          }
          .machine-code-input {
            flex: 1;
            width: 0;
            height: 48px;
            margin-bottom: 0;
            font-size: 15px;
            line-height: 48px;
            letter-spacing: 0.5px;
            background: #f5f7fa;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover,
            &:focus {
              background: #eef1f6;
              box-shadow: 0 0 0 2px rgb(26 115 232 / 10%);
            }
          }
          .activation-code-textarea {
            flex: 1;
            width: 0;
            min-height: 140px;
            font-size: 15px;
            letter-spacing: 0.5px;
            resize: vertical;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover,
            &:focus {
              box-shadow: 0 0 0 2px rgb(26 115 232 / 10%);
            }
          }
        }
        .activate-btn {
          display: flex;
          gap: 25px;
          align-items: center;
          justify-content: center;
          width: 100%;
          margin-top: 35px;
          white-space: nowrap;
          animation: fade-in-up 0.8s ease-out;
          .el-button {
            width: 180px;
            height: 48px;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 0.5px;
            border-radius: 24px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 6px 16px rgb(26 115 232 / 20%);
              transform: translateY(-2px);
            }
            &:active {
              transform: translateY(0);
            }
            &.is-loading {
              background: linear-gradient(90deg, #1a73e8 0%, #2196f3 100%);
              border: none;
              .el-icon {
                font-size: 18px;
                animation: rotate 1s linear infinite;
              }
            }
          }
          .el-button--primary {
            background: linear-gradient(135deg, #1a73e8, #0d47a1);
            border: none;
            &:hover {
              background: linear-gradient(135deg, #2196f3, #1565c0);
            }
            &:active {
              background: linear-gradient(135deg, #1976d2, #0d47a1);
            }
          }
        }
      }
    }
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.window-controls {
  position: absolute;
  top: 2px;
  right: 8px;
  display: flex;
  gap: 6px;
  .el-button {
    padding: 6px;
    font-size: 14px;
  }
}
.loading-progress {
  width: 200px;
  margin: 20px auto 0;
}
.progress-bar {
  width: 100%;
  height: 3px;
  overflow: hidden;
  background: rgb(26 115 232 / 10%);
  border-radius: 3px;
}
.progress-inner {
  position: relative;
  width: 100%;
  height: 100%;
  background: #1a73e8;
  animation: progress-bar 1.5s ease-in-out infinite;
}

@keyframes progress-bar {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media screen and (width <= 600px) {
  .activate-form {
    width: 95% !important;
    padding: 30px 30px 35px !important;
  }
  .form-item-row {
    flex-direction: column;
    align-items: flex-start;
    .form-label {
      margin-bottom: 8px;
      text-align: left;
    }
  }
}
</style>
