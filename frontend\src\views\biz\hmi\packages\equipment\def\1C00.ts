import { calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "1C00",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 3,
        y1: 42.33,
        x2: 3,
        y2: 9.67
      }
    },
    {
      tagName: "polygon",
      groupSelector: "triangle",
      attrs: {
        points: calculateTriangle(0, 0, 6, 9.33)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    triangle: {
      stroke: "#000"
    }
  }
};

export default e;
