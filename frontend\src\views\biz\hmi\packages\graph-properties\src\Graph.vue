<template>
  <el-tabs>
    <el-tab-pane :label="t(`hmi.graphProperties.graph.canvasSetting`)">
      <el-form :label-width="90">
        <el-form-item :label="t(`hmi.graphProperties.graph.grid`)">
          <el-checkbox v-model="form.grid.show" @change="onGridChange"></el-checkbox>
        </el-form-item>
        <el-form-item :label="t(`hmi.graphProperties.graph.backgroundColor`)">
          <color-picker :color="form.bg.color" @color-picker="onBackgroundChange"></color-picker>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { inject, Ref } from "vue";
import { ref } from "vue";
import ColorPicker from "./ColorPicker.vue";
import { EventTypeParams, EventTypeParamsName } from "../../graph/Graph";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const eventTypeParams = inject(EventTypeParamsName) as Ref<EventTypeParams>;
const graph = eventTypeParams.value.eventParam.graph;
const form = ref({
  bg: {
    color: "rgba(0, 0, 0, 1)"
  },
  grid: {
    show: false
  }
});
if (graph) {
  if (graph.options.background && graph.options.background.color) {
    form.value.bg.color = graph.options.background.color as string;
  }
  form.value.grid.show = graph.options.grid.visible;
}

const onGridChange = () => {
  if (graph) {
    if (form.value.grid.show) {
      graph.showGrid();
    } else {
      graph.hideGrid();
    }
  }
};
const onBackgroundChange = (color: string) => {
  if (!graph) {
    return;
  }
  graph.drawBackground({
    color: color
  });
};
</script>
