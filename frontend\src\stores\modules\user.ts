import { defineStore } from "pinia";
import { Login } from "@/api/interface";
import piniaPersistConfig from "@/stores/helper/persist";
import { ElNotification } from "element-plus";

const name = "simple-user"; // 定义模块名称

/* UserState */
export interface UserState {
  /** token */
  accessToken: string;
  /** 刷新token */
  refreshToken: string;
  /** 用户信息 */
  userInfo: Login.LoginUserInfo | null;
  /** 默认模块 */
  defaultModule: number | string | null;
  /** 选择模块 */
  chooseModule: number | string | null;
  /** 模块列表 */
  moduleList: Login.ModuleInfo[];
}

/** 用户模块 */
export const useUserStore = defineStore({
  id: name,
  state: (): UserState => ({
    accessToken: "",
    refreshToken: "",
    userInfo: null,
    defaultModule: null,
    chooseModule: null,
    moduleList: []
  }),
  getters: {
    userInfoGet: state => state.userInfo,
    chooseModuleGet: state => state.chooseModule
  },
  actions: {
    // Set Token
    setToken(token: string, refreshToken: string) {
      this.accessToken = token;
      this.refreshToken = refreshToken;
    },
    async getUserInfo() {
      /**  获取用户信息 */
      const { data } = await loginApi.getLoginUser();
      if (data) {
        this.setUserInfo(data);
      } else {
        ElNotification({
          title: "系统错误",
          message: "获取个人信息失败，请联系系统管理员！",
          type: "warning",
          duration: 3000
        });
      }
      return this.userInfo;
    },
    /** 设置用户信息 */
    setUserInfo(userInfo: Login.LoginUserInfo) {
      this.userInfo = userInfo;
      this.defaultModule = userInfo.defaultModule;
      this.moduleList = userInfo.moduleList;
    },
    /** 设置用户签名 */
    setSignature(signature: string) {
      this.userInfo.signature = signature;
    },
    /** 设置用户单个属性 */
    setUserInfoItem(key: string, value: any) {
      this.userInfo[key] = value;
    },
    /** 清除token */
    clearToken() {
      this.accessToken = "";
      this.refreshToken = "";
    },
    /** 清理用户信息 */
    clearUserStore() {
      this.clearToken();
      this.userInfo = null;
      this.defaultModule = null;
      this.moduleList = [];
    },
    /** 选择模块 */
    setModule(moduleId: number | string | null) {
      this.chooseModule = moduleId;
    }
  },
  persist: piniaPersistConfig(name)
});
