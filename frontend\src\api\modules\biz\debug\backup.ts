import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/debug/backup/");

const backupApi = {
  /** 一键备份（指定 deviceId） */
  oneKeyBackupByDevice(deviceId: string, params: { backupRoot: string; types: string[]; taskId: string }) {
    return ipc.iecInvokeWithDevice<any>("oneKeyBackup", params, deviceId);
  },
  /** 取消备份（指定 deviceId） */
  cancelBackupByDevice(deviceId: string, params: { id: string }) {
    return ipc.iecInvokeWithDevice<any>("cancelBackup", params, deviceId);
  }
};

export { backupApi };
