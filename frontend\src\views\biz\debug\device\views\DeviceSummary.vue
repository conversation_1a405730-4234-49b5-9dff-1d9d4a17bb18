<template>
  <div class="dataVisualize-box">
    <div class="card top-box">
      <div class="top-title">{{ t("device.summary.title") }}</div>
      <el-tabs v-model="tabActive" class="demo-tabs">
        <el-tab-pane v-for="item in tab" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
      </el-tabs>
      <div class="top-content">
        <el-row :gutter="40">
          <el-col class="mb40" :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
            <div class="item-left sle">
              <span class="left-title">{{ t("device.summary.settingTotal") }}</span>
              <div class="img-box">
                <img src="@/assets/debug/images/param.png" alt="" />
              </div>
              <span class="left-number">{{ summaryInfo?.settingNum }}</span>
            </div>
          </el-col>
          <el-col class="mb40" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-center">
              <div class="gitee-traffic traffic-box">
                <div class="traffic-img">
                  <img src="@/assets/debug/images/yaoce.png" alt="" />
                </div>
                <span class="item-value">{{ summaryInfo?.ycNum }}</span>
                <span class="traffic-name sle">{{ t("device.summary.telemetry") }}</span>
              </div>
              <div class="gitHub-traffic traffic-box">
                <div class="traffic-img">
                  <img src="@/assets/debug/images/yaoxin.png" alt="" />
                </div>
                <span class="item-value">{{ summaryInfo?.yxNum }}</span>
                <span class="traffic-name sle">{{ t("device.summary.teleindication") }}</span>
              </div>
              <div class="today-traffic traffic-box">
                <div class="traffic-img">
                  <img src="@/assets/debug/images/yaokong.png" alt="" />
                </div>
                <span class="item-value">{{ summaryInfo?.ykNum }}</span>
                <span class="traffic-name sle">{{ t("device.summary.telecontrol") }}</span>
              </div>
              <div class="yesterday-traffic traffic-box">
                <div class="traffic-img">
                  <img src="@/assets/debug/images/drive.png" alt="" />
                </div>
                <span class="item-value">{{ summaryInfo?.driveNum }}</span>
                <span class="traffic-name sle">{{ t("device.summary.driveOutput") }}</span>
              </div>
            </div>
          </el-col>
          <el-col class="mb40" :xs="24" :sm="24" :md="24" :lg="10" :xl="10">
            <div class="item-right">
              <div class="echarts-title">{{ t("device.summary.settingRatio") }}</div>
              <div class="book-echarts">
                <SummaryPie ref="pieRef" :device-id="props.deviceId" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="dataVisualize">
import { defineAsyncComponent, computed } from "vue";
const SummaryPie = defineAsyncComponent(() => import("../components/SummaryPie.vue"));
import { deviceSummaryApi } from "@/api/modules/biz/debug/devicesummary";
import { SummaryInfo } from "@/api/interface/biz/debug/devicesummary";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tabActive = ref(1);

const tab = computed(() => [{ label: t("device.summary.basicInfo"), name: 1 }]);

const summaryInfo = ref<SummaryInfo>();
// 简单缓存，避免重复查询同一装置时的等待
const summaryInfoCache = new Map<string, any>();

const props = defineProps<{ deviceId: string }>();

onMounted(async () => {
  // 优先从缓存读取，减少重复请求
  let result = summaryInfoCache.get(props.deviceId);
  if (!result) {
    result = await deviceSummaryApi.getSummaryInfoByDevice(props.deviceId);
    summaryInfoCache.set(props.deviceId, result);
  }
  if (result.code == 0) {
    summaryInfo.value = result.data;
  }
});
</script>

<style scoped lang="scss">
.dataVisualize-box {
  .top-box {
    box-sizing: border-box;
    padding: 25px 40px 0;
    margin-bottom: 10px;
    .top-title {
      margin-bottom: 10px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .top-content {
      margin-top: 10px;
      .item-left {
        box-sizing: border-box;
        height: 100%;
        padding: 40px 0 30px 30px;
        overflow: hidden;
        color: #ffffff;
        background: url("@/assets/debug/images/book-bg.png");
        background-position: 50%;
        background-size: cover;
        border-radius: 20px;
        .left-title {
          font-family: DIN;
          font-size: 20px;
        }
        .img-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;
          margin: 40px 0 20px;
          background-color: #ffffff;
          border-radius: 20px;
          box-shadow: 0 10px 20px rgb(0 0 0 / 14%);
          img {
            width: 60px;
            height: 65px;
          }
        }
        .left-number {
          overflow: hidden;
          font-family: DIN;
          font-size: 62px;
        }
      }
      .item-center {
        display: flex;
        flex-wrap: wrap;
        place-content: space-between space-between;
        height: 100%;
        .traffic-box {
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          width: 47%;
          height: 48%;
          padding: 25px;
          border-radius: 30px;
          .traffic-img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 70px;
            height: 70px;
            margin-bottom: 10px;
            background-color: #ffffff;
            border-radius: 19px;
          }
        }
        img {
          width: 33px;
          height: 33px;
        }
        .item-value {
          margin-bottom: 4px;
          font-family: DIN;
          font-size: 28px;
          font-weight: bold;
          color: #1a1a37;
        }
        .traffic-name {
          overflow: hidden;
          font-family: DIN;
          font-size: 15px;
          color: #1a1a37;
          white-space: nowrap;
        }
        .gitee-traffic {
          background: url("@/assets/debug/images/1-bg.png");
          background-color: #e8faea;
          background-size: 100% 100%;
        }
        .gitHub-traffic {
          background: url("@/assets/debug/images/2-bg.png");
          background-color: #e7e1fb;
          background-size: 100% 100%;
        }
        .today-traffic {
          background: url("@/assets/debug/images/3-bg.png");
          background-color: #fdf3e9;
          background-size: 100% 100%;
        }
        .yesterday-traffic {
          background: url("@/assets/debug/images/4-bg.png");
          background-color: #f0f5fb;
          background-size: 100% 100%;
        }
      }
      .item-right {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 430px;
        border: 1px solid var(--el-border-color);
        border-radius: 25px;
        .echarts-title {
          padding: 15px 20px;
          font-family: DIN;
          font-size: 18px;
          font-weight: bold;
          border-bottom: 1px solid var(--el-border-color);
        }
        .book-echarts {
          flex: 1;
          width: 100%;
        }
      }
    }
  }
  .bottom-box {
    position: relative;
    padding: 20px 0 0;
    .bottom-title {
      position: absolute;
      top: 75px;
      left: 50px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .bottom-tabs {
      padding: 0 50px;
    }
    .curve-echarts {
      box-sizing: border-box;
      height: 400px;
      padding: 0 50px;
    }
  }
}
</style>
