import { CbrD<PERSON>, EquipmentStatus, RenderData, RenderItem, RenderItemShow, SetSaddrValue } from "./Graph";
import { Cell } from "@antv/x6";
import { sprintf } from "sprintf-js";
import { setCellVisible } from "./GraphUtil";
/**
 * 图符渲染
 * <AUTHOR>
 * @version 1.0 2025-03-27
 */
export class GraphRenderFactory {
  setSaddrValue(datalist: SetSaddrValue[], renderData: RenderData, cbrdis: CbrDis) {
    let renderItems: RenderItem[] | undefined;
    for (const saddrValue of datalist) {
      // 产生随机值
      //saddrValue.value = Math.floor(Math.random() * 3) + "";
      renderItems = renderData.yxMap.get(saddrValue.saddr);
      if (!renderItems) {
        console.log("短地址未找到配置项", saddrValue);
        continue;
      }
      this.setRenderItemValue(saddrValue, renderItems, cbrdis);
    }
  }
  setRenderItemValue(setSaddrValue: SetSaddrValue, renderItems: RenderItem[], cbrdis: CbrDis) {
    for (const renderItem of renderItems) {
      if (renderItem.show && renderItem.show.length > 0) {
        // 组件个性化显示
        this.setCellRender(renderItem, setSaddrValue, cbrdis);
      } else {
        // 设置组件文本值
        this.setCellText(renderItem, setSaddrValue);
      }
    }
  }

  getShowValue(renderItem: RenderItem, setSaddrValue: SetSaddrValue) {
    let value = setSaddrValue.value;
    let numberValue: number;
    try {
      numberValue = Number(value);
    } catch (e) {
      return value;
    }
    if (renderItem.equipmentConfig.factor) {
      numberValue = numberValue * renderItem.equipmentConfig.factor;
    }
    if (renderItem.equipmentConfig.format) {
      try {
        return sprintf(renderItem.equipmentConfig.format, numberValue);
      } catch (e) {}
    }
    return numberValue + "";
  }

  setCellText(renderItem: RenderItem, setSaddrValue: SetSaddrValue) {
    renderItem.cell.attr("label/text", this.getShowValue(renderItem, setSaddrValue));
  }
  setCbrDisRender(renderItem: RenderItem, setSaddrValue: SetSaddrValue, cbrdis: CbrDis) {
    const value = this.getShowValue(renderItem, setSaddrValue);
    // 开关或者刀闸的值
    const showList = renderItem.show as RenderItemShow[];
    let openCell: Cell;
    let closeCell: Cell;
    // 找到开和关对应的cell
    if (showList[0].value == EquipmentStatus.OPEN) {
      openCell = showList[0].showValue as Cell;
      closeCell = renderItem.cell;
    } else {
      openCell = renderItem.cell;
      closeCell = showList[0].showValue as Cell;
    }
    // 判断是否是开
    if (value == cbrdis.open) {
      setCellVisible(openCell, true);
      setCellVisible(closeCell, false);
    } else {
      setCellVisible(openCell, false);
      setCellVisible(closeCell, true);
    }
  }
  setCellRender(renderItem: RenderItem, setSaddrValue: SetSaddrValue, cbrdis: CbrDis) {
    const showList = renderItem.show as RenderItemShow[];
    // 判断是否是开关或刀闸
    if (showList.length == 1 && showList[0].isCbrDis) {
      this.setCbrDisRender(renderItem, setSaddrValue, cbrdis);
      return;
    }
    const value = this.getShowValue(renderItem, setSaddrValue);
    const showMap = new Map<string, string | Cell>();
    for (const item of showList) {
      showMap.set(item.value, item.showValue);
      if (typeof item.showValue == "object") {
        const cell = item.showValue;
        // 隐藏所有关联的图符
        if (cell.isVisible()) {
          setCellVisible(cell, false);
        }
      }
    }
    const showCell = showMap.get(value);
    if (!showCell) {
      this.setCellText(renderItem, setSaddrValue);
      // 设置当前图符可见
      if (!renderItem.cell.isVisible()) {
        setCellVisible(renderItem.cell, true);
      }
      return;
    }
    if (typeof showCell == "string") {
      renderItem.cell.attr("label/text", showCell);
      // 设置当前图符可见
      if (!renderItem.cell.isVisible()) {
        setCellVisible(renderItem.cell, true);
      }
    } else {
      if (renderItem.cell.isVisible()) {
        setCellVisible(renderItem.cell, false);
      }
      setCellVisible(showCell, true);
    }
  }
}
