export default {
  common: {
    // 通用操作按钮
    buttons: {
      add: "Ajouter",
      edit: "Modifier",
      delete: "Supprimer",
      save: "Enregistrer",
      cancel: "Annuler",
      confirm: "Confirmer",
      search: "Rechercher",
      reset: "Réinitialiser",
      import: "Importer",
      export: "Exporter",
      upload: "Télécharger",
      download: "Télécharger",
      preview: "Aperçu",
      refresh: "Actualiser",
      clear: "Effacer",
      moveUp: "Monter",
      moveDown: "Descendre"
    },
    // 通用状态
    status: {
      success: "Succès",
      failed: "Échec",
      loading: "Chargement",
      processing: "Traitement",
      completed: "Terminé",
      error: "Erreur",
      warning: "Avertissement",
      info: "Information"
    },
    // 通用提示信息
    messages: {
      confirmDelete: "Êtes-vous sûr de vouloir supprimer ?",
      confirmClear: "Êtes-vous sûr de vouloir effacer ?",
      confirmImport: "Êtes-vous sûr de vouloir importer ?",
      confirmExport: "Êtes-vous sûr de vouloir exporter ?",
      operationSuccess: "Opération réussie",
      operationFailed: "Opération échouée",
      saveSuccess: "Enregistrement réussi",
      saveFailed: "Échec de l'enregistrement",
      deleteSuccess: "Suppression réussie",
      deleteFailed: "Échec de la suppression",
      importSuccess: "Import réussi",
      importFailed: "Échec de l'import",
      exportSuccess: "Export réussi",
      exportFailed: "Échec de l'export",
      uploadSuccess: "Téléchargement réussi",
      uploadFailed: "Échec du téléchargement",
      downloadSuccess: "Téléchargement réussi",
      downloadFailed: "Échec du téléchargement",
      noData: "Aucune donnée",
      loading: "Chargement...",
      processing: "Traitement...",
      pleaseWait: "Veuillez patienter...",
      pleaseSelect: "Veuillez sélectionner",
      pleaseInput: "Veuillez entrer",
      invalidInput: "Entrée invalide",
      networkError: "Erreur réseau",
      serverError: "Erreur serveur",
      unknownError: "Erreur inconnue"
    },
    // 通用表单验证
    validation: {
      required: "Ce champ est obligatoire",
      invalidFormat: "Format incorrect",
      invalidLength: "Longueur incorrecte",
      invalidRange: "Plage incorrecte",
      invalidValue: "Valeur incorrecte",
      invalidEmail: "Format d'email incorrect",
      invalidPhone: "Format de téléphone incorrect",
      invalidUrl: "Format d'URL incorrect",
      invalidNumber: "Veuillez entrer un nombre",
      invalidInteger: "Veuillez entrer un entier",
      invalidDecimal: "Veuillez entrer un décimal",
      invalidDate: "Format de date incorrect",
      invalidTime: "Format d'heure incorrect",
      invalidDateTime: "Format de date et heure incorrect",
      invalidIp: "Format d'adresse IP incorrect",
      invalidPort: "Format de port incorrect",
      invalidPath: "Format de chemin incorrect",
      invalidFileName: "Format de nom de fichier incorrect",
      invalidFileSize: "Taille de fichier dépassée",
      invalidFileType: "Type de fichier non supporté"
    },
    // 通用表格列
    columns: {
      index: "Index",
      name: "Nom",
      type: "Type",
      status: "Statut",
      createTime: "Date de création",
      updateTime: "Date de mise à jour",
      description: "Description",
      operation: "Opération"
    },
    // 通用对话框
    dialog: {
      title: "Conseil",
      confirm: "Confirmer",
      cancel: "Annuler",
      close: "Fermer",
      maximize: "Maximiser",
      minimize: "Minimiser",
      restore: "Restaurer"
    },
    // 通用文件操作
    file: {
      upload: "Télécharger un fichier",
      download: "Télécharger un fichier",
      delete: "Supprimer un fichier",
      preview: "Aperçu du fichier",
      rename: "Renommer",
      move: "Déplacer",
      copy: "Copier",
      paste: "Coller",
      cut: "Couper",
      select: "Sélectionner un fichier",
      drag: "Glissez le fichier ici",
      drop: "Relâcher le fichier",
      size: "Taille du fichier",
      type: "Type de fichier",
      name: "Nom du fichier",
      path: "Chemin du fichier",
      lastModified: "Dernière modification",
      createTime: "Date de création"
    }
  }
};
