<template>
  <div class="table-box">
    <!-- 差异对比弹窗 -->
    <AllParamCompareDialog
      v-if="showDiffDialog"
      v-model:visible="showDiffDialog"
      :diffdata="diffdata"
      @confirm="confirmImport"
      @cancel="showDiffDialog = false"
    />
    <div class="card mb10 pt0 pb0">
      <SelectFilter :data="selectFilterData" :default-values="selectFilterValues" @change="changeSelectFilter" />
    </div>
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getParamList"
      highlight-current-row
      table-key="allParamset"
      :init-param="initParam"
      :request-auto="false"
      :data-callback="dataCallback"
      row-key="paramName"
      :cell-style="cellStyle"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="confirmSelect(scope.row)">{{ t("device.allParamSetting.confirm") }}</el-button>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader="">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-checkbox v-model="refreshCheck" :label="t('device.allParamSetting.autoRefresh')" size="large" />
          <el-button type="primary" plain :icon="Refresh" @click="refreshParam">{{ t("device.allParamSetting.refresh") }}</el-button>
          <el-button type="primary" :icon="EditPen" @click="confirmParam">{{ t("device.allParamSetting.confirm") }}</el-button>
          <el-button type="success" :icon="Upload" @click="importFile">{{ t("device.allParamSetting.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="exportFile">{{ t("device.allParamSetting.export") }}</el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Upload, EditPen, Download, Refresh } from "@element-plus/icons-vue";
// const router = useRouter();
import { paramSettingApi } from "@/api/modules/biz/debug/paramsetting";
import { osControlApi } from "@/api/modules/biz/os";
import { useDebugStore } from "@/stores/modules/debug";
import AllParamCompareDialog from "../dialog/AllParamCompareDialog.vue";
import { IpUtils } from "@/utils/iec/ipUtils";
import { DiffData } from "@/api/interface/biz/debug/diffitem";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { deviceInfoMenutreeApi } from "@/api";
const { debugIndex, currDevice } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
import { useConfigStore } from "@/stores/modules";
import Decimal from "decimal.js";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { paramInfo } = useConfigStore();
const showDiffDialog = ref(false);
const diffdata = ref<DiffData>([]); // 明确指定数组类型
const progressDialog = ref();
const selectFilterData = reactive([
  {
    title: t("device.allParamSetting.settingGroup"),
    key: "grpName",
    multiple: true,
    options: [{ label: t("device.allParamSetting.all"), value: "ALLSETTING_TABLE" }]
  }
]);
// 默认 selectFilter 参数
const selectFilterValues = ref({ grpName: ["ALLSETTING_TABLE"] });
const changeSelectFilter = (value: typeof selectFilterValues.value) => {
  proTable.value!.pageable.pageNum = 1;
  selectFilterValues.value = value;
  proTable.value!.search();
};
const { addConsole } = useDebugStore();
// ProTable 实例
const proTable = ref<ProTableInstance>();

const refreshCheck = ref(false);
// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

// 确认导入
const confirmImport = async data => {
  console.log(t("device.allParamSetting.confirm"));
  showDiffDialog.value = false;
  const modifiedData = data;
  if (!modifiedData || modifiedData.length === 0) {
    ElMessage.warning(t("device.allParamSetting.noDataToImport"));
    return;
  }
  let updateItems: any[] = [];
  modifiedData.forEach(item => {
    updateItems.push({
      grp: item.grp,
      inf: item.inf,
      name: item.name,
      v: item.newValue
    });
  });
  console.log(t("device.allParamSetting.responseLog"), updateItems);
  showLoading();
  try {
    // 提交数据到 API
    const response = await paramSettingApi.confirmParamByDevice(props.deviceId, updateItems);
    console.log(t("device.allParamSetting.responseLog"), response);
    // 处理返回结果
    if (Number(response.code) === 0) {
      addConsole(t("device.allParamSetting.importSuccess"));
      ElMessageBox.alert(t("device.allParamSetting.importSuccess"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
    } else {
      addConsole(t("device.allParamSetting.importFailed", { msg: response.msg }));
      ElMessageBox.alert(t("device.allParamSetting.importFailed", { msg: response.msg }), t("device.allParamSetting.errorTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.allParamSetting.requestFailed"), error);
    ElMessageBox.alert(t("device.allParamSetting.requestFailed"), t("device.allParamSetting.errorTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  try {
    // hideLoading();
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }
    console.log("dataCallback", data.list);

    // 直接在原数据上添加 isModified 和 originalValue 属性
    data.list.forEach(item => {
      item.isModified = false;
      item.originalValue = item.value; // 保存原始值
    });

    // 检查是否有 isModified 为 true 的数据
    const hasModified = data.list.some(item => item.isModified);
    if (hasModified) {
      refreshCheck.value = false; // 禁止自动刷新
    }

    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  }
};

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getParamList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.grpName = toRaw(selectFilterValues.value["grpName"]);
  delete newParams.createTime;
  console.log(t("device.allParamSetting.responseLog"), newParams);
  try {
    const result = await paramSettingApi.getAllParamByDevice(props.deviceId, newParams);
    console.log(t("device.allParamSetting.responseLog"), result);
    if (Number(result.code) !== 0) {
      addConsole(t("device.allParamSetting.queryFailed", { msg: result.msg }));
      ElMessageBox.alert(t("device.allParamSetting.queryFailed", { msg: result.msg }), t("device.allParamSetting.errorTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "error"
      });
    }
    return result || { list: [], total: 0 };
  } catch (error) {
    console.error(t("device.allParamSetting.requestFailed"), error);
    return { list: [], total: 0 };
  }
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

const refreshParam = async () => {
  if (proTable.value?.tableData.some(row => row.isModified)) {
    ElMessageBox.confirm(t("device.allParamSetting.unsavedChanges"), t("device.allParamSetting.alertTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      cancelButtonText: t("device.allParamSetting.cancelButton"),
      type: "warning"
    })
      .then(() => {
        proTable.value?.getTableList();
      })
      .catch(() => {
        // 用户取消刷新
      });
  } else {
    proTable.value?.getTableList();
  }
};
onMounted(() => {
  getGroupInfo();
  proTable.value!.search();
});

const getGroupInfo = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.grpName = debugIndex.compData.get(currDevice.id).pname;
  newParams.label = debugIndex.compData.get(currDevice.id).label;
  delete newParams.createTime;
  try {
    const result = await deviceInfoMenutreeApi.getDeviceMenuTree(currDevice.id);
    console.log("result:", result);
    // 递归查找result中fc为SP或者SG的菜单
    const findSettingMenus = (menus: any[]): any[] => {
      const settingMenus: any[] = [];

      const traverse = (menu: any) => {
        if (menu.fc === "SP") {
          settingMenus.push(menu);
        }
        if (menu.menus && Array.isArray(menu.menus)) {
          menu.menus.forEach(traverse);
        }
      };

      menus.forEach(traverse);
      return settingMenus;
    };

    const settingMenus = findSettingMenus(result.data);
    result.data = settingMenus;

    const resultData = Array.isArray(result.data) ? result.data : [];
    resultData.forEach(item => {
      selectFilterData[0].options.push({ label: item.desc, value: item.name });
    });

    console.log(t("device.allParamSetting.responseLog"), result);
  } catch (error) {
    console.error(t("device.allParamSetting.requestFailed"), error);
  }
};

const isValid = (num: any, dataType: string, min: any, max: any, step: number) => {
  if (dataType === "s") {
    return true;
  }
  let realValue = num.trim();
  if (realValue.length == 0) {
    return false;
  }
  if (dataType === "net") {
    if (!IpUtils.validateIp(realValue)) {
      return false;
    }
    realValue = IpUtils.ipToNumber(realValue);
    console.log(IpUtils.ipToNumber(max));
    min = IpUtils.ipToNumber(min);
    max = IpUtils.ipToNumber(max);
  }
  min = Number(min);
  max = Number(max);
  const value = Number(realValue);
  // 检查是否为有效数值
  if (typeof value !== "number" || isNaN(value)) {
    return false;
  }

  // 处理步长为0的情况
  if (step === 0) {
    return false;
  }
  const diff = new Decimal(value).minus(min);
  const n = diff.dividedBy(step);
  return n.isInteger() && value >= min && value <= max;
};

const confirmParam = async () => {
  const modifiedData = proTable.value?.tableData.filter(row => row.isModified);
  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.allParamSetting.noDataToConfirm"), t("device.allParamSetting.alertTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "warning"
    });
    return;
  }
  showLoading();
  const updateItems = modifiedData.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.paramName,
    v: item.value
  }));

  try {
    // 提交数据到 API
    const response = await paramSettingApi.confirmParamByDevice(props.deviceId, updateItems);
    console.log(t("device.allParamSetting.responseLog"), response);
    // 处理返回结果
    if (Number(response.code) === 0) {
      addConsole(t("device.allParamSetting.confirmSuccess"));
      ElMessageBox.alert(t("device.allParamSetting.confirmSuccess"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
    } else {
      addConsole(t("device.allParamSetting.confirmFailed") + response.msg);
      ElMessageBox.alert(t("device.allParamSetting.confirmFailed") + response.msg, t("device.allParamSetting.errorTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.allParamSetting.requestFailed"), error);
    ElMessageBox.alert(t("device.allParamSetting.requestFailed"), t("device.allParamSetting.errorTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};
const confirmSelect = async (row: any) => {
  const modifiedData = row.isModified ? [row] : [];
  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.allParamSetting.noDataToConfirm"), t("device.allParamSetting.alertTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "warning"
    });
    return;
  }
  showLoading();
  const updateItems = modifiedData.map(item => ({
    grp: item.grp,
    inf: item.inf,
    name: item.paramName,
    v: item.value
  }));

  try {
    // 提交数据到 API
    const response = await paramSettingApi.confirmParamByDevice(props.deviceId, updateItems);
    console.log(t("device.allParamSetting.responseLog"), response);
    // 处理返回结果
    if (Number(response.code) === 0) {
      addConsole(t("device.allParamSetting.confirmSuccess"));
      ElMessageBox.alert(t("device.allParamSetting.confirmSuccess"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "success"
      }).then(() => {
        modifiedData.forEach(row => {
          row.isModified = false;
        });
      });
    } else {
      addConsole(t("device.allParamSetting.confirmFailed") + response.msg);
      ElMessageBox.alert(t("device.allParamSetting.confirmFailed") + response.msg, t("device.allParamSetting.errorTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.allParamSetting.requestFailed"), error);
    ElMessageBox.alert(t("device.allParamSetting.requestFailed"), t("device.allParamSetting.errorTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};
const importFile = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("device.allParamSetting.import"),
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] },
      { name: "xml", extensions: ["xml"] }
    ]
  });
  // 导入路径不存在返回
  if (!selectPath.path) {
    return;
  }

  showLoading();
  const path = String(selectPath.path);

  try {
    // 比较差异
    const response = await paramSettingApi.getAllDiffParamByDevice(props.deviceId, { path, grpName: "ALLSETTING_TABLE" });
    console.log(t("device.allParamSetting.responseLog"), response);

    if (Number(response.code) === 0 && response.data) {
      diffdata.value = response.data as DiffData;
      if (diffdata.value.length == 0) {
        ElMessageBox.alert(t("device.allParamSetting.noDataToImport"), t("device.allParamSetting.alertTitle"), {
          type: "warning"
        });
      } else {
        showDiffDialog.value = true;
      }
    } else {
      ElMessageBox.alert(response.msg, t("device.allParamSetting.errorTitle"), {
        type: "error"
      });
    }
    addConsole(response.msg);
  } finally {
    // 关闭 loading 实例
    hideLoading();
  }
};

const exportFile = async () => {
  const defaultPath = t("device.allParamSetting.exportFileName");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.allParamSetting.export"),
    defaultPath,
    filterList: [
      { name: "xlsx", extensions: ["xlsx"] },
      { name: "csv", extensions: ["csv"] },
      { name: "xml", extensions: ["xml"] }
    ]
  });
  // 导出路径不存在返回
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  console.log(t("device.allParamSetting.selectPathLog"), selectPath);

  showLoading();

  try {
    const result = await paramSettingApi.exportAllParamByDevice(props.deviceId, {
      path,
      grpName: "ALLSETTING_TABLE"
    });
    // 判断返回结果中的code为0，提示成功，否则提示失败
    if (Number(result.code) === 0) {
      addConsole(t("device.allParamSetting.exportSuccess") + path);
      ElMessageBox.alert(t("device.allParamSetting.exportSuccess"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "success"
      });
    } else {
      addConsole(t("device.allParamSetting.exportFailed"));
      ElMessageBox.alert(t("device.allParamSetting.exportFailed"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.allParamSetting.exportFailed"), error);
    ElMessageBox.alert(t("device.allParamSetting.exportFailed"), t("device.allParamSetting.alertTitle"), {
      confirmButtonText: t("device.allParamSetting.confirmButton"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { prop: "index", label: t("device.variable.sequenceNumber"), fixed: "left", width: 70 },
  {
    prop: "grpName",
    label: t("device.allParamSetting.settingGroup"),
    width: 120
  },
  {
    prop: "paramName",
    label: t("device.variable.name"),
    width: 260,
    search: {
      el: "input",
      tooltip: t("device.allParamSetting.searchNamePlaceholder"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "paramDesc",
    label: t("device.variable.description"),
    search: {
      el: "input",
      tooltip: t("device.allParamSetting.searchDescPlaceholder"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "value",
    label: t("device.variable.value"),
    width: 180,
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        if (refreshCheck.value) {
          addConsole(t("device.allParamSetting.autoRefreshWarning"));
          ElMessageBox.alert(t("device.allParamSetting.autoRefreshWarning"), t("device.allParamSetting.alertTitle"), {
            confirmButtonText: t("device.allParamSetting.confirmButton"),
            type: "warning"
          });
          await nextTick(() => {
            row.value = row.originalValue;
          });
          return;
        }
        if (!isValid(value, row.type, row.minValue, row.maxValue, Number(row.step))) {
          addConsole(t("device.allParamSetting.invalidValue", { name: row.paramName, value: value }));
          ElMessageBox.alert(
            t("device.allParamSetting.invalidValue", { name: row.paramName, value: value }),
            t("device.allParamSetting.errorTitle"),
            {
              confirmButtonText: t("device.allParamSetting.confirmButton"),
              type: "error"
            }
          );
          await nextTick(() => {
            row.value = row.originalValue;
          });
          return;
        }
        row.isModified = true;
        row.originalValue = value;
      };
      return (
        <div>
          <el-input v-model={scope.row.value} onChange={(value: string) => handleChange(value, scope.row)} />
        </div>
      );
    }
  },
  {
    prop: "minValue",
    label: t("device.allParamSetting.minValue"),
    width: 100
  },
  {
    prop: "maxValue",
    label: t("device.allParamSetting.maxValue"),
    width: 100
  },
  {
    prop: "step",
    label: t("device.allParamSetting.step"),
    width: 100
  },
  {
    prop: "inf",
    width: 80,
    isShow: false,
    label: t("device.variable.address")
  },
  {
    prop: "unit",
    label: t("device.allParamSetting.unit"),
    width: 80
  },
  { prop: "operation", label: t("device.variable.operation"), fixed: "right", width: 120 }
]);
const cellStyle = ({ row }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  if (row.isModified) {
    return { backgroundColor: "var(--el-color-warning-light-9)" }; // 修改时的背景色
  }
  return {};
};

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, paramInfo.PARAM_REFRESH_TIME); // 每 5 秒刷新一次
};
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};
onBeforeUnmount(() => {
  stopRefreshTimer();
});
watch(refreshCheck, newValue => {
  if (newValue) {
    // 自动刷新被选中时，检查是否有数据被修改
    if (proTable.value?.tableData.some(row => row.isModified)) {
      ElMessageBox.confirm(t("device.allParamSetting.continueAutoRefresh"), t("device.allParamSetting.alertTitle"), {
        confirmButtonText: t("device.allParamSetting.confirmButton"),
        cancelButtonText: t("device.allParamSetting.cancelButton"),
        type: "warning"
      })
        .then(() => {
          // 用户确认继续启用自动刷新
          console.log(t("device.allParamSetting.continueAutoRefresh"));
          startRefreshTimer();
        })
        .catch(() => {
          // 用户取消启用自动刷新
          refreshCheck.value = false; // 取消自动刷新
        });
    } else {
      // 自动刷新被取消时，停止定时器
      startRefreshTimer();
    }
  } else {
    stopRefreshTimer();
  }
});
watch(
  debugIndex.compData,
  newValue => {
    console.log(t("device.allParamSetting.responseLog"), newValue);
    if (newValue) {
      console.log(t("device.allParamSetting.settingGroup"), newValue);
      proTable.value?.reset();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
</style>
