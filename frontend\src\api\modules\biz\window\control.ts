import { moduleIpcRequest } from "@/api/request";
import { CloseTypeEnum } from "@/enums";
const ipc = moduleIpcRequest("controller/common/window/");

const windowControlApi = {
  /** 窗口关闭 */
  closeWindow(params: CloseTypeEnum) {
    return ipc.invoke<{}>("closeWindow", params);
  },
  /** 窗口最大化 */
  maximizeWindow() {
    return ipc.invoke<{}>("maximizeWindow");
  },
  /** 窗口最小化 */
  minimizeWindow() {
    return ipc.invoke<{}>("minimizeWindow");
  },
  /**  开发者工具 */
  openDevTools() {
    return ipc.invoke<{}>("openDevTools");
  },
  /** 截图 */
  printScreen() {
    return ipc.invoke<{}>("printScreen");
  },
  /** 拖拽窗口 */
  dragWindow() {
    return ipc.invoke<{}>("dragWindow");
  }
};

export { windowControlApi };
