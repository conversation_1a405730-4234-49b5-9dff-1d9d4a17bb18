// 词条条目接口
export interface ProjectDictEntry {
  /** 词条缩写 */
  abbr: string;
  /** 中文描述 */
  cnDesc: string;
  /** 英文描述 */
  enDesc: string;
  /** 西班牙语描述 */
  esDesc: string;
  /** 俄语描述 */
  ruDesc: string;
  /** 本地化描述 */
  locDesc: string;
  /** 其他语言描述 */
  [key: string]: string;
}

// 获取词条请求参数
export interface ProjectDictRequestData {
  /** 起始缩写 */
  abbrAfter: string;
  /** 收到数据后的回调（当多帧数据时仅当前帧数据） */
  cb?: (values: ProjectDictRequestRes) => void;
  /** 自动读取后续数据，存在分页情况下，false=不读取，默认true */
  autoReadAfter?: boolean;
}

// 获取词条响应数据
export interface ProjectDictRequestRes {
  /** 服务错误码 */
  serviceError: number;
  /** 语言 */
  language: string;
  /** 词条数组 */
  dict: ProjectDictEntry[];
  /** 是否有更多数据 */
  moreFollows: number;
}

// 设置词条请求参数
export interface SetProjectDictRequestData {
  /** 写的词条数组 */
  entrys: ProjectDictEntry[];
  /** 每次最大写多少条，默认80条，建议不超80，否则易超报文总长度或者装置一次性写的限制 */
  batchSize?: number;
}

// 设置词条响应数据
export interface SetProjectDictRes {
  /** true=成功，false=存在失败，从errorData中读取 */
  success: boolean;
  /** position:写数组中的索引位置，serviceError：错误码，item：写值项 */
  errorData: {
    position: number;
    serviceError: number;
    item?: ProjectDictEntry;
  }[];
}

// 兼容旧接口
interface DictInfo {
  abbr: string;
  cndesc: number;
  endesc: string;
  esdesc: string;
  frdesc: string;
}

// 装置词条 // 中英西法
export type { DictInfo };
