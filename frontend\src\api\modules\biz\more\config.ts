import { moduleIpcRequest } from "@/api/request";
import { MoreInfo } from "@/api";
const ipc = moduleIpcRequest("controller/common/more/");

const moreControlApi = {
  /** 导入工程配置 */
  importConfig(req: MoreInfo.ImportConfigParam) {
    return ipc.invoke<any>("importConfig", req);
  },
  /** 导出工程配置 */
  exportConfig(req: MoreInfo.ExportConfigParam) {
    return ipc.invoke<any>("exportConfig", req);
  }
};

export { moreControlApi };
