import { SheetData } from "@/api/interface/biz/debug/diffitem";
import { FileItem } from "@/api/interface/biz/debug/fileitem";
import { MatrixTaskItem } from "@/api/interface/biz/matrix";
import { moduleIpcRequest } from "@/api/request";
import { DeviceItem } from "@/stores/modules/deviceItem";
const ipc = moduleIpcRequest("controller/matrix/matrix/");

const matrixApi = {
  // 导入装置列表
  importDeviceList(params: any) {
    console.log("matrixApi.importDeviceList");
    return ipc.iecInvoke<{}>("importDeviceList", params);
  },
  // 导出装置列表
  exportDeviceList(param: { path: string; deviceItems: DeviceItem[] }) {
    console.log("matrixApi.exportDeviceList");
    return ipc.iecInvoke<{}>("exportDeviceList", param);
  },
  // 导入下载文件
  importDownloadList(params: any) {
    console.log("matrixApi.importDownloadList");
    return ipc.iecInvoke<{}>("importDownloadList", params);
  },
  // 导出下载文件
  exportDownloadList(param: { path: string; fileItems: FileItem[] }) {
    console.log("matrixApi.exportDownloadList");
    return ipc.iecInvoke<{}>("exportDownloadList", param);
  },
  // 导入参数定值
  importParamList(params: any) {
    console.log("matrixApi.importParamList");
    return ipc.iecInvoke<{}>("importParamList", params);
  },
  // 导出参数定值
  exportParanList(param: { path: string; paramItems: SheetData[] }) {
    console.log("matrixApi.exportParanList");
    return ipc.iecInvoke<{}>("exportParanList", param);
  },
  // 执行装置任务
  runTask(params: MatrixTaskItem[]) {
    console.log("matrixApi.runTask");
    return ipc.iecInvoke<{}>("runTask", params);
  },
  // 打包文件列表到指定目录
  handlePackage(params: { saveDir: string; fileList: any[] }) {
    return ipc.iecInvoke<any>("handlePackage", params);
  }
};

export { matrixApi };
