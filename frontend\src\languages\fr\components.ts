export default {
  checkCard: {
    default: "Par défaut"
  },
  chooseModule: {
    title: "Sélectionner l'application",
    noModule: "Aucun module trouvé !",
    setDefault: "Définir par défaut",
    cancel: "Annuler",
    confirm: "Confirmer"
  },
  closer: {
    title: "Confirmation de sortie",
    message: "Voulez-vous vraiment quitter ?",
    confirm: "Confirmer",
    minimize: "Minimiser dans la barre des tâches",
    cancel: "Annuler"
  },
  codeHighLight: {
    noCode: "Aucun"
  },
  cropUpload: {
    title: "Recadrage d'image",
    zoomIn: "Zoom avant",
    zoomOut: "Zoom arrière",
    rotateLeft: "Rotation vers la gauche",
    rotateRight: "Rotation vers la droite",
    uploadImage: "Cliquez pour télécharger une image",
    uploadTip: "Veuillez télécharger un fichier image, recommandé de ne pas dépasser 2M",
    cancel: "Annuler",
    confirm: "Confirmer"
  },
  error: {
    forbidden: "D<PERSON><PERSON><PERSON>, vous n'avez pas accès à cette page~🙅‍♂️🙅‍♀️",
    notFound: "<PERSON><PERSON><PERSON><PERSON>, la page que vous visitez n'existe pas~🤷‍♂️🤷‍♀️",
    serverError: "Désolé, votre réseau a disparu~🤦‍♂️🤦‍♀️",
    back: "Retour à la page précédente"
  },
  form: {
    input: {
      placeholder: "Veuillez remplir {label}"
    },
    select: {
      placeholder: "Veuillez sélectionner {label}"
    },
    button: {
      add: "Ajouter",
      edit: "Modifier",
      delete: "Supprimer",
      view: "Voir"
    },
    search: {
      inputPlaceholder: "Veuillez saisir",
      selectPlaceholder: "Veuillez sélectionner",
      rangeSeparator: "à",
      startPlaceholder: "Heure de début",
      endPlaceholder: "Heure de fin"
    }
  },
  selectIcon: {
    title: "Sélection d'icône",
    placeholder: "Veuillez sélectionner une icône",
    searchPlaceholder: "Rechercher une icône",
    noSearchResult: "Aucune icône trouvée pour votre recherche~",
    moreIcons: "Plus d'icônes",
    enterIconifyCode: "Veuillez entrer le code iconify de l'icône souhaitée, ex: mdi:home-variant",
    iconifyAddress: "Adresse iconify",
    localIcons: "Icônes locales"
  },
  selector: {
    add: "Ajouter",
    addCurrent: "Ajouter l'actuel",
    addSelected: "Ajouter la sélection",
    delete: "Supprimer",
    deleteCurrent: "Supprimer l'actuel",
    deleteSelected: "Supprimer la sélection",
    cancel: "Annuler",
    confirm: "Confirmer",
    selected: "Sélectionné",
    maxSelect: "Sélection maximale",
    singleSelectOnly: "Une seule sélection possible",
    maxSelectLimit: "Sélection maximale de {count} éléments",
    person: "personne"
  },
  upload: {
    view: "Voir",
    edit: "Modifier",
    delete: "Supprimer",
    uploadImage: "Veuillez télécharger une image",
    uploadSuccess: "Image téléchargée avec succès !",
    uploadFailed: "Échec du téléchargement de l'image, veuillez réessayer !",
    invalidFormat: "Le format de l'image téléchargée ne correspond pas au format requis !",
    fileSizeExceeded: "La taille de l'image téléchargée ne peut pas dépasser {size}M !",
    maxFilesExceeded: "Vous ne pouvez télécharger que {limit} images maximum, veuillez en supprimer avant de télécharger !",
    fileSizeZero: "Le fichier {fileName} a une taille de 0, impossible de télécharger !",
    tips: "Conseils"
  },
  treeFilter: {
    searchPlaceholder: "Entrez des mots-clés pour filtrer",
    expandAll: "Développer tout",
    collapseAll: "Réduire tout",
    all: "Tout"
  },
  proTable: {
    search: {
      reset: "Réinitialiser",
      search: "Rechercher",
      expand: "Développer",
      collapse: "Réduire"
    },
    pagination: {
      total: "Total {total} éléments",
      pageSize: "éléments/page",
      goto: "Aller à",
      page: "page"
    },
    colSetting: {
      title: "Paramètres de colonnes",
      fixedLeft: "Afficher",
      fixedRight: "Triable",
      cancelFixed: "Annuler la fixation",
      reset: "Restaurer par défaut",
      confirm: "Confirmer",
      cancel: "Annuler"
    },
    table: {
      empty: "Aucune donnée"
    }
  },
  basicComponent: {
    title: "Composants de base",
    line: "Ligne",
    text: "Texte",
    rect: "Rectangle",
    circle: "Cercle",
    ellipse: "Ellipse",
    triangle: "Triangle",
    arc: "Arc"
  }
};
