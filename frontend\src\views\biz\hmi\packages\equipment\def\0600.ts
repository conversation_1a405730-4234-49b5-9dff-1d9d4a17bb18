import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0600",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 19,
        y1: 0,
        x2: 6.33,
        y2: 8.5
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(28, 8.33, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 35.67,
        y1: 10,
        x2: 31.67,
        y2: 10
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(4, 8, 3.67, 3.33)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 0,
        y1: 9.67,
        x2: 4,
        y2: 9.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
