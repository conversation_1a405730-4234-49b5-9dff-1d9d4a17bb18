<!-- 纵向布局 -->
<template>
  <el-container class="layout">
    <el-aside>
      <div class="aside-box" :style="{ width: isCollapse ? '65px' : '210px' }">
        <div class="logo flx-center">
          <img class="logo-img" :src="sysInfo.SYS_LOGO" alt="logo" />
          <span v-show="!isCollapse" class="logo-text">{{ sysInfo.SYS_NAME }}</span>
        </div>
        <div style="display: flex; flex-direction: column; justify-content: center; height: 100%">
          <el-scrollbar>
            <el-menu :router="false" :default-active="activeMenu" :collapse="isCollapse" :unique-opened="accordion" :collapse-transition="false">
              <SubMenu :menu-list="menuList" />
            </el-menu>
          </el-scrollbar>
        </div>
        <div class="other flx-justify-between">
          <GlobalSetting id="globalSetting" v-model="place" />
          <ThemeQuickSwitch id="themeQuickSwitch" v-model="place" />
          <Language id="language" v-model="place" />
          <MoreInfo id="moreInfo" v-model="place" />
        </div>
      </div>
    </el-aside>
    <el-container>
      <el-header>
        <ToolBarLeft />
        <ToolBarDrag />
        <ToolBarRight />
      </el-header>
      <Main />
    </el-container>
  </el-container>
</template>

<script setup lang="ts" name="layoutVertical">
import { computed } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore, useConfigStore } from "@/stores/modules";
import { useI18n } from "vue-i18n";
import Main from "@/layouts/components/Main/index.vue";
import ToolBarLeft from "@/layouts/components/Header/ToolBarLeft.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import ThemeQuickSwitch from "@/layouts/components/Header/components/ThemeQuickSwitch.vue";
import GlobalSetting from "@/layouts/components/Header/components/GlobalSetting.vue";
import Language from "@/layouts/components/Header/components/Language.vue";
import MoreInfo from "@/layouts/components/Header/components/MoreInfo.vue";
import ToolBarDrag from "@/layouts/components/Header/ToolBarDrag.vue";
const route = useRoute();
const authStore = useAuthStore();
const configStore = useConfigStore();
const place: string = "right";
const accordion = true;
const isCollapse = true;
useI18n();
const menuList = computed(() => authStore.showMenuListGet);
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const sysInfo = computed(() => configStore.sysBaseInfoGet);
</script>

<style scoped lang="scss">
@import "./index";
</style>
