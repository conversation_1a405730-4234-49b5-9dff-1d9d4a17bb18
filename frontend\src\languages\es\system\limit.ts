export default {
  limit: {
    module: {
      title: "Nombre del Módulo",
      icon: "Ícono",
      status: "Estado",
      sort: "Orden",
      description: "Descrip<PERSON>",
      createTime: "Fecha de Creación",
      operation: "Operación",
      add: "Agregar <PERSON>",
      edit: "Editar Módulo",
      delete: "Eliminar Módulo",
      deleteConfirm: "Eliminar módulos seleccionados",
      deleteConfirmWithName: "Eliminar módulo [{name}]",
      form: {
        title: "Por favor ingrese el nombre del módulo",
        status: "Por favor seleccione el estado",
        sort: "Por favor ingrese el orden",
        icon: "Por favor seleccione el ícono"
      }
    },
    menu: {
      title: "Nombre del Menú",
      icon: "Ícono del Menú",
      type: "Tipo de Menú",
      component: "Nombre del Componente",
      path: "Ruta",
      componentPath: "Ruta del Componente",
      sort: "Orden",
      status: "Estado",
      description: "Descripción",
      operation: "Operación",
      add: "Agregar Menú",
      edit: "Editar Menú",
      delete: "Eliminar Menú",
      deleteConfirm: "Eliminar menús seleccionados",
      deleteConfirmWithName: "Eliminar menú [{name}]",
      form: {
        title: "Por favor ingrese el nombre del menú",
        parent: "Por favor seleccione el menú superior",
        type: "Por favor seleccione el tipo de menú",
        path: "Por favor ingrese la ruta",
        component: "Por favor ingrese la ruta del componente",
        sort: "Por favor ingrese el orden",
        icon: "Por favor seleccione el ícono",
        status: "Por favor seleccione el estado",
        link: "Por favor ingrese la dirección del enlace"
      }
    },
    button: {
      title: "Nombre del Botón",
      code: "Código del Botón",
      sort: "Orden",
      description: "Descripción",
      operation: "Operación",
      add: "Agregar Botón",
      edit: "Editar Botón",
      delete: "Eliminar Botón",
      deleteConfirm: "Eliminar botones seleccionados",
      deleteConfirmWithName: "Eliminar botón [{name}]",
      batch: {
        title: "Agregar Botones en Lote",
        shortName: "Nombre Corto del Permiso",
        codePrefix: "Prefijo del Código",
        form: {
          shortName: "Por favor ingrese el nombre corto del permiso",
          codePrefix: "Por favor ingrese el prefijo del código"
        }
      },
      form: {
        title: "Por favor ingrese el nombre del botón",
        code: "Por favor ingrese el código del botón",
        sort: "Por favor ingrese el orden"
      }
    },
    role: {
      title: "Nombre del Rol",
      org: "Organización",
      category: "Tipo de Rol",
      status: "Estado",
      sort: "Orden",
      description: "Descripción",
      createTime: "Fecha de Creación",
      operation: "Operación",
      add: "Agregar Rol",
      edit: "Editar Rol",
      delete: "Eliminar Rol",
      deleteConfirm: "Eliminar roles seleccionados",
      deleteConfirmWithName: "Eliminar rol [{name}]",
      grant: {
        resource: "Recurso de Autorización",
        permission: "Permiso de Autorización",
        dataScope: "Alcance de Datos"
      },
      form: {
        title: "Por favor ingrese el nombre del rol",
        org: "Por favor seleccione la organización",
        category: "Por favor seleccione el tipo de rol",
        status: "Por favor seleccione el estado"
      }
    },
    spa: {
      title: "Nombre de la Página Única",
      icon: "Ícono",
      type: "Tipo de Página Única",
      path: "Ruta",
      component: "Ruta del Componente",
      sort: "Orden",
      description: "Descripción",
      createTime: "Fecha de Creación",
      operation: "Operación",
      add: "Agregar Página Única",
      edit: "Editar Página Única",
      delete: "Eliminar Página Única",
      deleteConfirm: "Eliminar páginas únicas seleccionadas",
      deleteConfirmWithName: "Eliminar página única [{name}]",
      form: {
        title: "Por favor ingrese el nombre de la página única",
        type: "Por favor seleccione el tipo de página única",
        path: "Por favor ingrese la ruta",
        component: "Por favor ingrese la ruta del componente",
        sort: "Por favor ingrese el orden",
        icon: "Por favor seleccione el ícono",
        link: "Por favor ingrese la dirección del enlace, ej.: http://www.baidu.com"
      }
    }
  }
};
