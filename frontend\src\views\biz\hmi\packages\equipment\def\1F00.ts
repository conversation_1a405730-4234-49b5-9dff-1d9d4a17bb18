import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "1F00",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "e",
      attrs: {
        ...calculateEllipse(0, 0, 4, 4)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "e",
      attrs: {
        ...calculateEllipse(0, 0, 4, 4)
      }
    }
  ],
  attrs: {
    width: 4,
    height: 4,
    e: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
