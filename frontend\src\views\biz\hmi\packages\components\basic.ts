export function getBasicComponent(t: (key: string) => string) {
  const commonAttrs = {
    body: {
      fill: "#fff",
      strokeWidth: 1
    },
    label: { fill: "#000" }
  };
  const basicTools = [
    {
      name: "node-editor",
      args: {
        attrs: {
          backgroundColor: "transparent"
        },
        getText: "label/text",
        setText: "label/text"
      }
    }
  ];
  return {
    title: t("hmi.graph.basic.title"),
    components: [
      {
        title: t("hmi.graph.basic.components.line"),
        url: new URL("@/assets/hmi/basic/line.png", import.meta.url).href,
        data: {
          shape: "path",
          width: 100,
          height: 10,
          tooltip: t("hmi.graph.basic.components.line"),
          data: {
            isLine: true
          },
          attrs: {
            ...commonAttrs,
            body: {
              fill: "#f00",
              strokeWidth: 2,
              ref: "bg",
              refY: "50%",
              refDKeepOffset: "M 0,0 H 100"
            },
            label: { fill: "#000" }
          }
        }
      },
      {
        title: t("hmi.graph.basic.components.text"),
        url: new URL("@/assets/hmi/basic/text.png", import.meta.url).href,
        data: {
          shape: "text-block",
          width: 100,
          height: 30,
          tooltip: t("hmi.graph.basic.components.text"),
          attrs: {
            body: {
              fill: "none",
              stroke: "none"
            },
            label: {
              class: "graph-text-block"
            }
          },
          text: t("hmi.graph.basic.components.text"),
          tools: basicTools
        }
      },
      {
        title: t("hmi.graph.basic.components.rectangle"),
        url: new URL("@/assets/hmi/basic/rect.png", import.meta.url).href,
        data: {
          shape: "rect",
          width: 100,
          height: 50,
          tooltip: t("hmi.graph.basic.components.rectangle"),
          tools: basicTools,
          attrs: {
            ...commonAttrs,
            label: { fill: "#000" }
          }
        }
      },
      {
        title: t("hmi.graph.basic.components.circle"),
        url: new URL("@/assets/hmi/basic/circle.png", import.meta.url).href,
        data: {
          shape: "circle",
          width: 50,
          height: 50,
          tooltip: t("hmi.graph.basic.components.circle"),
          tools: basicTools,
          attrs: {
            ...commonAttrs,
            label: { fill: "#000" }
          }
        }
      },
      {
        title: t("hmi.graph.basic.components.ellipse"),
        url: new URL("@/assets/hmi/basic/ellipse.png", import.meta.url).href,
        data: {
          shape: "ellipse",
          width: 100,
          height: 50,
          tooltip: t("hmi.graph.basic.components.ellipse"),
          tools: basicTools,
          attrs: {
            ...commonAttrs,
            label: { fill: "#000" }
          }
        }
      },
      {
        title: t("hmi.graph.basic.components.triangle"),
        url: new URL("@/assets/hmi/basic/triangle.png", import.meta.url).href,
        data: {
          shape: "polygon",
          width: 50,
          height: 50,
          tooltip: t("hmi.graph.basic.components.triangle"),
          tools: basicTools,
          // 三角形比例
          points: "0.5,0.05 1,0.95 0,0.95",
          attrs: {
            ...commonAttrs,
            label: { fill: "#000" }
          }
        }
      },
      {
        title: t("hmi.graph.basic.components.arc"),
        url: new URL("@/assets/hmi/basic/arc.png", import.meta.url).href,
        data: {
          shape: "path",
          width: 50,
          height: 25,
          tooltip: t("hmi.graph.basic.components.arc"),
          tools: basicTools,
          attrs: {
            body: {
              refDResetOffset: "M 0,25 A 50 25 0 0 1 50 25"
            },
            label: { fill: "#000" }
          }
        }
      }
    ]
  };
}
