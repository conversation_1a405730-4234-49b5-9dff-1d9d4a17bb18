/**
 * Preload module, this file will be loaded when the program starts.
 */

import { logger } from 'ee-core/log';
import { trayService } from '../service/os/tray';
import { securityService } from '../service/os/security';
import { autoUpdaterService } from '../service/os/auto_updater';

function preload(): void {
  // Example feature module, optional to use and modify
  logger.info("[preload] load 5");

  // 异步延迟加载非关键服务，提高启动速度
  setTimeout(() => {
    trayService.create();
    securityService.create();
    autoUpdaterService.create();
  }, 100); // 延迟100ms加载，让主窗口先显示
}

/**
 * Entry point of the preload module
 */
export { preload };