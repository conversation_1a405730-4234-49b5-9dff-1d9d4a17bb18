<template>
  <el-dropdown @command="handleCommand">
    <span class="language-switch">
      {{ currentLanguage }}
      <el-icon class="el-icon--right">
        <arrow-down />
      </el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="zh">{{ t("layout.language.zh") }}</el-dropdown-item>
        <el-dropdown-item command="en">{{ t("layout.language.en") }}</el-dropdown-item>
        <el-dropdown-item command="es">{{ t("layout.language.es") }}</el-dropdown-item>
        <el-dropdown-item command="fr">{{ t("layout.language.fr") }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from "@element-plus/icons-vue";
import { useGlobalStore } from "@/stores/modules";
import { LanguageType } from "@/stores/interface";

const { locale, t } = useI18n();

const globalStore = useGlobalStore();

const currentLanguage = computed(() => {
  const languageMap = {
    zh: t("layout.language.zh"),
    en: t("layout.language.en"),
    es: t("layout.language.es"),
    fr: t("layout.language.fr")
  };
  return languageMap[locale.value as keyof typeof languageMap];
});

const handleCommand = (command: string) => {
  locale.value = command;
  localStorage.setItem("language", command);
  globalStore.setGlobalState("language", command as LanguageType);
};
</script>

<style scoped>
.language-switch {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-primary);
  cursor: pointer;
}
.language-switch:hover {
  color: var(--el-color-primary);
}
</style>
