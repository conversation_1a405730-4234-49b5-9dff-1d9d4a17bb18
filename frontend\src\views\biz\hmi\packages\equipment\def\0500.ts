import { calculateEllipse } from "../../graph/GraphUtil";

const e = {
  shape: "0500",
  markup: [
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 3,
        y1: 28.83,
        x2: 11,
        y2: 11.83
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0.33, 4, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 2,
        y1: 0,
        x2: 2,
        y2: 4
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 28, 3.33, 3.67)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 1.67,
        y1: 35.67,
        x2: 1.67,
        y2: 31.67
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
