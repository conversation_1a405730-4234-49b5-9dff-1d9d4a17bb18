export namespace ReportParam {
  export interface IECRpcCommonReportRes {
    entryID: number;
    time: string;
    timeQ?: number;
    name: string;
    stVal?: string;
    oldVal?: string;
    quality?: number;
    paraVal?: EventReportParaVal[];
  }

  export interface IECRpcOperateReportRes {
    entryID: number;
    name: string;
    time: string;
    conID: string;
    srcID: string;
    srcType: string;
    type: string;
    step: string;
    val: string;
    para: string;
    cause: string;
  }

  export interface IECRpcAuditLogReportRes {
    entryID: number;
    module: string;
    msg: string;
    type: string;
    level: string;
    orig: string;
    time: string;
    result: string;
    user: string;
  }

  export interface EventReportParaVal {
    name: string;
    value: string;
  }

  export interface IECRpcGroupReportRes {
    id?: number;
    name: string;
    value: string;
    ret_ms: number | string;
    children?: IECRpcGroupReportRes[] | undefined;
  }

  export interface FaultReportFaultEntryST {
    name: string;
    value: string;
    ret_ms: number;
  }
  export interface FaultReportFaultEntryMX {
    name: string;
    value: string;
    ret_ms: number;
  }

  export interface IECRpcReportSearchParam {
    type: string;
    /** 不设置时填空字符 */
    startTime: string;
    /** 不设置时填空字符 */
    stopTime: string;
    entryAfter?: string;
    orderBy?: "ASC" | "DESC";
  }

  export interface IECRpcGroupReportSearchParam {
    type: string;
    /** 不设置时填空字符 */
    startTime: string;
    /** 不设置时填空字符 */
    stopTime: string;
    faultAfter: string;
  }

  export interface IECRpcCommonReportExportParam {
    type: string;
    method?: string;
    path: string;
    items: IECRpcCommonReportRes[] | IECRpcGroupReportRes[] | IECRpcOperateReportRes[];
  }

  export interface IECRpcReportRefreshRes {
    type: string;
    items: IECRpcCommonReportRes[];
  }

  export interface IECRpcTripReportRefreshRes {
    type: string;
    items: IECRpcCommonReportRes[];
  }

  export interface IECRpcGroupReportRefreshRes {
    type: string;
    items: IECRpcGroupReportRes[];
  }

  export interface IECRpcOperateReportRefreshRes {
    type: string;
    items: IECRpcOperateReportRes[];
  }

  export interface IECRpcReportOpenWaveParam {
    exePath: string;
    filePath: string;
  }
}
