import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";

// 定义激活状态的接口
export interface ActivateState {
  isActivated: boolean;
}

const name = "simple-activate"; // 定义模块名称

// 创建和导出激活状态的store
export const useActivateStore = defineStore({
  id: name,
  state: (): ActivateState => ({
    isActivated: false
  }),
  getters: {
    // 获取激活状态
    getActivated(): boolean {
      return this.isActivated;
    }
  },
  actions: {
    // 设置激活状态
    setActivated(isActivated: boolean) {
      this.isActivated = isActivated;
    }
  },
  persist: piniaPersistConfig(name, ["isActivated"])
});
