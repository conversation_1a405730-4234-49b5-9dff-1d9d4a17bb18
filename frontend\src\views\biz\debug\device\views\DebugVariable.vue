<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :cell-style="cellStyle"
      :columns="columns"
      highlight-current-row
      table-key="debugVariable"
      :request-api="getVariableList"
      :init-param="initParam"
      :request-auto="true"
      row-key="name"
      :data-callback="dataCallback"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="EditPen" @click="confirmSelect(scope.row)">{{ t("device.variable.confirm") }}</el-button>
        <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">{{ t("device.variable.delete") }}</el-button>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-checkbox v-model="refreshCheck" :label="t('device.variable.autoRefresh')" size="large" />
          <el-text class="mx-1">{{ t("device.variable.variableName") }}:</el-text>
          <el-input v-model="variableName" style="width: 200px" :placeholder="t('device.variable.inputVariableName')" />

          <el-button type="primary" plain :icon="Refresh" @click="handleButtonClick('refresh')">{{ t("device.variable.refresh") }}</el-button>
          <el-button type="primary" :icon="CirclePlus" @click="handleButtonClick('add')">{{ t("device.variable.add") }}</el-button>
          <el-button type="primary" :icon="CircleCheck" @click="handleConfirm">{{ t("device.variable.confirm") }}</el-button>
          <el-button type="success" :icon="Upload" @click="handleButtonClick('import')">
            {{ t("device.variable.import") }}
          </el-button>
          <el-button type="success" :icon="Download" @click="handleButtonClick('export')">
            {{ t("device.variable.export") }}
          </el-button>
          <el-button type="danger" plain :disabled="!scope.isSelected" :icon="Delete" @click="handleBatchDelete(scope.selectedList)">
            {{ t("device.variable.delete") }}
          </el-button>
        </div>
      </template>
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive, onUnmounted, onBeforeUnmount, nextTick } from "vue";
import { ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { variableApi } from "@/api/modules/biz/debug/variable";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { CirclePlus, Delete, Upload, Download, Refresh, EditPen, CircleCheck } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus"; // 导入 ElLoading 和 ElMessageBox
import { useDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const { addConsole } = useDebugStore();
import { useConfigStore } from "@/stores/modules";
const { paramInfo } = useConfigStore();

// 定义 props
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const selectType = ref("1");
const refreshCheck = ref(false);
const variableName = ref("");

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 初始化请求参数
const initParam = reactive({ type: 1 });

onUnmounted(() => {
  stopRefreshTimer();
});

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};

// 处理返回的表格数据
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  hideLoading();
  try {
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }

    console.log("dataCallback", data.list);

    // 直接在原数据上添加 isModified 和 originalValue 属性
    data.list.forEach(item => {
      item.isModified = false;
      item.originalValue = item.value; // 保存原始值
    });

    // 检查是否有 isModified 为 true 的数据
    const hasModified = data.list.some(item => item.isModified);
    if (hasModified) {
      refreshCheck.value = false; // 禁止自动刷新
    }

    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  } finally {
    hideLoading();
  }
};

// 获取变量列表
const getVariableList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.selectType = selectType.value;
  console.log(newParams);
  // showLoading();
  try {
    const result = await variableApi.getVariableByDevice(props.deviceId, newParams);
    console.log("Fetched data:", result);

    return result || { list: [], total: 0 };
  } catch (error) {
    console.error("Error fetching data:", error);
    return { list: [], total: 0 };
  } finally {
    // hideLoading();
  }
};

const handleConfirm = async () => {
  const modifiedData = proTable.value?.tableData.filter(row => row.isModified);

  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.variable.noDataToConfirm"), t("device.variable.warning"), {
      type: "warning"
    });
    return;
  }

  showLoading();
  try {
    const updateItems = modifiedData.map(item => ({
      name: item.name,
      id: item.id,
      type: item.type,
      error: 0,
      value: item.originalValue,
      writeValue: item.value
    }));

    const response = await variableApi.modifyVariableByDevice(props.deviceId, updateItems);

    if (Number(response.code) === 0) {
      addConsole(t("device.variable.variableModifiedSuccess"));
      ElMessageBox.alert(t("device.variable.variableModifiedSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        setTimeout(() => {
          proTable.value?.getTableList();
        }, 2000);
      });
    } else {
      addConsole(t("device.variable.variableModifiedFailed") + response.msg);
      ElMessageBox.alert(t("device.variable.variableModifiedFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.variable.requestFailed"), error);
    ElMessageBox.alert(t("device.variable.requestFailed"), t("device.variable.error"), {
      confirmButtonText: t("device.variable.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

const confirmSelect = async (row: any) => {
  // 获取所有被修改的数据
  const modifiedData = row.isModified ? [row] : [];

  // 如果没有修改的数据，提示用户
  if (!modifiedData || modifiedData.length === 0) {
    ElMessageBox.alert(t("device.variable.noDataToConfirm"), t("device.variable.warning"), { type: "warning" });
    return;
  }

  showLoading();
  try {
    // 提取每个选中项的唯一标识符（假设是 name 和 id 字段）
    const updateItems = modifiedData.map(item => ({
      name: item.name,
      id: item.id,
      type: item.type,
      error: 0,
      value: item.originalValue,
      writeValue: item.value
    }));

    // 提交数据到 API
    const response = await variableApi.modifyVariableByDevice(props.deviceId, updateItems);

    // 处理返回结果
    if (Number(response.code) === 0) {
      addConsole(t("device.variable.variableModifiedSuccess"));
      ElMessageBox.alert(t("device.variable.variableModifiedSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        // proTable.value?.getTableList();
        modifiedData.forEach(row => {
          row.isModified = false;
        });

        setTimeout(() => {
          proTable.value?.getTableList();
        }, 2000);
      });
    } else {
      addConsole(t("device.variable.variableModifiedFailed") + response.msg);
      ElMessageBox.alert(t("device.variable.variableModifiedFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.variable.requestFailed"), error);
    ElMessageBox.alert(t("device.variable.requestFailed"), t("device.variable.error"), {
      confirmButtonText: t("device.variable.confirm"),
      type: "error"
    });
  } finally {
    hideLoading();
  }
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "index",
    width: 59,
    label: t("device.variable.sequenceNumber")
  },
  {
    prop: "id",
    isShow: false,
    width: 200,
    label: t("device.variable.id")
  },
  {
    prop: "name",
    width: 350,
    label: t("device.variable.name"),
    search: {
      el: "input",
      tooltip: t("device.variable.enterVariableName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    width: 200,
    label: t("device.variable.description")
  },
  {
    prop: "type",
    width: 200,
    label: t("device.variable.type")
  },
  {
    prop: "addr",
    width: 200,
    isShow: false,
    isSetting: false,
    label: t("device.variable.address")
  },
  {
    prop: "value",
    label: t("device.variable.value"),
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        if (refreshCheck.value) {
          addConsole(t("device.variable.autoRefreshEditForbidden"));
          ElMessageBox.alert(t("device.variable.autoRefreshEditForbidden"), t("device.variable.warningTitle"), { type: "warning" });
          // 恢复原始值
          await nextTick(() => {
            row.value = row.originalValue; // 假设 originalValue 是原始值
          });
          return;
        }
        if (!isValid(value)) {
          addConsole(t("device.variable.invalidNumber", { value }));
          ElMessageBox.alert(t("device.variable.invalidNumber", { value }), t("device.variable.errorTitle"), { type: "error" });
          // 恢复原始值
          await nextTick(() => {
            row.value = row.originalValue; // 假设 originalValue 是原始值
          });
          return;
        }
        console.log("Value changed:", value);
        row.isModified = true; // 设置 isModified 为 true
        row.originalValue = value; // 保存原始值
      };
      return (
        <div>
          <el-input v-model={scope.row.value} onChange={(value: string) => handleChange(value, scope.row)} />
        </div>
      );
    }
  },
  { prop: "operation", label: t("device.variable.operation"), fixed: "right", width: 200 }
]);

const isValid = (value: string) => {
  const trim = value.trim();
  if (trim.length == 0) {
    return false;
  }
  const num = Number(trim);
  return !isNaN(num) && isFinite(num);
};

const handleButtonClick = async (action: string) => {
  switch (action) {
    case "refresh":
      if (proTable.value?.tableData.some(row => row.isModified)) {
        ElMessageBox.confirm(t("device.variable.confirmRefresh"), t("device.variable.warning"), {
          confirmButtonText: t("device.variable.confirm"),
          cancelButtonText: t("device.variable.cancel"),
          type: "warning"
        })
          .then(() => {
            proTable.value?.getTableList();
          })
          .catch(() => {
            // 用户取消刷新
          });
      } else {
        proTable.value?.getTableList();
      }
      break;
    case "add":
      handleAddVariable();
      break;
    case "import":
      handleImport();
      break;
    case "export":
      handleExport();
      break;
    // 其他按钮逻辑...
    default:
      console.log(action);
  }
};
const handleAddVariable = async () => {
  if (!variableName.value.trim()) {
    ElMessage.warning(t("device.variable.pleaseInputVariableName"));
    return;
  }
  showLoading();
  try {
    const response = await variableApi.addVariableByDevice(props.deviceId, { name: variableName.value.trim() });
    console.log("Fetched data:", response);
    if (Number(response.code) === 0) {
      addConsole(t("device.variable.variableAddSuccess"));
      ElMessageBox.alert(t("device.variable.variableAddSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
        setTimeout(() => {
          proTable.value?.getTableList();
        }, 3000);
      });
      variableName.value = "";
    } else {
      ElMessageBox.alert(t("device.variable.variableAddFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
      addConsole(t("device.variable.variableAddFailed") + response.msg);
    }
  } catch (error) {
    console.error(t("device.variable.requestFailed"), error);
    alert(t("device.variable.requestFailed"));
  } finally {
    hideLoading();
  }
};

const handleBatchDelete = async (selectedList: any[]) => {
  showLoading();
  try {
    const selectedItems = selectedList.map(item => item.name);

    const response = await variableApi.deleteVariableByDevice(props.deviceId, selectedItems);
    if (Number(response.code) === 0) {
      addConsole(t("device.variable.variableDeleteSuccess"));
      ElMessageBox.alert(t("device.variable.variableDeleteSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
      variableName.value = "";
    } else {
      ElMessageBox.alert(t("device.variable.variableDeleteFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
      addConsole(t("device.variable.variableDeleteFailed") + response.msg);
    }
  } catch (error) {
    console.error(t("device.variable.requestFailed"), error);
  } finally {
    hideLoading();
  }
};
const handleDelete = async (row: any) => {
  showLoading();
  try {
    const selectedItems = [row.name];

    const response = await variableApi.deleteVariableByDevice(props.deviceId, selectedItems);
    if (Number(response.code) === 0) {
      addConsole(t("device.variable.variableDeleteSuccess"));
      ElMessageBox.alert(t("device.variable.variableDeleteSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
      variableName.value = "";
    } else {
      ElMessageBox.alert(t("device.variable.variableDeleteFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
      addConsole(t("device.variable.variableDeleteFailed") + response.msg);
    }
  } catch (error) {
    console.error(t("device.variable.requestFailed"), error);
  } finally {
    hideLoading();
  }
};
const handleExport = async () => {
  const defaultPath = t("device.variable.defaultExportPath");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.variable.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  console.log("selectPath:", selectPath);
  const path = String(selectPath);
  showLoading();
  try {
    const result = await variableApi.exportVariableByDevice(props.deviceId, {
      path
    });
    if (Number(result.code) === 0) {
      addConsole(t("device.variable.exportSuccess") + path);
      ElMessageBox.alert(t("device.variable.exportSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("device.variable.exportFailed") + result.msg);
      ElMessageBox.alert(t("device.variable.exportFailed") + result.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.variable.exportFailed"));
    ElMessageBox.alert(t("device.variable.exportFailed"), t("device.variable.error"), {
      confirmButtonText: t("device.variable.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    hideLoading();
  }
};

const handleImport = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("device.variable.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });

  if (!selectPath.path) {
    return;
  }

  const path = String(selectPath.path);
  showLoading();
  try {
    const response = await variableApi.importVariableByDevice(props.deviceId, { path });

    if (Number(response.code) === 0) {
      addConsole(t("device.variable.importSuccess"));
      ElMessageBox.alert(t("device.variable.importSuccess"), t("device.variable.success"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "success"
      }).then(() => {
        proTable.value?.getTableList();
      });
    } else {
      addConsole(t("device.variable.importFailed") + response.msg);
      ElMessageBox.alert(t("device.variable.importFailed") + response.msg, t("device.variable.error"), {
        confirmButtonText: t("device.variable.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error(t("device.variable.importFailed"), error);
    ElMessageBox.alert(t("device.variable.importFailed"), t("device.variable.error"), {
      confirmButtonText: t("device.variable.confirm"),
      type: "error"
    });
  } finally {
    // 关闭 loading 实例
    hideLoading();
  }
};

const cellStyle = ({ row }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  if (row.isModified) {
    return { backgroundColor: "var(--el-color-warning-light-9)" }; // 修改时的背景色
  }
  return {};
};

watch(refreshCheck, newValue => {
  if (newValue) {
    if (proTable.value?.tableData.some(row => row.isModified)) {
      ElMessageBox.confirm(t("device.variable.confirmAutoRefresh"), t("device.variable.warning"), {
        confirmButtonText: t("device.variable.confirm"),
        cancelButtonText: t("device.variable.cancel"),
        type: "warning"
      })
        .then(() => {
          console.log(t("device.variable.continueAutoRefresh"));
          startRefreshTimer();
        })
        .catch(() => {
          refreshCheck.value = false;
        });
    } else {
      startRefreshTimer();
    }
  } else {
    stopRefreshTimer();
  }
});

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, paramInfo.VARI_REFRESH_TIME); // 每 5 秒刷新一次
};
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};
onBeforeUnmount(() => {
  stopRefreshTimer();
});
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
</style>
