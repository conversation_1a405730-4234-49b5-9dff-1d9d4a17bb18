# VisualDebug 使用说明文档

---

## 1. 软件简介

**VisualDebug** 是一款可视化平台工程调试工具，集成了可视化工具连接、装置信息查看、设定量、模拟量、状态量、遥信、遥测、遥控、报告、装置对时、定值导入导出、变量调试等多项功能。支持组态工具预览、新增、编辑、自定义图符、装置信息关联、主题定制、IT小工具、装置配置导入导出等，适用于电力自动化、工业控制等领域的工程调试与管理。

## 2. 功能说明

- **可视化工具连接**：支持多种通信协议，快速连接各类工程装置，实现数据采集与交互。
- **装置信息管理**：集中管理所有工程装置的信息，支持查看、编辑、批量导入导出。
- **设定量/模拟量/状态量调试**：对装置的设定量、模拟量、状态量进行实时监控与调试，便于工程调试和故障排查。
- **遥信/遥测/遥控**：支持遥信、遥测数据的实时采集与展示，具备遥控操作能力。
- **报告生成与导出**：自动生成调试报告，支持多种格式导出，便于归档与分析。
- **装置对时**：支持对装置进行统一对时，保证系统时钟一致性。
- **定值导入导出**：支持装置定值参数的批量导入与导出，提升工程效率。
- **变量调试**：支持变量级别的调试与监控，便于开发与维护。
- **组态工具**：内置图形化组态编辑器，支持自定义图符、装置与图形的关联，预览、新增、编辑组态画面。
- **主题定制**：支持多种主题风格切换，满足不同用户的视觉需求。
- **IT小工具**：集成多种工程辅助工具，如数据转换、协议分析等。
- **装置配置导入导出**：支持装置配置文件的批量导入与导出，便于工程迁移和备份。
- **多协议支持**：内置IEC等多种工业通信协议库，适配多类型设备。

## 3. 系统环境与依赖

- **操作系统**：Windows 10/11、macOS、Linux
- **Node.js**：建议 16.x 及以上
- **依赖管理**：npm / pnpm / yarn
- **前端依赖**：Vue3、Vite、Element Plus、AntV X6、ECharts 等
- **后端依赖**：Go（支持多平台编译）、Python（Flask/FastAPI）、Electron

## 4. 安装与启动方法

### 4.1 开发环境启动

1. 安装依赖：
   ```bash
   npm install
   cd frontend && npm install
   ```
2. 启动前端、Electron、后端服务（推荐多终端并行）：
   ```bash
   npm run dev-frontend   # 启动前端（8080端口）
   npm run dev-electron   # 启动Electron桌面端
   npm run dev-go         # 启动Go后端
   npm run dev-python     # 启动Python后端
   ```
3. 一键开发模式（自动启动所有服务）：
   ```bash
   npm run dev
   ```

### 4.2 生产环境打包与启动

1. 构建前端与Electron：
   ```bash
   npm run build
   ```
2. 启动生产环境：
   ```bash
   npm start
   ```
3. 多平台打包（Windows/macOS/Linux）：
   ```bash
   npm run build-w      # Windows 64位
   npm run build-m      # macOS
   npm run build-l      # Linux
   # 其他平台见 package.json scripts
   ```

## 5. 主要界面与功能模块

- **装置信息管理**：查看、编辑、导入导出装置信息
- **实时数据监控**：支持遥信、遥测、遥控、模拟量、状态量等实时数据展示与调试
- **组态工具**：图形化组态编辑、图符自定义、装置关联
- **报告与日志**：生成调试报告，支持日志查询与导出
- **主题与界面定制**：多主题切换，界面自定义
- **IT小工具**：内置多种工程辅助工具
- **多协议支持**：内置IEC等多种通信协议库

## 6. 常见问题与支持

- **依赖安装失败**：请确认Node.js版本与网络环境，必要时更换npm源
- **端口冲突**：可在配置文件中修改默认端口（如8080、7070、7071等）
- **Go/Python后端未启动**：请确保已正确安装Go/Python环境
- **界面显示异常**：建议清理缓存或重装依赖
- **更多问题**：请联系开发团队 <EMAIL>

## 7. 版本与许可证

- 当前版本：1.00.001
- 许可证：Apache License 2.0
- 作者：Sieyuan
- 官网：https://www.sieyuan.com

---

如需更多帮助或定制开发，请联系官方支持邮箱。
