export type RemoteStatus = "sync_check" | "dead_chek" | "no_check" | "close";

export interface SelectRequestData {
  /** 控制源即name */
  ctlObj: string;
  /** 值 */
  ctlVal: string;
  /** 模式 sbo=选控，direct=直控 */
  ctlModel: CtlMode;
  /** 当前时间iso8601格式 */
  opemTm: string;
  test: string;
  check: RemoteStatus;
}

export enum CtlMode {
  /** 选控 */
  SBO = "sbo",
  /** 直控 */
  DIRECT = "direct"
}
export enum CtlCheck {
  /** 不检 */
  NO_CHECK = "no_check",
  /** 检同期 */
  SYNC_CHECK = "sync_check",
  /** 检无压 */
  DEAD_CHECK = "dead_check"
}

