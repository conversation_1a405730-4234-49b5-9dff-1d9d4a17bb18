"use strict";

import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ProjectDictRequestData,
  ProjectDictRequestRes,
  SetProjectDictRequestData,
  SetProjectDictRes,
} from "iec-upadrpc";

/**
 * 词条配置Service
 * <AUTHOR>
 * @class
 */
class DictConfigService {
  // 获取工程词条
  async getProjectDict(
    req: IECReq<ProjectDictRequestData>
  ): Promise<ProjectDictRequestRes> {
    logger.info(
      `[DictConfigService] ${t("logs.dictConfigService.getProjectDictEntry")}:`,
      JSON.stringify(req)
    );
    const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = device?.deviceClient;
    try {
      const response = await client?.getProjectDict(req.data);
      logger.info(
        `[DictConfigService] ${t("logs.dictConfigService.getProjectDictReturn")}:`,
        response
      );
      if (response?.isSuccess()) {
        return response.data;
      }
      throw new Error(String(response?.msg));
    } catch (error) {
      logger.error(
        `[DictConfigService] ${t("logs.dictConfigService.getProjectDictError")}:`,
        error
      );
      throw error;
    }
  }

  // 设置工程词条
  async setProjectDict(
    req: IECReq<SetProjectDictRequestData>
  ): Promise<SetProjectDictRes> {
    logger.info(
      `[DictConfigService] ${t("logs.dictConfigService.setProjectDictEntry")}:`,
      JSON.stringify(req)
    );
    const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = device?.deviceClient;
    try {
      const result = await client?.setProjectDict(req.data);
      logger.info(
        `[DictConfigService] ${t("logs.dictConfigService.setProjectDictReturn")}:`,
        result
      );
      if (result?.isSuccess()) {
        return result.data;
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error(
        `[DictConfigService] ${t("logs.dictConfigService.setProjectDictError")}:`,
        error
      );
      throw error;
    }
  }
}

DictConfigService.toString = () => "[class DictConfigService]";
const dictConfigService = new DictConfigService();

export { DictConfigService, dictConfigService };
