<template>
  <div class="tool-bar-lf">
    <Breadcrumb v-show="globalStore.breadcrumb" id="breadcrumb" />
    <div class="logo">
      <span class="company-name-en">{{ t("layout.header.company.englishName") }}</span>
      <div class="logo-mark">®</div>
      <span class="company-name-cn">&nbsp;{{ t("layout.header.company.name") }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules/global";
import Breadcrumb from "./components/Breadcrumb.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const globalStore = useGlobalStore();
</script>

<style scoped lang="scss">
.tool-bar-lf {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
  .logo {
    display: flex;
    align-items: center;
    margin-left: 20px; // 增大左边距
    font-size: 20px; // 整体字体放大
    .company-name-en {
      font-family: Tahoma;
      font-size: 18px; // 英文名字体放大
      font-weight: 600;
      color: #124198;
      user-select: none;
    }
    .company-name-cn {
      font-family: "黑体";
      font-size: 16px; // 中文名字体放大
      font-weight: 600;
      color: #124198;
      user-select: none;
    }
    .logo-mark {
      top: 1px;
      right: 1px;
      margin: 0 4px;
      font-family: Tahoma;
      font-size: 14px; // logo-mark 字号放大
      font-weight: 600;
      color: #124198;
      user-select: none;
    }
  }
}
</style>
