import { calculateEllipse, calculateTriangle } from "../../graph/GraphUtil";

const e = {
  shape: "0C00",
  markup: [
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 0, 23, 22.5)
      }
    },
    {
      tagName: "ellipse",
      groupSelector: "ellipse",
      attrs: {
        ...calculateEllipse(0, 15.86, 23, 22.5)
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 6.67,
        y1: 25.63,
        x2: 12.33,
        y2: 30.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 16.67,
        y1: 25.33,
        x2: 11.33,
        y2: 30.33
      }
    },
    {
      tagName: "line",
      groupSelector: "line",
      attrs: {
        x1: 11.67,
        y1: 29.75,
        x2: 11.67,
        y2: 35.33
      }
    },
    {
      tagName: "polygon",
      groupSelector: "triangle",
      attrs: {
        points: calculateTriangle(6.67, 4.33, 9.33, 7.33)
      }
    }
  ],
  attrs: {
    line: {
      stroke: "#000"
    },
    ellipse: {
      fill: "transparent",
      stroke: "#000"
    },
    triangle: {
      fill: "transparent",
      stroke: "#000"
    }
  }
};

export default e;
