import { Spa } from "@/api/interface";
import { MenuTypeDictEnum } from "@/enums";

export namespace Menu {
  /** 菜单类型 */
  export type MenuType = MenuTypeDictEnum.MENU | MenuTypeDictEnum.CATALOG | MenuTypeDictEnum.LINK;

  /**菜单分页查询 */
  export interface Tree {
    /** 菜单名称 */
    menuType?: MenuType;
    /** 所属模块名称 */
    module: number | string;
  }

  /** 菜单信息 */
  export interface MenuInfo extends Spa.SpaInfo {
    /** 所属模块 */
    module: number | string;
    /** 上级菜单 */
    parentId: number | string;
    /** 子菜单 */
    children?: MenuInfo[];
  }

  /** 菜单树信息 */
  export interface MenuTreeInfo {
    /** id */
    id: number | string;
    /** 菜单名称 */
    title: string;
    /** 上级菜单Id */
    parentId: number | string;
    /** 菜单路由 */
    path?: string;
    /** 子菜单 */
    children?: MenuTreeInfo[];
  }

  /** 菜单树选择器输入参数 */
  export interface MenuTreeSelectorReq {
    /** 所属模块 */
    module?: number | string;
  }
}
