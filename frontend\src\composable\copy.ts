// eslint-disable-next-line no-restricted-imports
import { useClipboard } from "@vueuse/core";
import { ElMessage } from "element-plus";
import type { MaybeRefOrGetter } from "vue";
import { ComputedRef, Ref } from "vue-demi";

export function useCopy({ source, text, createToast = true }: { source?: MaybeRefOrGetter<string>; text?: string; createToast?: boolean } = {}): {
  isJustCopied: ComputedRef<boolean>;
  isSupported: Ref<boolean>;
  text: ComputedRef<string>;
  copy(content?: string, { notificationMessage }?: { notificationMessage?: string }): Promise<void>;
} {
  const { copy, copied, ...rest } = useClipboard({
    source,
    legacy: true
  });

  return {
    ...rest,
    isJustCopied: copied,
    async copy(content?: string, { notificationMessage }: { notificationMessage?: string } = {}): Promise<any> {
      if (source) {
        await copy();
      } else {
        await copy(content);
      }

      if (createToast) {
        ElMessage.success(notificationMessage ?? text);
      }
    }
  };
}
