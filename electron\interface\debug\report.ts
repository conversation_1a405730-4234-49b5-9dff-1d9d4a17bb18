import {
    EventReportEntry,
    OperateReportEntry
} from "iec-upadrpc/dist/src/data/index";

export interface IECReportExportParam {
  type: string;
  method: string;
  path: string;
  items: EventReportEntry[] | FaultReportEntry[] | OperateReportEntry[];
}

export interface FaultReportEntry {
    id?: number;
    name: string;
    value: string;
    ret_ms: number | string;
    children?: FaultReportEntry[] | undefined;
}

export interface IECRpcReportOpenWaveParam {
    exePath: string;
    filePath: string;
}